import { PrismaClient } from '@prisma/client';
import { Router } from 'express';

const router = Router();
const prisma = new PrismaClient();

// GET /api/animals - Get all animals with filters
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      animalType,
      breed,
      gender,
      category,
      status,
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    const where: any = {};

    if (search) {
      where.OR = [
        { internalId: { contains: search as string } },
        { tagNumber: { contains: search as string } },
      ];
    }

    if (animalType) where.animalTypeId = animalType as string;
    if (breed) where.breedId = breed as string;
    if (gender) where.gender = gender;
    if (category) where.category = category;
    if (status) where.status = status;

    const [animals, total] = await Promise.all([
      prisma.animal.findMany({
        where,
        include: {
          animalType: true,
          breed: true,
          mother: {
            select: { id: true, internalId: true, tagNumber: true },
          },
          father: {
            select: { id: true, internalId: true, tagNumber: true },
          },
        },
        skip,
        take: Number(limit),
        orderBy: { createdAt: 'desc' },
      }),
      prisma.animal.count({ where }),
    ]);

    res.json({
      animals,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    });
  } catch (error) {
    console.error('Error fetching animals:', error);
    res.status(500).json({ error: 'Failed to fetch animals' });
  }
});

// GET /api/animals/:id - Get single animal
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const animal = await prisma.animal.findUnique({
      where: { id },
      include: {
        animalType: true,
        breed: true,
        mother: {
          select: { id: true, internalId: true, tagNumber: true },
        },
        father: {
          select: { id: true, internalId: true, tagNumber: true },
        },
        offspring: {
          select: { id: true, internalId: true, tagNumber: true, gender: true },
        },
        treatments: {
          orderBy: { date: 'desc' },
          take: 10,
        },
        weightRecords: {
          orderBy: { date: 'desc' },
          take: 10,
        },
      },
    });

    if (!animal) {
      return res.status(404).json({ error: 'Animal not found' });
    }

    return res.json(animal);
  } catch (error) {
    console.error('Error fetching animal:', error);
    return res.status(500).json({ error: 'Failed to fetch animal' });
  }
});

// POST /api/animals - Create new animal
router.post('/', async (req, res) => {
  try {
    const animalData = req.body;

    // Generate internal ID if not provided
    if (!animalData.internalId) {
      const count = await prisma.animal.count();
      animalData.internalId = `A${String(count + 1).padStart(6, '0')}`;
    }

    const animal = await prisma.animal.create({
      data: animalData,
      include: {
        animalType: true,
        breed: true,
      },
    });

    res.status(201).json(animal);
  } catch (error) {
    console.error('Error creating animal:', error);
    res.status(500).json({ error: 'Failed to create animal' });
  }
});

// PUT /api/animals/:id - Update animal
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const animal = await prisma.animal.update({
      where: { id },
      data: updateData,
      include: {
        animalType: true,
        breed: true,
      },
    });

    res.json(animal);
  } catch (error) {
    console.error('Error updating animal:', error);
    res.status(500).json({ error: 'Failed to update animal' });
  }
});

// DELETE /api/animals/:id - Delete animal
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.animal.delete({
      where: { id },
    });

    res.status(204).send();
  } catch (error) {
    console.error('Error deleting animal:', error);
    res.status(500).json({ error: 'Failed to delete animal' });
  }
});

// POST /api/animals/:id/weight - Add weight record
router.post('/:id/weight', async (req, res) => {
  try {
    const { id } = req.params;
    const { weight, date, notes } = req.body;

    const weightRecord = await prisma.weightRecord.create({
      data: {
        animalId: id,
        weight: parseFloat(weight),
        date: new Date(date),
        notes,
      },
    });

    // Update current weight in animal record
    await prisma.animal.update({
      where: { id },
      data: { currentWeight: parseFloat(weight) },
    });

    res.status(201).json(weightRecord);
  } catch (error) {
    console.error('Error adding weight record:', error);
    res.status(500).json({ error: 'Failed to add weight record' });
  }
});

export default router;

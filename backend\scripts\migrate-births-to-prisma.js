const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Sample births data that matches the animals we created
const sampleBirths = [
  {
    id: 'birth-1',
    internalId: '2024-B001',
    tagNumber: 'B001',
    motherId: '2', // SH002 (female sheep)
    fatherId: '1', // SH001 (male sheep)
    birthDate: '2024-01-15',
    gender: 'FEMALE',
    birthWeight: 3.5,
    status: 'ALIVE',
    birthType: 'SINGLE',
    barnLocation: 'حظيرة ب',
    notes: 'ولادة طبيعية - نعجة ممتازة',
  },
  {
    id: 'birth-2',
    internalId: '2024-B002',
    tagNumber: 'B002',
    motherId: '2', // SH002 (female sheep)
    fatherId: '1', // SH001 (male sheep)
    birthDate: '2024-02-20',
    gender: 'MALE',
    birthWeight: 4.2,
    status: 'ALIVE',
    birthType: 'TWIN',
    barnLocation: 'حظيرة ب',
    notes: 'توأم - الأول، خروف قوي',
  },
  {
    id: 'birth-3',
    internalId: '2024-B003',
    tagNumber: 'B003',
    motherId: '3', // GT001 (female goat)
    fatherId: '4', // GT002 (male goat)
    birthDate: '2024-03-10',
    gender: 'FEMALE',
    birthWeight: 2.8,
    status: 'ALIVE',
    birthType: 'SINGLE',
    barnLocation: 'حظيرة أ',
    notes: 'عنزة صغيرة نشطة',
  },
];

async function migrateBirths() {
  try {
    console.log('🚀 بدء نقل بيانات المواليد إلى Prisma...');

    // Clear existing births and reproduction cycles
    console.log('🗑️ حذف المواليد والدورات الإنتاجية الموجودة...');
    await prisma.birth.deleteMany();
    await prisma.reproductionCycle.deleteMany();

    // Create reproduction cycles first
    console.log('📝 إنشاء الدورات الإنتاجية...');
    const reproductionCycles = [];

    for (let i = 0; i < sampleBirths.length; i++) {
      const birth = sampleBirths[i];
      const cycleId = `cycle-${i + 1}`;

      // Get animal type ID based on mother
      const animalTypeId =
        birth.motherId === '2' ||
        birth.motherId === '1' ||
        birth.motherId === '5' ||
        birth.motherId === '6'
          ? '1'
          : '2'; // 1 for sheep, 2 for goat

      const cycle = await prisma.reproductionCycle.create({
        data: {
          id: cycleId,
          cycleNumber: i + 1, // Sequential cycle number
          animalTypeId: animalTypeId,
          motherId: birth.motherId,
          fatherId: birth.fatherId,
          pregnancyStart: new Date(
            new Date(birth.birthDate).getTime() - 150 * 24 * 60 * 60 * 1000
          ), // 150 days before birth
          status: 'COMPLETED',
          notes: `دورة إنتاجية للمولود ${birth.tagNumber}`,
        },
      });

      reproductionCycles.push({ ...cycle, originalBirth: birth });
    }

    // Create births
    console.log('📝 إنشاء المواليد...');
    for (let i = 0; i < reproductionCycles.length; i++) {
      const cycle = reproductionCycles[i];
      const birth = cycle.originalBirth;

      await prisma.birth.create({
        data: {
          id: birth.id,
          reproductionCycleId: cycle.id,
          motherId: birth.motherId,
          fatherId: birth.fatherId,
          birthDate: new Date(birth.birthDate),
          gender: birth.gender,
          birthWeight: birth.birthWeight,
          status: birth.status,
          birthType: birth.birthType,
          notes: birth.notes,
        },
      });
    }

    console.log('✅ تم نقل بيانات المواليد بنجاح!');
    console.log(`📊 تم إنشاء ${sampleBirths.length} مولود`);
  } catch (error) {
    console.error('❌ خطأ في نقل بيانات المواليد:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateBirths();

import {
  AccountBalance as Account<PERSON>alanceIcon,
  Pets as PetsIcon,
  TrendingDown as TrendingDownIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Paper,
  Typography,
  useTheme,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();

  // State for real data
  const [stats, setStats] = useState({
    totalAnimals: 0,
    totalSheep: 0,
    totalGoats: 0,
    pregnantAnimals: 0,
    newborns: 0,
    monthlyRevenue: 0,
    monthlyExpenses: 0,
    netProfit: 0,
  });

  // Load real data from localStorage
  useEffect(() => {
    try {
      // Load animals data
      const savedAnimals = localStorage.getItem('animals');
      const animals = savedAnimals ? JSON.parse(savedAnimals) : [];

      // Load births data
      const savedBirths = localStorage.getItem('births');
      const births = savedBirths ? JSON.parse(savedBirths) : [];

      // Load purchases/expenses data
      const savedPurchasesExpenses = localStorage.getItem('purchasesExpenses');
      const purchasesExpenses = savedPurchasesExpenses
        ? JSON.parse(savedPurchasesExpenses)
        : [];

      // Calculate statistics
      const totalAnimals = animals.length;
      const totalSheep = animals.filter(
        (animal: any) => animal.type === 'أغنام'
      ).length;
      const totalGoats = animals.filter(
        (animal: any) => animal.type === 'ماعز'
      ).length;
      const pregnantAnimals = animals.filter(
        (animal: any) => animal.status === 'حامل'
      ).length;

      // Calculate births in current month
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const newborns = births.filter((birth: any) => {
        const birthDate = new Date(birth.birthDate);
        return (
          birthDate.getMonth() === currentMonth &&
          birthDate.getFullYear() === currentYear
        );
      }).length;

      // Calculate financial data
      const currentMonthExpenses = purchasesExpenses
        .filter((item: any) => {
          const itemDate = new Date(item.date);
          return (
            itemDate.getMonth() === currentMonth &&
            itemDate.getFullYear() === currentYear
          );
        })
        .reduce((sum: number, item: any) => sum + item.amount, 0);

      // Mock revenue for now (can be calculated from sales data when available)
      const monthlyRevenue = currentMonthExpenses * 1.6; // Assuming 60% profit margin
      const netProfit = monthlyRevenue - currentMonthExpenses;

      setStats({
        totalAnimals,
        totalSheep,
        totalGoats,
        pregnantAnimals,
        newborns,
        monthlyRevenue,
        monthlyExpenses: currentMonthExpenses,
        netProfit,
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Fallback to default values
      setStats({
        totalAnimals: 0,
        totalSheep: 0,
        totalGoats: 0,
        pregnantAnimals: 0,
        newborns: 0,
        monthlyRevenue: 0,
        monthlyExpenses: 0,
        netProfit: 0,
      });
    }
  }, []);

  const monthlyData = [
    { month: 'يناير', sheep: 170, goats: 60, revenue: 42000, expenses: 25000 },
    { month: 'فبراير', sheep: 175, goats: 62, revenue: 44000, expenses: 26000 },
    { month: 'مارس', sheep: 180, goats: 65, revenue: 45000, expenses: 28000 },
  ];

  const animalDistribution = [
    { name: t('sheep'), value: stats.totalSheep, color: '#2E7D32' },
    { name: t('goats'), value: stats.totalGoats, color: '#FF8F00' },
  ];

  const StatCard = ({
    title,
    value,
    icon,
    color = 'primary',
    trend,
    isCurrency = false,
  }: {
    title: string;
    value: number;
    icon: React.ReactNode;
    color?: 'primary' | 'secondary' | 'success' | 'error';
    trend?: 'up' | 'down';
    isCurrency?: boolean;
  }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              p: 1,
              borderRadius: 1,
              backgroundColor: theme.palette[color].main + '20',
              color: theme.palette[color].main,
              mr: 2,
            }}
          >
            {icon}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h4" component="div" fontWeight="bold">
              {isCurrency
                ? `${value.toLocaleString()} ر.س`
                : value.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
          {trend && (
            <Box sx={{ color: trend === 'up' ? 'success.main' : 'error.main' }}>
              {trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        {t('dashboard')}
      </Typography>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('totalAnimals')}
            value={stats.totalAnimals}
            icon={<PetsIcon />}
            color="primary"
            trend="up"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('pregnantAnimals')}
            value={stats.pregnantAnimals}
            icon={<PetsIcon />}
            color="secondary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('monthlyRevenue')}
            value={stats.monthlyRevenue}
            icon={<AccountBalanceIcon />}
            color="success"
            trend="up"
            isCurrency
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('netProfit')}
            value={stats.netProfit}
            icon={<TrendingUpIcon />}
            color="success"
            trend="up"
            isCurrency
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Monthly Trends */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              الاتجاهات الشهرية
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="sheep" fill="#2E7D32" name={t('sheep')} />
                <Bar dataKey="goats" fill="#FF8F00" name={t('goats')} />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Animal Distribution */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              توزيع الحيوانات
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={animalDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {animalDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;

import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import DropdownManager from '../components/DropdownManager';
import { useThemeStore } from '../store/themeStore';
import {
  getFilterSectionProps,
  getTableContainerProps,
} from '../utils/pageColors';

// Define interfaces locally
interface Birth {
  id: string;
  registrationNumber: string;
  tagNumber?: string;
  motherRegistrationNumber: string;
  motherTagNumber: string;
  fatherTagNumber?: string;
  birthDate: string;
  gender: 'male' | 'female';
  birthWeight: number;
  type: 'sheep' | 'goat';
  breed: string;
  status: 'present' | 'sold' | 'dead';
  birthType: 'single' | 'twin';
  twinCount?: number;
  barnLocation?: string;
  notes?: string;
}

const Births: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { isDarkMode } = useThemeStore();
  const [births, setBirths] = useState<Birth[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // States for dropdown management
  const [birthTypes, setBirthTypes] = useState([
    {
      id: '1',
      name: 'ولادة طبيعية',
      nameEn: 'Natural Birth',
      icon: '🐣',
      active: true,
    },
    {
      id: '2',
      name: 'ولادة قيصرية',
      nameEn: 'C-Section',
      icon: '🏥',
      active: true,
    },
    {
      id: '3',
      name: 'ولادة مساعدة',
      nameEn: 'Assisted Birth',
      icon: '👨‍⚕️',
      active: true,
    },
  ]);

  const [birthStatuses, setBirthStatuses] = useState([
    { id: '1', name: 'حي', nameEn: 'Alive', icon: '✅', active: true },
    { id: '2', name: 'ميت', nameEn: 'Dead', icon: '❌', active: true },
    { id: '3', name: 'ضعيف', nameEn: 'Weak', icon: '⚠️', active: true },
  ]);

  const [complications, setComplications] = useState([
    {
      id: '1',
      name: 'لا توجد مضاعفات',
      nameEn: 'No Complications',
      icon: '✅',
      active: true,
    },
    { id: '2', name: 'نزيف', nameEn: 'Bleeding', icon: '🩸', active: true },
    { id: '3', name: 'التهاب', nameEn: 'Infection', icon: '🦠', active: true },
    {
      id: '4',
      name: 'صعوبة في الولادة',
      nameEn: 'Difficult Birth',
      icon: '⚠️',
      active: true,
    },
  ]);

  const [seasons, setSeasons] = useState([
    { id: '1', name: 'الربيع', nameEn: 'Spring', icon: '🌸', active: true },
    { id: '2', name: 'الصيف', nameEn: 'Summer', icon: '☀️', active: true },
    { id: '3', name: 'الخريف', nameEn: 'Autumn', icon: '🍂', active: true },
    { id: '4', name: 'الشتاء', nameEn: 'Winter', icon: '❄️', active: true },
  ]);

  const [dropdownSnackbar, setDropdownSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  // Filter states
  const [filterType, setFilterType] = useState('');
  const [filterBreed, setFilterBreed] = useState('');
  const [filterGender, setFilterGender] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterBarn, setFilterBarn] = useState('');

  // Load animal types and breeds from settings
  const [animalTypes, setAnimalTypes] = useState<any[]>([]);
  const [breeds, setBreeds] = useState<any[]>([]);
  const [barns, setBarns] = useState<any[]>([]);
  const [mothers, setMothers] = useState<any[]>([]);

  // Dialog states
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [selectedBirth, setSelectedBirth] = useState<Birth | null>(null);

  // Form states
  const [editFormData, setEditFormData] = useState<Partial<Birth>>({});
  const [addFormData, setAddFormData] = useState<Partial<Birth>>({
    type: 'sheep',
    breed: '',
    gender: 'male',
    birthType: 'single',
    status: 'present',
    birthWeight: 0,
  });

  // Sample data for demonstration
  const getSampleBirths = (): Birth[] => [
    {
      id: '1',
      registrationNumber: '2024-1001',
      tagNumber: 'B001',
      motherRegistrationNumber: '2024-0002',
      motherTagNumber: 'SH002',
      fatherTagNumber: 'SH001',
      birthDate: '2024-01-15',
      gender: 'female',
      birthWeight: 3.5,
      type: 'sheep',
      breed: 'حري',
      status: 'present',
      birthType: 'single',
      barnLocation: 'حظيرة ب',
      notes: 'ولادة طبيعية - نعجة ممتازة',
    },
    {
      id: '2',
      registrationNumber: '2024-1002',
      tagNumber: 'B002',
      motherRegistrationNumber: '2024-0002',
      motherTagNumber: 'SH002',
      fatherTagNumber: 'SH001',
      birthDate: '2023-03-20',
      gender: 'male',
      birthWeight: 4.2,
      type: 'sheep',
      breed: 'حري',
      status: 'sold',
      birthType: 'twin',
      twinCount: 2,
      barnLocation: 'حظيرة ب',
      notes: 'توأم - الأول، تم بيعه',
    },
    {
      id: '3',
      registrationNumber: '2024-1003',
      tagNumber: 'B003',
      motherRegistrationNumber: '2024-0002',
      motherTagNumber: 'SH002',
      fatherTagNumber: 'SH001',
      birthDate: '2023-03-20',
      gender: 'female',
      birthWeight: 3.8,
      type: 'sheep',
      breed: 'حري',
      status: 'present',
      birthType: 'twin',
      twinCount: 2,
      barnLocation: 'حظيرة ب',
      notes: 'توأم - الثانية، نعجة منتجة',
    },
    {
      id: '4',
      registrationNumber: '2024-1004',
      tagNumber: 'B004',
      motherRegistrationNumber: '2024-0004',
      motherTagNumber: 'GT002',
      fatherTagNumber: 'GT001',
      birthDate: '2024-02-10',
      gender: 'male',
      birthWeight: 2.8,
      type: 'goat',
      breed: 'بلدي',
      status: 'present',
      birthType: 'twin',
      twinCount: 2,
      barnLocation: 'حظيرة أ',
      notes: 'توأم - الأول، تيس قوي',
    },
    {
      id: '5',
      registrationNumber: '2024-1005',
      tagNumber: 'B005',
      motherRegistrationNumber: '2024-0004',
      motherTagNumber: 'GT002',
      fatherTagNumber: 'GT001',
      birthDate: '2024-02-10',
      gender: 'female',
      birthWeight: 2.5,
      type: 'goat',
      breed: 'بلدي',
      status: 'present',
      birthType: 'twin',
      twinCount: 2,
      barnLocation: 'حظيرة أ',
      notes: 'توأم - الثانية، عنزة نشطة',
    },
    {
      id: '6',
      registrationNumber: '2024-1006',
      tagNumber: 'B006',
      motherRegistrationNumber: '2024-0004',
      motherTagNumber: 'GT002',
      birthDate: '2023-05-15',
      gender: 'male',
      birthWeight: 3.2,
      type: 'goat',
      breed: 'بلدي',
      status: 'dead',
      birthType: 'single',
      barnLocation: 'حظيرة أ',
      notes: 'فُقد بعد 6 أشهر',
    },
    {
      id: '7',
      registrationNumber: '2024-1007',
      tagNumber: 'B007',
      motherRegistrationNumber: '2024-0002',
      motherTagNumber: 'SH002',
      birthDate: '2022-04-10',
      gender: 'female',
      birthWeight: 3.0,
      type: 'sheep',
      breed: 'حري',
      status: 'sold',
      birthType: 'single',
      barnLocation: 'حظيرة ب',
      notes: 'مولود العام الماضي - تم بيعها',
    },
    {
      id: '8',
      registrationNumber: '2024-1008',
      tagNumber: 'B008',
      motherRegistrationNumber: '2024-0004',
      motherTagNumber: 'GT002',
      birthDate: '2022-12-05',
      gender: 'male',
      birthWeight: 2.9,
      type: 'goat',
      breed: 'بلدي',
      status: 'sold',
      birthType: 'single',
      barnLocation: 'حظيرة أ',
      notes: 'تيس ممتاز - تم بيعه للتربية',
    },
    {
      id: '9',
      registrationNumber: '2024-1009',
      tagNumber: 'B009',
      motherRegistrationNumber: '2024-0002',
      motherTagNumber: 'SH002',
      birthDate: '2024-11-01',
      gender: 'male',
      birthWeight: 4.0,
      type: 'sheep',
      breed: 'حري',
      status: 'present',
      birthType: 'single',
      barnLocation: 'حظيرة ب',
      notes: 'مولود حديث - خروف قوي',
    },
    {
      id: '10',
      registrationNumber: '2024-1010',
      tagNumber: 'B010',
      motherRegistrationNumber: '2024-0004',
      motherTagNumber: 'GT002',
      birthDate: '2024-10-20',
      gender: 'female',
      birthWeight: 2.7,
      type: 'goat',
      breed: 'بلدي',
      status: 'present',
      birthType: 'single',
      barnLocation: 'حظيرة أ',
      notes: 'عنزة صغيرة نشطة',
    },
    {
      id: '11',
      registrationNumber: '2024-1011',
      motherRegistrationNumber: '2024-0002',
      motherTagNumber: 'SH002',
      birthDate: '2024-06-15',
      gender: 'male',
      birthWeight: 2.0,
      type: 'sheep',
      breed: 'حري',
      status: 'dead',
      birthType: 'single',
      barnLocation: 'حظيرة ب',
      notes: 'نفق عند الولادة - ولادة صعبة',
    },
    {
      id: '12',
      registrationNumber: '2024-1012',
      motherRegistrationNumber: '2024-0004',
      motherTagNumber: 'GT002',
      birthDate: '2024-08-10',
      gender: 'female',
      birthWeight: 1.8,
      type: 'goat',
      breed: 'بلدي',
      status: 'dead',
      birthType: 'twin',
      twinCount: 3,
      barnLocation: 'حظيرة أ',
      notes: 'ثلاثة توائم - نفق واحد منهم',
    },
  ];

  useEffect(() => {
    // Load animals first to validate mothers exist
    const loadData = async () => {
      try {
        // Load animal types and breeds from settings
        const savedAnimalTypes = localStorage.getItem('animalTypes');
        if (savedAnimalTypes) {
          const parsedAnimalTypes = JSON.parse(savedAnimalTypes);
          console.log('Births - Loaded animal types:', parsedAnimalTypes);
          setAnimalTypes(parsedAnimalTypes);
        } else {
          console.log('Births - No animal types found in localStorage');
          // Set default animal types if none exist
          const defaultAnimalTypes = [
            { id: '1', name: 'أغنام', nameEn: 'Sheep', active: true },
            { id: '2', name: 'ماعز', nameEn: 'Goats', active: true },
          ];
          setAnimalTypes(defaultAnimalTypes);
          localStorage.setItem(
            'animalTypes',
            JSON.stringify(defaultAnimalTypes)
          );
        }

        const savedBreeds = localStorage.getItem('breeds');
        if (savedBreeds) {
          const parsedBreeds = JSON.parse(savedBreeds);
          console.log('Births - Loaded breeds:', parsedBreeds);
          setBreeds(parsedBreeds);
        } else {
          console.log('Births - No breeds found in localStorage');
          // Set default breeds if none exist
          const defaultBreeds = [
            {
              id: '1',
              name: 'حري',
              nameEn: 'Harri',
              type: 'sheep',
              active: true,
            },
            {
              id: '2',
              name: 'نجدي',
              nameEn: 'Najdi',
              type: 'sheep',
              active: true,
            },
            {
              id: '3',
              name: 'عواسي',
              nameEn: 'Awassi',
              type: 'sheep',
              active: true,
            },
            {
              id: '4',
              name: 'بلدي',
              nameEn: 'Baladi',
              type: 'goat',
              active: true,
            },
            {
              id: '5',
              name: 'شامي',
              nameEn: 'Shami',
              type: 'goat',
              active: true,
            },
            {
              id: '6',
              name: 'العارضي',
              nameEn: 'Aradi',
              type: 'goat',
              active: true,
            },
          ];
          setBreeds(defaultBreeds);
          localStorage.setItem('breeds', JSON.stringify(defaultBreeds));
        }

        // Load barns from settings
        const savedBarns = localStorage.getItem('barns');
        if (savedBarns) {
          const parsedBarns = JSON.parse(savedBarns);
          console.log('Births - Loaded barns:', parsedBarns);
          setBarns(parsedBarns);
        } else {
          console.log('Births - No barns found in localStorage');
          // Set default barns if none exist
          const defaultBarns = [
            {
              id: '1',
              name: 'حظيرة أ',
              nameEn: 'Barn A',
              capacity: 50,
              type: 'general',
              location: 'الجانب الشرقي',
              active: true,
            },
            {
              id: '2',
              name: 'حظيرة ب',
              nameEn: 'Barn B',
              capacity: 30,
              type: 'maternity',
              location: 'الجانب الغربي',
              active: true,
            },
            {
              id: '3',
              name: 'حظيرة الحمل',
              nameEn: 'Pregnancy Barn',
              capacity: 25,
              type: 'pregnancy',
              location: 'الجانب الشمالي',
              active: true,
            },
            {
              id: '4',
              name: 'حظيرة العزل',
              nameEn: 'Isolation Barn',
              capacity: 10,
              type: 'isolation',
              location: 'منطقة منفصلة',
              active: true,
            },
          ];
          setBarns(defaultBarns);
          localStorage.setItem('barns', JSON.stringify(defaultBarns));
        }

        // Load animals from API to get available mothers
        const animalsResponse = await apiService.getAnimals();
        let availableMothers: any[] = [];

        if (animalsResponse && animalsResponse.animals) {
          availableMothers = animalsResponse.animals.filter(
            (animal: any) => animal.gender === 'FEMALE'
          );

          // Transform API data to match local format for mothers
          const transformedMothers = availableMothers.map((animal: any) => ({
            id: animal.id,
            registrationNumber: animal.internalId,
            tagNumber: animal.tagNumber,
            type: animal.animalType?.name === 'أغنام' ? 'sheep' : 'goat',
            breed: animal.breed?.name || animal.breed?.nameAr || 'غير محدد',
            gender: 'female',
            barnLocation: animal.barnLocation || '',
          }));

          setMothers(transformedMothers);
        }

        // Load births from API
        const birthsResponse = await apiService.getBirths();
        if (birthsResponse && birthsResponse.births) {
          // Transform API data to match local format
          const transformedBirths = birthsResponse.births.map((birth: any) => ({
            id: birth.id,
            registrationNumber: birth.reproductionCycle?.id || `RC-${birth.id}`,
            tagNumber: `B${birth.id.slice(-3)}`,
            motherRegistrationNumber:
              birth.mother?.internalId || birth.motherId,
            motherTagNumber: birth.mother?.tagNumber || 'Unknown',
            fatherTagNumber: birth.father?.tagNumber || 'Unknown',
            birthDate: birth.birthDate.split('T')[0],
            gender: birth.gender.toLowerCase(),
            birthWeight: birth.birthWeight || 0,
            type: birth.mother?.animalType?.name === 'أغنام' ? 'sheep' : 'goat',
            breed:
              birth.mother?.breed?.name ||
              birth.mother?.breed?.nameAr ||
              'غير محدد',
            status:
              birth.status === 'ALIVE'
                ? 'present'
                : birth.status === 'DEAD'
                ? 'dead'
                : 'present',
            birthType: birth.birthType.toLowerCase(),
            twinCount: birth.siblingCount || 1,
            barnLocation: birth.mother?.barnLocation || 'غير محدد',
            notes: birth.notes || '',
          }));

          setBirths(transformedBirths);
        } else {
          setBirths([]);
        }
      } catch (error) {
        console.error('Error loading birth data:', error);
      }
    };

    loadData();

    // Load dropdown data from localStorage
    const savedBirthTypes = localStorage.getItem('birthTypes');
    if (savedBirthTypes) {
      setBirthTypes(JSON.parse(savedBirthTypes));
    } else {
      // إضافة بيانات افتراضية لأنواع الولادة
      const defaultBirthTypes = [
        {
          id: '1',
          name: 'ولادة طبيعية',
          nameEn: 'Natural Birth',
          icon: '🐣',
          active: true,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'ولادة قيصرية',
          nameEn: 'C-Section',
          icon: '🏥',
          active: true,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'ولادة مساعدة',
          nameEn: 'Assisted Birth',
          icon: '👨‍⚕️',
          active: true,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'ولادة مبكرة',
          nameEn: 'Premature Birth',
          icon: '⏰',
          active: true,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setBirthTypes(defaultBirthTypes);
      localStorage.setItem('birthTypes', JSON.stringify(defaultBirthTypes));
    }

    const savedBirthStatuses = localStorage.getItem('birthStatuses');
    if (savedBirthStatuses) {
      setBirthStatuses(JSON.parse(savedBirthStatuses));
    } else {
      // إضافة بيانات افتراضية لحالات الولادة
      const defaultBirthStatuses = [
        {
          id: '1',
          name: 'موجود',
          nameEn: 'Present',
          icon: '✅',
          active: true,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'مباع',
          nameEn: 'Sold',
          icon: '💰',
          active: true,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'نافق',
          nameEn: 'Dead',
          icon: '💔',
          active: true,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'مفقود',
          nameEn: 'Missing',
          icon: '❓',
          active: true,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setBirthStatuses(defaultBirthStatuses);
      localStorage.setItem(
        'birthStatuses',
        JSON.stringify(defaultBirthStatuses)
      );
    }

    const savedComplications = localStorage.getItem('birthComplications');
    if (savedComplications) {
      setComplications(JSON.parse(savedComplications));
    } else {
      // إضافة بيانات افتراضية للمضاعفات
      const defaultComplications = [
        {
          id: '1',
          name: 'لا توجد مضاعفات',
          nameEn: 'No Complications',
          icon: '✅',
          active: true,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'صعوبة في الولادة',
          nameEn: 'Difficult Birth',
          icon: '⚠️',
          active: true,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'نزيف',
          nameEn: 'Bleeding',
          icon: '🩸',
          active: true,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'التهاب',
          nameEn: 'Infection',
          icon: '🦠',
          active: true,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setComplications(defaultComplications);
      localStorage.setItem(
        'birthComplications',
        JSON.stringify(defaultComplications)
      );
    }

    const savedSeasons = localStorage.getItem('birthSeasons');
    if (savedSeasons) {
      setSeasons(JSON.parse(savedSeasons));
    } else {
      // إضافة بيانات افتراضية للمواسم
      const defaultSeasons = [
        {
          id: '1',
          name: 'الربيع',
          nameEn: 'Spring',
          icon: '🌸',
          active: true,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'الصيف',
          nameEn: 'Summer',
          icon: '☀️',
          active: true,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'الخريف',
          nameEn: 'Autumn',
          icon: '🍂',
          active: true,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'الشتاء',
          nameEn: 'Winter',
          icon: '❄️',
          active: true,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setSeasons(defaultSeasons);
      localStorage.setItem('birthSeasons', JSON.stringify(defaultSeasons));
    }
  }, []);

  // Save functions for dropdown management
  const saveBirthTypes = (newTypes: any[]) => {
    setBirthTypes(newTypes);
    localStorage.setItem('birthTypes', JSON.stringify(newTypes));
    setDropdownSnackbar({
      open: true,
      message: 'تم حفظ أنواع الولادة بنجاح',
      severity: 'success',
    });
  };

  const saveBirthStatuses = (newStatuses: any[]) => {
    setBirthStatuses(newStatuses);
    localStorage.setItem('birthStatuses', JSON.stringify(newStatuses));
    setDropdownSnackbar({
      open: true,
      message: 'تم حفظ حالات المواليد بنجاح',
      severity: 'success',
    });
  };

  const saveComplications = (newComplications: any[]) => {
    setComplications(newComplications);
    localStorage.setItem(
      'birthComplications',
      JSON.stringify(newComplications)
    );
    setDropdownSnackbar({
      open: true,
      message: 'تم حفظ المضاعفات بنجاح',
      severity: 'success',
    });
  };

  const saveSeasons = (newSeasons: any[]) => {
    setSeasons(newSeasons);
    localStorage.setItem('birthSeasons', JSON.stringify(newSeasons));
    setDropdownSnackbar({
      open: true,
      message: 'تم حفظ المواسم بنجاح',
      severity: 'success',
    });
  };

  // Helper functions (moved to after data loading)

  const calculateAge = (birthDate: string) => {
    const birth = new Date(birthDate);
    const today = new Date();

    let years = today.getFullYear() - birth.getFullYear();
    let months = today.getMonth() - birth.getMonth();
    let days = today.getDate() - birth.getDate();

    if (days < 0) {
      months--;
      days += new Date(today.getFullYear(), today.getMonth(), 0).getDate();
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    return `${years.toString().padStart(2, '0')}-${months
      .toString()
      .padStart(2, '0')}-${days.toString().padStart(2, '0')}`;
  };

  // Function to get available mothers from API data
  const getAvailableMothers = () => {
    // Use the mothers data loaded from API in useEffect
    return mothers;
  };

  // Get breeds by type
  const getBreedsByType = (type: string) => {
    // Convert type to match the breeds data structure
    let targetType = type;
    if (type === 'sheep') targetType = 'sheep';
    if (type === 'goat') targetType = 'goat';

    return breeds.filter((breed) => breed.active && breed.type === targetType);
  };

  // Get animal type value for form
  const getAnimalTypeValue = (animalType: any) => {
    // Convert from settings format to form format
    const nameEn = animalType.nameEn.toLowerCase();
    if (nameEn === 'sheep') return 'sheep';
    if (nameEn === 'goats' || nameEn === 'goat') return 'goat';
    return nameEn;
  };

  // Get type label from type value
  const getTypeLabel = (type: string) => {
    const animalType = animalTypes.find((t) => getAnimalTypeValue(t) === type);
    if (animalType) return animalType.name;

    // Fallback labels
    if (type === 'sheep') return 'أغنام';
    if (type === 'goat') return 'ماعز';
    return type;
  };

  // Get active barns
  const getActiveBarns = () => {
    return barns.filter((barn) => barn.active);
  };

  // Get barn name by barn name (for display)
  const getBarnDisplayName = (barnName: string) => {
    const barn = barns.find((b) => b.name === barnName);
    return barn ? barn.name : barnName;
  };

  // Get gender label
  const getGenderLabel = (gender: string) => {
    return gender === 'male' ? 'ذكر' : 'أنثى';
  };

  // Get status label and color
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'present':
        return 'موجود';
      case 'sold':
        return 'مباع';
      case 'dead':
        return 'نافق';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'success';
      case 'sold':
        return 'warning';
      case 'dead':
        return 'error';
      default:
        return 'default';
    }
  };

  // Filter births based on search term and filters
  const filteredBirths = births.filter((birth) => {
    // Search filter
    const matchesSearch =
      searchTerm === '' ||
      birth.registrationNumber
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (birth.tagNumber &&
        birth.tagNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
      birth.motherTagNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      birth.breed.includes(searchTerm) ||
      (birth.barnLocation &&
        birth.barnLocation.toLowerCase().includes(searchTerm.toLowerCase()));

    // Type filter
    const matchesType = filterType === '' || birth.type === filterType;

    // Breed filter
    const matchesBreed = filterBreed === '' || birth.breed === filterBreed;

    // Gender filter
    const matchesGender = filterGender === '' || birth.gender === filterGender;

    // Status filter
    const matchesStatus = filterStatus === '' || birth.status === filterStatus;

    // Barn filter
    const matchesBarn = filterBarn === '' || birth.barnLocation === filterBarn;

    return (
      matchesSearch &&
      matchesType &&
      matchesBreed &&
      matchesGender &&
      matchesStatus &&
      matchesBarn
    );
  });

  // Filter reset function
  const handleResetFilters = () => {
    setFilterType('');
    setFilterBreed('');
    setFilterGender('');
    setFilterStatus('');
    setFilterBarn('');
    setSearchTerm('');
  };

  // Get unique values for filter options
  const getUniqueBreeds = () => {
    const breeds = [...new Set(births.map((birth) => birth.breed))];
    return breeds.sort();
  };

  return (
    <Box sx={{ p: 3, maxWidth: '100%', mx: 'auto' }}>
      {/* Page Title */}
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        🍼 إدارة المواليد
      </Typography>

      {/* Dropdown Management Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          إدارة القوائم المنسدلة - المواليد
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="أنواع الولادة"
              items={birthTypes}
              onSave={saveBirthTypes}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="حالات المواليد"
              items={birthStatuses}
              onSave={saveBirthStatuses}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="المضاعفات"
              items={complications}
              onSave={saveComplications}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="المواسم"
              items={seasons}
              onSave={saveSeasons}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Search Bar */}
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 3 }}>
        <TextField
          placeholder="البحث في المواليد (رقم التاغ، رقم التسجيل، الأم، السلالة)..."
          variant="outlined"
          size="small"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ flexGrow: 1 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setAddDialogOpen(true)}
          disabled={getAvailableMothers().length === 0}
          sx={{
            backgroundColor: 'primary.main',
            '&:hover': { backgroundColor: 'primary.dark' },
          }}
        >
          إضافة مولود
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'primary.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'primary.contrastText' }}
            >
              {births.length}
            </Typography>
            <Typography
              variant="caption"
              sx={{ color: 'primary.contrastText' }}
            >
              إجمالي المواليد
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'success.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'success.contrastText' }}
            >
              {births.filter((b) => b.status === 'present').length}
            </Typography>
            <Typography
              variant="caption"
              sx={{ color: 'success.contrastText' }}
            >
              ✅ موجود
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'warning.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'warning.contrastText' }}
            >
              {births.filter((b) => b.status === 'sold').length}
            </Typography>
            <Typography
              variant="caption"
              sx={{ color: 'warning.contrastText' }}
            >
              💰 مباع
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'error.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'error.contrastText' }}
            >
              {births.filter((b) => b.status === 'dead').length}
            </Typography>
            <Typography variant="caption" sx={{ color: 'error.contrastText' }}>
              💔 نافق
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Filters */}
      <Box
        {...getFilterSectionProps(isDarkMode)}
        sx={{
          ...getFilterSectionProps(isDarkMode).sx,
          display: 'flex',
          gap: 2,
          mb: 3,
          flexWrap: 'wrap',
          alignItems: 'center',
        }}
      >
        {/* Type Filter */}
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>النوع</InputLabel>
          <Select
            value={filterType}
            label="النوع"
            onChange={(e) => setFilterType(e.target.value)}
          >
            <MenuItem value="">الكل</MenuItem>
            {animalTypes
              .filter((type) => type.active)
              .map((type) => (
                <MenuItem key={type.id} value={getAnimalTypeValue(type)}>
                  {type.nameEn.toLowerCase() === 'sheep' ? '🐑' : '🐐'}{' '}
                  {type.name}
                </MenuItem>
              ))}
          </Select>
        </FormControl>

        {/* Breed Filter */}
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>السلالة</InputLabel>
          <Select
            value={filterBreed}
            label="السلالة"
            onChange={(e) => setFilterBreed(e.target.value)}
          >
            <MenuItem value="">الكل</MenuItem>
            {getUniqueBreeds().map((breed) => (
              <MenuItem key={breed} value={breed}>
                {breed}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Gender Filter */}
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>الجنس</InputLabel>
          <Select
            value={filterGender}
            label="الجنس"
            onChange={(e) => setFilterGender(e.target.value)}
          >
            <MenuItem value="">الكل</MenuItem>
            <MenuItem value="male">ذكر</MenuItem>
            <MenuItem value="female">أنثى</MenuItem>
          </Select>
        </FormControl>

        {/* Status Filter */}
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>الحالة</InputLabel>
          <Select
            value={filterStatus}
            label="الحالة"
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <MenuItem value="">الكل</MenuItem>
            <MenuItem value="present">موجود</MenuItem>
            <MenuItem value="sold">مباع</MenuItem>
            <MenuItem value="dead">نافق</MenuItem>
          </Select>
        </FormControl>

        {/* Barn Filter */}
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>الحظيرة</InputLabel>
          <Select
            value={filterBarn}
            label="الحظيرة"
            onChange={(e) => setFilterBarn(e.target.value)}
          >
            <MenuItem value="">الكل</MenuItem>
            {(() => {
              const activeBarns = getActiveBarns();
              return activeBarns.map((barn: any) => (
                <MenuItem key={barn.id} value={barn.name}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>
                      {barn.type === 'general'
                        ? '🏠'
                        : barn.type === 'maternity'
                        ? '🍼'
                        : barn.type === 'pregnancy'
                        ? '🤱'
                        : barn.type === 'isolation'
                        ? '🚫'
                        : '🏠'}
                    </Typography>
                    <Typography variant="body2">{barn.name}</Typography>
                  </Box>
                </MenuItem>
              ));
            })()}
          </Select>
        </FormControl>

        {/* Reset Filters Button */}
        <Button
          variant="outlined"
          onClick={handleResetFilters}
          size="small"
          sx={{
            color: 'primary.main',
            borderColor: 'primary.main',
            '&:hover': {
              backgroundColor: 'primary.light',
              borderColor: 'primary.dark',
            },
          }}
        >
          إعادة تعيين
        </Button>
      </Box>

      {/* Births Table */}
      <TableContainer
        component={Paper}
        {...getTableContainerProps(isDarkMode)}
        sx={{
          ...getTableContainerProps(isDarkMode).sx,
          overflow: 'hidden',
        }}
      >
        <Table>
          <TableHead
            sx={{ backgroundColor: isDarkMode ? 'grey.800' : '#f5f5f5' }}
          >
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>رقم التسجيل</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>رقم التاغ</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>النوع</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الجنس</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>العمر</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الوزن (كغ)</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الأم</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>نوع الولادة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الحظيرة</TableCell>
              <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>
                العمليات
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredBirths.length > 0 ? (
              filteredBirths.map((birth) => (
                <TableRow key={birth.id} hover>
                  <TableCell sx={{ fontWeight: 500 }}>
                    {birth.registrationNumber}
                  </TableCell>
                  <TableCell>{birth.tagNumber || '-'}</TableCell>
                  <TableCell>
                    <Chip
                      label={getTypeLabel(birth.type)}
                      color={birth.type === 'sheep' ? 'primary' : 'secondary'}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>{getGenderLabel(birth.gender)}</TableCell>
                  <TableCell>
                    <Typography
                      variant="body2"
                      sx={{ fontFamily: 'monospace', color: 'text.secondary' }}
                    >
                      {calculateAge(birth.birthDate)}
                    </Typography>
                  </TableCell>
                  <TableCell>{birth.birthWeight}</TableCell>
                  <TableCell sx={{ color: 'primary.main', fontWeight: 500 }}>
                    {birth.motherTagNumber}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={birth.birthType === 'single' ? 'مفرد' : 'توأم'}
                      color={birth.birthType === 'single' ? 'success' : 'info'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusLabel(birth.status)}
                      color={getStatusColor(birth.status) as any}
                      size="small"
                      sx={{ fontWeight: 500 }}
                    />
                  </TableCell>
                  <TableCell>{birth.barnLocation || '-'}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>
                    <Box
                      sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}
                    >
                      <IconButton
                        size="small"
                        onClick={() => {
                          setSelectedBirth(birth);
                          setViewDialogOpen(true);
                        }}
                        sx={{ color: 'primary.main' }}
                        title="عرض التفاصيل"
                      >
                        <ViewIcon fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => {
                          setSelectedBirth(birth);
                          setEditFormData({
                            ...birth,
                            tagNumber: birth.tagNumber || '',
                            motherTagNumber: birth.motherTagNumber || '',
                            fatherTagNumber: birth.fatherTagNumber || '',
                            barnLocation: birth.barnLocation || '',
                            notes: birth.notes || '',
                          });
                          setEditDialogOpen(true);
                        }}
                        sx={{ color: 'warning.main' }}
                        title="تعديل"
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => {
                          setSelectedBirth(birth);
                          setDeleteDialogOpen(true);
                        }}
                        sx={{ color: 'error.main' }}
                        title="حذف"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={11} sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    📝 لا توجد مواليد مسجلة
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {getAvailableMothers().length === 0
                      ? 'يرجى إضافة إناث أولاً في صفحة الحيوانات قبل تسجيل المواليد'
                      : 'ابدأ بإضافة أول مولود باستخدام زر "إضافة مولود" أعلاه'}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* View Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
            color: 'white',
            fontWeight: 'bold',
          }}
        >
          🍼 تفاصيل المولود
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {selectedBirth && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              {/* Basic Information */}
              <Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  fontWeight="bold"
                >
                  المعلومات الأساسية
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <Box
                    sx={{
                      flex: '1 1 300px',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 1,
                    }}
                  >
                    <Typography>
                      <strong>رقم التسجيل:</strong>{' '}
                      {selectedBirth.registrationNumber}
                    </Typography>
                    <Typography>
                      <strong>رقم التاغ:</strong>{' '}
                      {selectedBirth.tagNumber || 'غير محدد'}
                    </Typography>
                    <Typography>
                      <strong>النوع:</strong>
                      <Chip
                        label={getTypeLabel(selectedBirth.type)}
                        color={
                          selectedBirth.type === 'sheep'
                            ? 'primary'
                            : 'secondary'
                        }
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Typography>
                    <Typography>
                      <strong>السلالة:</strong> {selectedBirth.breed}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      flex: '1 1 300px',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 1,
                    }}
                  >
                    <Typography>
                      <strong>الجنس:</strong>{' '}
                      {getGenderLabel(selectedBirth.gender)}
                    </Typography>
                    <Typography>
                      <strong>تاريخ الميلاد:</strong>{' '}
                      {new Date(selectedBirth.birthDate).toLocaleDateString(
                        'ar-SA'
                      )}
                    </Typography>
                    <Typography>
                      <strong>العمر:</strong>
                      <Typography
                        component="span"
                        sx={{
                          fontFamily: 'monospace',
                          ml: 1,
                          color: 'text.secondary',
                        }}
                      >
                        {calculateAge(selectedBirth.birthDate)}
                      </Typography>
                    </Typography>
                    <Typography>
                      <strong>وزن الولادة:</strong> {selectedBirth.birthWeight}{' '}
                      كغ
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* Parent Information */}
              <Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  fontWeight="bold"
                >
                  معلومات الوالدين
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <Box
                    sx={{
                      flex: '1 1 300px',
                      p: 2,
                      backgroundColor: 'grey.50',
                      borderRadius: 2,
                    }}
                  >
                    <Typography
                      variant="subtitle1"
                      fontWeight="bold"
                      color="primary"
                      gutterBottom
                    >
                      👩 الأم
                    </Typography>
                    <Typography>
                      <strong>رقم التاغ:</strong>{' '}
                      {selectedBirth.motherTagNumber}
                    </Typography>
                    <Typography>
                      <strong>رقم التسجيل:</strong>{' '}
                      {selectedBirth.motherRegistrationNumber}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      flex: '1 1 300px',
                      p: 2,
                      backgroundColor: 'grey.50',
                      borderRadius: 2,
                    }}
                  >
                    <Typography
                      variant="subtitle1"
                      fontWeight="bold"
                      color="primary"
                      gutterBottom
                    >
                      👨 الأب
                    </Typography>
                    <Typography>
                      <strong>رقم التاغ:</strong>{' '}
                      {selectedBirth.fatherTagNumber || 'غير محدد'}
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* Birth Information */}
              <Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  fontWeight="bold"
                >
                  معلومات الولادة
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <Box
                    sx={{
                      flex: '1 1 300px',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 1,
                    }}
                  >
                    <Typography>
                      <strong>نوع الولادة:</strong>
                      <Chip
                        label={
                          selectedBirth.birthType === 'single' ? 'مفرد' : 'توأم'
                        }
                        color={
                          selectedBirth.birthType === 'single'
                            ? 'success'
                            : 'info'
                        }
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Typography>
                    {selectedBirth.twinCount && (
                      <Typography>
                        <strong>عدد التوائم:</strong> {selectedBirth.twinCount}
                      </Typography>
                    )}
                  </Box>
                  <Box
                    sx={{
                      flex: '1 1 300px',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 1,
                    }}
                  >
                    <Typography>
                      <strong>الحظيرة:</strong>{' '}
                      {selectedBirth.barnLocation || 'غير محدد'}
                    </Typography>
                    <Typography>
                      <strong>الحالة:</strong>
                      <Chip
                        label={getStatusLabel(selectedBirth.status)}
                        color={getStatusColor(selectedBirth.status) as any}
                        size="small"
                        sx={{ ml: 1, fontWeight: 500 }}
                      />
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* Notes */}
              {selectedBirth.notes && (
                <Box>
                  <Typography
                    variant="h6"
                    gutterBottom
                    color="primary"
                    fontWeight="bold"
                  >
                    ملاحظات
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Box
                    sx={{
                      p: 2,
                      backgroundColor: isDarkMode ? 'grey.800' : 'grey.50',
                      borderRadius: 2,
                      color: isDarkMode ? 'white' : 'inherit',
                    }}
                  >
                    <Typography>{selectedBirth.notes}</Typography>
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, backgroundColor: 'grey.50' }}>
          <Button onClick={() => setViewDialogOpen(false)} variant="outlined">
            إغلاق
          </Button>
          <Button
            onClick={() => {
              setViewDialogOpen(false);
              if (selectedBirth) {
                setEditFormData({
                  ...selectedBirth,
                  tagNumber: selectedBirth.tagNumber || '',
                  motherTagNumber: selectedBirth.motherTagNumber || '',
                  fatherTagNumber: selectedBirth.fatherTagNumber || '',
                  barnLocation: selectedBirth.barnLocation || '',
                  notes: selectedBirth.notes || '',
                });
              }
              setEditDialogOpen(true);
            }}
            variant="contained"
            startIcon={<EditIcon />}
          >
            تعديل
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
            color: 'white',
            fontWeight: 'bold',
          }}
        >
          ✏️ تعديل المولود
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {selectedBirth && editFormData && (
            <Box
              sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}
            >
              {/* Birth Info Display */}
              <Box
                sx={{
                  p: 2,
                  backgroundColor: isDarkMode ? 'info.dark' : 'info.light',
                  borderRadius: 2,
                  color: isDarkMode ? 'white' : 'info.contrastText',
                }}
              >
                <Typography variant="subtitle1" fontWeight="bold">
                  تعديل المولود: {selectedBirth.registrationNumber}
                </Typography>
                <Typography variant="body2">
                  {getTypeLabel(selectedBirth.type)} - {selectedBirth.breed} -{' '}
                  {getGenderLabel(selectedBirth.gender)}
                </Typography>
              </Box>

              {/* Basic Information */}
              <Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  fontWeight="bold"
                >
                  المعلومات الأساسية
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <TextField
                    label="رقم التاغ"
                    value={editFormData.tagNumber || ''}
                    onChange={(e) =>
                      setEditFormData({
                        ...editFormData,
                        tagNumber: e.target.value,
                      })
                    }
                    sx={{ flex: '1 1 200px' }}
                    placeholder="اختياري"
                  />
                  <FormControl sx={{ flex: '1 1 200px' }}>
                    <InputLabel>النوع</InputLabel>
                    <Select
                      value={editFormData.type || 'sheep'}
                      label="النوع"
                      onChange={(e) => {
                        setEditFormData({
                          ...editFormData,
                          type: e.target.value as any,
                          breed: '', // Reset breed when type changes
                        });
                      }}
                    >
                      {(() => {
                        const activeTypes = animalTypes.filter(
                          (type) => type.active
                        );
                        console.log(
                          'Births Edit - Active animal types for dropdown:',
                          activeTypes
                        );
                        return activeTypes.map((type) => (
                          <MenuItem
                            key={type.id}
                            value={getAnimalTypeValue(type)}
                          >
                            {type.nameEn.toLowerCase() === 'sheep'
                              ? '🐑'
                              : '🐐'}{' '}
                            {type.name}
                          </MenuItem>
                        ));
                      })()}
                    </Select>
                  </FormControl>
                  <FormControl sx={{ flex: '1 1 200px' }}>
                    <InputLabel>السلالة</InputLabel>
                    <Select
                      value={editFormData.breed || ''}
                      label="السلالة"
                      onChange={(e) =>
                        setEditFormData({
                          ...editFormData,
                          breed: e.target.value,
                        })
                      }
                      disabled={!editFormData.type}
                    >
                      <MenuItem value="">اختر السلالة</MenuItem>
                      {(() => {
                        const availableBreeds = getBreedsByType(
                          editFormData.type || 'sheep'
                        );
                        console.log(
                          'Births Edit - Available breeds for type',
                          editFormData.type,
                          ':',
                          availableBreeds
                        );
                        return availableBreeds.map((breed: any) => (
                          <MenuItem key={breed.id} value={breed.name}>
                            {breed.name}
                          </MenuItem>
                        ));
                      })()}
                    </Select>
                  </FormControl>
                  <FormControl sx={{ flex: '1 1 200px' }}>
                    <InputLabel>الجنس</InputLabel>
                    <Select
                      value={editFormData.gender || 'male'}
                      label="الجنس"
                      onChange={(e) =>
                        setEditFormData({
                          ...editFormData,
                          gender: e.target.value as any,
                        })
                      }
                    >
                      <MenuItem value="male">ذكر</MenuItem>
                      <MenuItem value="female">أنثى</MenuItem>
                    </Select>
                  </FormControl>
                  <TextField
                    label="وزن الولادة (كغ)"
                    type="number"
                    value={editFormData.birthWeight || ''}
                    onChange={(e) =>
                      setEditFormData({
                        ...editFormData,
                        birthWeight: parseFloat(e.target.value) || 0,
                      })
                    }
                    sx={{ flex: '1 1 200px' }}
                    slotProps={{
                      htmlInput: { min: 0, step: 0.1 },
                    }}
                  />
                </Box>
              </Box>

              {/* Parent Information */}
              <Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  fontWeight="bold"
                >
                  معلومات الوالدين
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <TextField
                    label="رقم تاغ الأم"
                    value={editFormData.motherTagNumber || ''}
                    onChange={(e) =>
                      setEditFormData({
                        ...editFormData,
                        motherTagNumber: e.target.value,
                      })
                    }
                    sx={{ flex: '1 1 200px' }}
                    disabled
                    helperText="لا يمكن تغيير الأم بعد التسجيل"
                  />
                  <TextField
                    label="رقم تاغ الأب"
                    value={editFormData.fatherTagNumber || ''}
                    onChange={(e) =>
                      setEditFormData({
                        ...editFormData,
                        fatherTagNumber: e.target.value,
                      })
                    }
                    sx={{ flex: '1 1 200px' }}
                    placeholder="اختياري"
                  />
                </Box>
              </Box>

              {/* Location and Status */}
              <Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  fontWeight="bold"
                >
                  الموقع والحالة
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <FormControl sx={{ flex: '1 1 200px' }}>
                    <InputLabel>الحظيرة</InputLabel>
                    <Select
                      value={editFormData.barnLocation || ''}
                      label="الحظيرة"
                      onChange={(e) =>
                        setEditFormData({
                          ...editFormData,
                          barnLocation: e.target.value,
                        })
                      }
                    >
                      <MenuItem value="">
                        <em>لا توجد حظيرة محددة</em>
                      </MenuItem>
                      {(() => {
                        const activeBarns = getActiveBarns();
                        console.log(
                          'Births Edit - Active barns for dropdown:',
                          activeBarns
                        );
                        return activeBarns.map((barn: any) => (
                          <MenuItem key={barn.id} value={barn.name}>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                              }}
                            >
                              <Typography>
                                {barn.type === 'general'
                                  ? '🏠'
                                  : barn.type === 'maternity'
                                  ? '🍼'
                                  : barn.type === 'pregnancy'
                                  ? '🤱'
                                  : barn.type === 'isolation'
                                  ? '🚫'
                                  : '🏠'}
                              </Typography>
                              <Box>
                                <Typography variant="body2" fontWeight="bold">
                                  {barn.name}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                >
                                  السعة: {barn.capacity} رأس
                                </Typography>
                              </Box>
                            </Box>
                          </MenuItem>
                        ));
                      })()}
                    </Select>
                  </FormControl>
                  <FormControl sx={{ flex: '1 1 200px' }}>
                    <InputLabel>الحالة</InputLabel>
                    <Select
                      value={editFormData.status || 'present'}
                      label="الحالة"
                      onChange={(e) =>
                        setEditFormData({
                          ...editFormData,
                          status: e.target.value as any,
                        })
                      }
                    >
                      <MenuItem value="present">موجود</MenuItem>
                      <MenuItem value="sold">مباع</MenuItem>
                      <MenuItem value="dead">نافق</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Box>

              {/* Notes */}
              <Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  fontWeight="bold"
                >
                  ملاحظات
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <TextField
                  label="ملاحظات"
                  multiline
                  rows={3}
                  value={editFormData.notes || ''}
                  onChange={(e) =>
                    setEditFormData({ ...editFormData, notes: e.target.value })
                  }
                  fullWidth
                  placeholder="أضف أي ملاحظات إضافية..."
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, backgroundColor: 'grey.50' }}>
          <Button onClick={() => setEditDialogOpen(false)} variant="outlined">
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              if (editFormData && selectedBirth) {
                const updatedBirth: Birth = {
                  ...selectedBirth,
                  tagNumber: editFormData.tagNumber || selectedBirth.tagNumber,
                  type: editFormData.type || selectedBirth.type,
                  breed: editFormData.breed || selectedBirth.breed,
                  gender: editFormData.gender || selectedBirth.gender,
                  motherTagNumber:
                    editFormData.motherTagNumber ||
                    selectedBirth.motherTagNumber,
                  fatherTagNumber:
                    editFormData.fatherTagNumber ||
                    selectedBirth.fatherTagNumber,
                  birthWeight:
                    editFormData.birthWeight || selectedBirth.birthWeight,
                  barnLocation:
                    editFormData.barnLocation || selectedBirth.barnLocation,
                  status: editFormData.status || selectedBirth.status,
                  notes: editFormData.notes || selectedBirth.notes,
                };

                const updatedBirths = births.map((birth) =>
                  birth.id === selectedBirth.id ? updatedBirth : birth
                );

                setBirths(updatedBirths);
                localStorage.setItem('births', JSON.stringify(updatedBirths));
                setEditDialogOpen(false);
                setSelectedBirth(null);
                setEditFormData({});
              }
            }}
          >
            حفظ التغييرات
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          {selectedBirth && (
            <Typography>
              هل أنت متأكد من حذف المولود{' '}
              <strong>{selectedBirth.registrationNumber}</strong>؟
              <br />
              هذا الإجراء لا يمكن التراجع عنه.
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>إلغاء</Button>
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              if (selectedBirth) {
                const updatedBirths = births.filter(
                  (b) => b.id !== selectedBirth.id
                );
                setBirths(updatedBirths);
                localStorage.setItem('births', JSON.stringify(updatedBirths));
                setDeleteDialogOpen(false);
                setSelectedBirth(null);
              }
            }}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Birth Dialog */}
      <Dialog
        open={addDialogOpen}
        onClose={() => setAddDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
            color: 'white',
            fontWeight: 'bold',
          }}
        >
          ➕ إضافة مولود جديد
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
            {/* Basic Information */}
            <Box>
              <Typography
                variant="h6"
                gutterBottom
                color="primary"
                fontWeight="bold"
              >
                المعلومات الأساسية
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                <TextField
                  label="رقم التاغ"
                  value={addFormData.tagNumber || ''}
                  onChange={(e) =>
                    setAddFormData({
                      ...addFormData,
                      tagNumber: e.target.value,
                    })
                  }
                  sx={{ flex: '1 1 200px' }}
                  placeholder="اختياري"
                />
                <FormControl sx={{ flex: '1 1 200px' }}>
                  <InputLabel>النوع</InputLabel>
                  <Select
                    value={addFormData.type || 'sheep'}
                    label="النوع"
                    onChange={(e) => {
                      setAddFormData({
                        ...addFormData,
                        type: e.target.value as any,
                        breed: '', // Reset breed when type changes
                      });
                    }}
                  >
                    {(() => {
                      const activeTypes = animalTypes.filter(
                        (type) => type.active
                      );
                      console.log(
                        'Births - Active animal types for dropdown:',
                        activeTypes
                      );
                      return activeTypes.map((type) => (
                        <MenuItem
                          key={type.id}
                          value={getAnimalTypeValue(type)}
                        >
                          {type.nameEn.toLowerCase() === 'sheep' ? '🐑' : '🐐'}{' '}
                          {type.name}
                        </MenuItem>
                      ));
                    })()}
                  </Select>
                </FormControl>
                <FormControl sx={{ flex: '1 1 200px' }}>
                  <InputLabel>السلالة</InputLabel>
                  <Select
                    value={addFormData.breed || ''}
                    label="السلالة"
                    onChange={(e) =>
                      setAddFormData({ ...addFormData, breed: e.target.value })
                    }
                    disabled={!addFormData.type}
                  >
                    <MenuItem value="">اختر السلالة</MenuItem>
                    {(() => {
                      const availableBreeds = getBreedsByType(
                        addFormData.type || 'sheep'
                      );
                      console.log(
                        'Births - Available breeds for type',
                        addFormData.type,
                        ':',
                        availableBreeds
                      );
                      return availableBreeds.map((breed: any) => (
                        <MenuItem key={breed.id} value={breed.name}>
                          {breed.name}
                        </MenuItem>
                      ));
                    })()}
                  </Select>
                </FormControl>
                <FormControl sx={{ flex: '1 1 200px' }}>
                  <InputLabel>الجنس</InputLabel>
                  <Select
                    value={addFormData.gender || 'male'}
                    label="الجنس"
                    onChange={(e) =>
                      setAddFormData({
                        ...addFormData,
                        gender: e.target.value as any,
                      })
                    }
                  >
                    <MenuItem value="male">ذكر</MenuItem>
                    <MenuItem value="female">أنثى</MenuItem>
                  </Select>
                </FormControl>
                <TextField
                  label="تاريخ الميلاد"
                  type="date"
                  value={addFormData.birthDate || ''}
                  onChange={(e) =>
                    setAddFormData({
                      ...addFormData,
                      birthDate: e.target.value,
                    })
                  }
                  sx={{ flex: '1 1 200px' }}
                  slotProps={{
                    inputLabel: { shrink: true },
                  }}
                />
                <TextField
                  label="وزن الولادة (كغ)"
                  type="number"
                  value={addFormData.birthWeight || ''}
                  onChange={(e) =>
                    setAddFormData({
                      ...addFormData,
                      birthWeight: parseFloat(e.target.value) || 0,
                    })
                  }
                  sx={{ flex: '1 1 200px' }}
                  slotProps={{
                    htmlInput: { min: 0, step: 0.1 },
                  }}
                />
              </Box>
            </Box>

            {/* Parent Information */}
            <Box>
              <Typography
                variant="h6"
                gutterBottom
                color="primary"
                fontWeight="bold"
              >
                معلومات الوالدين
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                <FormControl sx={{ flex: '1 1 200px' }} required>
                  <InputLabel>الأم</InputLabel>
                  <Select
                    value={addFormData.motherTagNumber || ''}
                    label="الأم"
                    onChange={(e) => {
                      const selectedMother = getAvailableMothers().find(
                        (m: any) => m.tagNumber === e.target.value
                      );
                      if (selectedMother) {
                        setAddFormData({
                          ...addFormData,
                          motherTagNumber: selectedMother.tagNumber,
                          motherRegistrationNumber:
                            selectedMother.registrationNumber,
                        });
                      }
                    }}
                  >
                    {(() => {
                      const filteredMothers = getAvailableMothers().filter(
                        (mother: any) => {
                          // Filter mothers by selected type and breed if specified
                          if (
                            addFormData.type &&
                            mother.type !== addFormData.type
                          )
                            return false;
                          if (
                            addFormData.breed &&
                            mother.breed !== addFormData.breed
                          )
                            return false;
                          return true;
                        }
                      );

                      if (filteredMothers.length === 0) {
                        return (
                          <MenuItem disabled>
                            لا توجد أمهات متوافقة مع النوع والسلالة المحددة
                          </MenuItem>
                        );
                      }

                      return filteredMothers.map((mother: any) => (
                        <MenuItem key={mother.id} value={mother.tagNumber}>
                          {mother.tagNumber} - {getTypeLabel(mother.type)} (
                          {mother.breed})
                        </MenuItem>
                      ));
                    })()}
                  </Select>
                  {addFormData.type && addFormData.breed && (
                    <Typography
                      variant="caption"
                      sx={{ mt: 0.5, color: 'text.secondary' }}
                    >
                      عرض الأمهات من نوع {getTypeLabel(addFormData.type)} -
                      سلالة {addFormData.breed} فقط
                    </Typography>
                  )}
                </FormControl>
                <TextField
                  label="رقم تاغ الأب"
                  value={addFormData.fatherTagNumber || ''}
                  onChange={(e) =>
                    setAddFormData({
                      ...addFormData,
                      fatherTagNumber: e.target.value,
                    })
                  }
                  sx={{ flex: '1 1 200px' }}
                  placeholder="اختياري"
                />
              </Box>
            </Box>

            {/* Birth Information */}
            <Box>
              <Typography
                variant="h6"
                gutterBottom
                color="primary"
                fontWeight="bold"
              >
                معلومات الولادة
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                <FormControl sx={{ flex: '1 1 200px' }}>
                  <InputLabel>نوع الولادة</InputLabel>
                  <Select
                    value={addFormData.birthType || 'single'}
                    label="نوع الولادة"
                    onChange={(e) =>
                      setAddFormData({
                        ...addFormData,
                        birthType: e.target.value as any,
                      })
                    }
                  >
                    <MenuItem value="single">مفرد</MenuItem>
                    <MenuItem value="twin">توأم</MenuItem>
                  </Select>
                </FormControl>
                {addFormData.birthType === 'twin' && (
                  <TextField
                    label="عدد التوائم"
                    type="number"
                    value={addFormData.twinCount || ''}
                    onChange={(e) =>
                      setAddFormData({
                        ...addFormData,
                        twinCount: parseInt(e.target.value) || 2,
                      })
                    }
                    sx={{ flex: '1 1 200px' }}
                    slotProps={{
                      htmlInput: { min: 2, max: 5 },
                    }}
                  />
                )}
                <FormControl sx={{ flex: '1 1 200px' }}>
                  <InputLabel>الحظيرة</InputLabel>
                  <Select
                    value={addFormData.barnLocation || ''}
                    label="الحظيرة"
                    onChange={(e) =>
                      setAddFormData({
                        ...addFormData,
                        barnLocation: e.target.value,
                      })
                    }
                  >
                    <MenuItem value="">
                      <em>لا توجد حظيرة محددة</em>
                    </MenuItem>
                    {(() => {
                      const activeBarns = getActiveBarns();
                      console.log(
                        'Births Add - Active barns for dropdown:',
                        activeBarns
                      );
                      return activeBarns.map((barn: any) => (
                        <MenuItem key={barn.id} value={barn.name}>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                            }}
                          >
                            <Typography>
                              {barn.type === 'general'
                                ? '🏠'
                                : barn.type === 'maternity'
                                ? '🍼'
                                : barn.type === 'pregnancy'
                                ? '🤱'
                                : barn.type === 'isolation'
                                ? '🚫'
                                : '🏠'}
                            </Typography>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {barn.name}
                              </Typography>
                              <Typography
                                variant="caption"
                                color="text.secondary"
                              >
                                السعة: {barn.capacity} رأس
                              </Typography>
                            </Box>
                          </Box>
                        </MenuItem>
                      ));
                    })()}
                  </Select>
                </FormControl>
              </Box>
            </Box>

            {/* Notes */}
            <Box>
              <Typography
                variant="h6"
                gutterBottom
                color="primary"
                fontWeight="bold"
              >
                ملاحظات
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <TextField
                label="ملاحظات"
                multiline
                rows={3}
                value={addFormData.notes || ''}
                onChange={(e) =>
                  setAddFormData({ ...addFormData, notes: e.target.value })
                }
                fullWidth
                placeholder="أضف أي ملاحظات إضافية..."
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2, backgroundColor: 'grey.50' }}>
          <Button onClick={() => setAddDialogOpen(false)} variant="outlined">
            إلغاء
          </Button>
          <Button
            variant="contained"
            disabled={
              !addFormData.type ||
              !addFormData.breed ||
              !addFormData.motherTagNumber ||
              !addFormData.birthDate
            }
            onClick={() => {
              if (
                addFormData.type &&
                addFormData.breed &&
                addFormData.motherTagNumber &&
                addFormData.birthDate
              ) {
                // Generate new registration number
                const currentYear = new Date().getFullYear();
                const existingNumbers = births
                  .map((b) => b.registrationNumber)
                  .filter((num) => num.startsWith(`${currentYear}-`))
                  .map((num) => parseInt(num.split('-')[1]))
                  .filter((num) => !isNaN(num));

                const nextNumber =
                  existingNumbers.length > 0
                    ? Math.max(...existingNumbers) + 1
                    : 1001;
                const registrationNumber = `${currentYear}-${nextNumber
                  .toString()
                  .padStart(4, '0')}`;

                const newBirth: Birth = {
                  id: Date.now().toString(),
                  registrationNumber,
                  tagNumber: addFormData.tagNumber,
                  motherRegistrationNumber:
                    addFormData.motherRegistrationNumber || '',
                  motherTagNumber: addFormData.motherTagNumber || '',
                  fatherTagNumber: addFormData.fatherTagNumber,
                  birthDate: addFormData.birthDate || '',
                  gender: addFormData.gender || 'male',
                  birthWeight: addFormData.birthWeight || 0,
                  type: addFormData.type || 'sheep',
                  breed: addFormData.breed || '',
                  status: addFormData.status || 'present',
                  birthType: addFormData.birthType || 'single',
                  twinCount: addFormData.twinCount,
                  barnLocation: addFormData.barnLocation,
                  notes: addFormData.notes,
                };

                const updatedBirths = [...births, newBirth];
                setBirths(updatedBirths);
                localStorage.setItem('births', JSON.stringify(updatedBirths));
                setAddDialogOpen(false);
                setAddFormData({
                  type: 'sheep',
                  breed: '',
                  gender: 'male',
                  birthType: 'single',
                  status: 'present',
                  birthWeight: 0,
                });
              }
            }}
          >
            إضافة المولود
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for dropdown notifications */}
      <Snackbar
        open={dropdownSnackbar.open}
        autoHideDuration={3000}
        onClose={() =>
          setDropdownSnackbar({ ...dropdownSnackbar, open: false })
        }
      >
        <Alert
          onClose={() =>
            setDropdownSnackbar({ ...dropdownSnackbar, open: false })
          }
          severity={dropdownSnackbar.severity}
        >
          {dropdownSnackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Births;

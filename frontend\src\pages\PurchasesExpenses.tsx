import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import {
  <PERSON><PERSON>,
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import IconSelector from '../components/IconSelector';
import { useThemeStore } from '../store/themeStore';

// Types
interface PurchaseExpense {
  id: string;
  category: string;
  date: string;
  description: string;
  amount: number;
  paymentMethod: string;
  invoiceNumber?: string;
  supplier?: string;
  notes?: string;
  createdAt: string;
}

interface ExpenseCategory {
  id: string;
  name: string;
  nameEn: string;
  mainCategory: 'purchase' | 'expense';
  costType: 'production' | 'operational';
  icon: string;
  active: boolean;
}

interface PaymentMethod {
  id: string;
  name: string;
  nameEn: string;
  icon: string;
  active: boolean;
}

const PurchasesExpenses: React.FC = () => {
  const { isDarkMode } = useThemeStore();

  // States
  const [purchasesExpenses, setPurchasesExpenses] = useState<PurchaseExpense[]>(
    []
  );
  const [expenseCategories, setExpenseCategories] = useState<ExpenseCategory[]>(
    []
  );
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<PurchaseExpense | null>(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  // Filter states
  const [filterCategory, setFilterCategory] = useState('');
  const [filterCostType, setFilterCostType] = useState('');
  const [filterPaymentMethod, setFilterPaymentMethod] = useState('');
  const [filterDateFrom, setFilterDateFrom] = useState('');
  const [filterDateTo, setFilterDateTo] = useState('');

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Form data
  const [formData, setFormData] = useState({
    category: '',
    date: new Date().toISOString().split('T')[0],
    description: '',
    amount: '',
    paymentMethod: '',
    invoiceNumber: '',
    supplier: '',
    notes: '',
  });

  // Payment Methods Management
  const [paymentMethodDialog, setPaymentMethodDialog] = useState(false);
  const [editingPaymentMethod, setEditingPaymentMethod] =
    useState<PaymentMethod | null>(null);
  const [paymentMethodForm, setPaymentMethodForm] = useState({
    name: '',
    nameEn: '',
    icon: '',
    active: true,
  });

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    // Load purchases/expenses
    const savedPurchasesExpenses = localStorage.getItem('purchasesExpenses');
    if (savedPurchasesExpenses) {
      try {
        setPurchasesExpenses(JSON.parse(savedPurchasesExpenses));
      } catch (error) {
        console.error('Error loading purchases/expenses:', error);
      }
    } else {
      // Add sample data if none exists
      const sampleData: PurchaseExpense[] = [
        {
          id: '1',
          category: 'أعلاف',
          date: '2024-01-15',
          description: 'شراء علف شعير للأغنام - 10 أكياس',
          amount: 1500,
          paymentMethod: 'cash',
          invoiceNumber: 'INV-2024-001',
          supplier: 'شركة الأعلاف المتحدة',
          notes: 'علف عالي الجودة',
          createdAt: new Date().toISOString(),
        },
        {
          id: '2',
          category: 'فواتير كهرباء',
          date: '2024-01-10',
          description: 'فاتورة كهرباء شهر ديسمبر 2023',
          amount: 850,
          paymentMethod: 'transfer',
          invoiceNumber: 'ELEC-2023-12',
          supplier: 'شركة الكهرباء السعودية',
          notes: 'استهلاك مرتفع بسبب التدفئة',
          createdAt: new Date().toISOString(),
        },
        {
          id: '3',
          category: 'علاجات وأدوية بيطرية',
          date: '2024-01-08',
          description: 'شراء لقاحات وأدوية بيطرية',
          amount: 750,
          paymentMethod: 'card',
          invoiceNumber: 'VET-2024-003',
          supplier: 'عيادة الحيوان البيطرية',
          notes: 'لقاحات دورية للقطيع',
          createdAt: new Date().toISOString(),
        },
      ];
      setPurchasesExpenses(sampleData);
      localStorage.setItem('purchasesExpenses', JSON.stringify(sampleData));
    }

    // Load expense categories
    const savedExpenseCategories = localStorage.getItem('expenseCategories');
    if (savedExpenseCategories) {
      try {
        setExpenseCategories(JSON.parse(savedExpenseCategories));
      } catch (error) {
        console.error('Error loading expense categories:', error);
      }
    } else {
      // Add default expense categories if none exist
      const defaultCategories: ExpenseCategory[] = [
        // Purchases (مشتريات)
        {
          id: '1',
          name: 'أعلاف',
          nameEn: 'Feed',
          mainCategory: 'purchase',
          costType: 'production',
          icon: '🌾',
          active: true,
        },
        {
          id: '2',
          name: 'أدوات ومعدات',
          nameEn: 'Tools & Equipment',
          mainCategory: 'purchase',
          costType: 'operational',
          icon: '🔧',
          active: true,
        },
        {
          id: '3',
          name: 'علاجات وأدوية بيطرية',
          nameEn: 'Veterinary Medicine',
          mainCategory: 'purchase',
          costType: 'production',
          icon: '💊',
          active: true,
        },
        {
          id: '4',
          name: 'مكملات غذائية',
          nameEn: 'Supplements',
          mainCategory: 'purchase',
          costType: 'production',
          icon: '🍃',
          active: true,
        },
        {
          id: '5',
          name: 'محروقات',
          nameEn: 'Fuel',
          mainCategory: 'purchase',
          costType: 'operational',
          icon: '⛽',
          active: true,
        },
        {
          id: '6',
          name: 'شراء حيوانات',
          nameEn: 'Animal Purchase',
          mainCategory: 'purchase',
          costType: 'production',
          icon: '🐑',
          active: true,
        },

        // Expenses (مصروفات)
        {
          id: '7',
          name: 'فواتير كهرباء',
          nameEn: 'Electricity Bills',
          mainCategory: 'expense',
          costType: 'operational',
          icon: '⚡',
          active: true,
        },
        {
          id: '8',
          name: 'فواتير مياه',
          nameEn: 'Water Bills',
          mainCategory: 'expense',
          costType: 'operational',
          icon: '💧',
          active: true,
        },
        {
          id: '9',
          name: 'صيانة عامة',
          nameEn: 'General Maintenance',
          mainCategory: 'expense',
          costType: 'operational',
          icon: '🔨',
          active: true,
        },
        {
          id: '10',
          name: 'خدمات بيطرية',
          nameEn: 'Veterinary Services',
          mainCategory: 'expense',
          costType: 'production',
          icon: '👨‍⚕️',
          active: true,
        },
        {
          id: '11',
          name: 'نقل ومواصلات',
          nameEn: 'Transportation',
          mainCategory: 'expense',
          costType: 'production',
          icon: '🚚',
          active: true,
        },
        {
          id: '12',
          name: 'عمولات سوق',
          nameEn: 'Market Commissions',
          mainCategory: 'expense',
          costType: 'production',
          icon: '📋',
          active: true,
        },
        {
          id: '13',
          name: 'رواتب عمالة',
          nameEn: 'Labor Salaries',
          mainCategory: 'expense',
          costType: 'production',
          icon: '👷',
          active: true,
        },
      ];
      setExpenseCategories(defaultCategories);
      localStorage.setItem(
        'expenseCategories',
        JSON.stringify(defaultCategories)
      );
    }

    // Load payment methods
    const savedPaymentMethods = localStorage.getItem('paymentMethods');
    if (savedPaymentMethods) {
      try {
        setPaymentMethods(JSON.parse(savedPaymentMethods));
      } catch (error) {
        console.error('Error loading payment methods:', error);
      }
    } else {
      // Add default payment methods if none exist
      const defaultPaymentMethods: PaymentMethod[] = [
        { id: '1', name: 'نقدي', nameEn: 'Cash', icon: '💵', active: true },
        { id: '2', name: 'شيك', nameEn: 'Check', icon: '📄', active: true },
        {
          id: '3',
          name: 'تحويل بنكي',
          nameEn: 'Bank Transfer',
          icon: '🏦',
          active: true,
        },
        {
          id: '4',
          name: 'بطاقة ائتمان',
          nameEn: 'Credit Card',
          icon: '💳',
          active: true,
        },
        {
          id: '5',
          name: 'بطاقة مدى',
          nameEn: 'Mada Card',
          icon: '💳',
          active: true,
        },
        {
          id: '6',
          name: 'محفظة إلكترونية',
          nameEn: 'E-Wallet',
          icon: '📱',
          active: true,
        },
      ];
      setPaymentMethods(defaultPaymentMethods);
      localStorage.setItem(
        'paymentMethods',
        JSON.stringify(defaultPaymentMethods)
      );
    }
  };

  // Helper functions
  const getActiveCategories = () => {
    return expenseCategories.filter((cat) => cat.active);
  };

  const getCategoryInfo = (categoryName: string) => {
    return expenseCategories.find((cat) => cat.name === categoryName);
  };

  const getActivePaymentMethods = () => {
    return paymentMethods.filter((method) => method.active);
  };

  const getPaymentMethodInfo = (methodName: string) => {
    return paymentMethods.find((method) => method.name === methodName);
  };

  const getPaymentMethodLabel = (method: string) => {
    // First try to find in our payment methods
    const methodInfo = paymentMethods.find(
      (pm) =>
        pm.nameEn.toLowerCase() === method.toLowerCase() || pm.name === method
    );
    if (methodInfo) {
      return methodInfo.name;
    }

    // Fallback for old data
    switch (method) {
      case 'cash':
        return 'نقدي';
      case 'check':
        return 'شيك';
      case 'transfer':
        return 'تحويل بنكي';
      case 'card':
        return 'بطاقة ائتمان';
      default:
        return method;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Filter and search logic
  const filteredData = purchasesExpenses.filter((item) => {
    const categoryInfo = getCategoryInfo(item.category);

    // Search filter
    const matchesSearch =
      searchTerm === '' ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.supplier &&
        item.supplier.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.invoiceNumber &&
        item.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()));

    // Category filter
    const matchesCategory =
      filterCategory === '' || item.category === filterCategory;

    // Cost type filter
    const matchesCostType =
      filterCostType === '' ||
      (categoryInfo && categoryInfo.costType === filterCostType);

    // Payment method filter
    const matchesPaymentMethod =
      filterPaymentMethod === '' || item.paymentMethod === filterPaymentMethod;

    // Date range filter
    const matchesDateFrom =
      filterDateFrom === '' || item.date >= filterDateFrom;
    const matchesDateTo = filterDateTo === '' || item.date <= filterDateTo;

    return (
      matchesSearch &&
      matchesCategory &&
      matchesCostType &&
      matchesPaymentMethod &&
      matchesDateFrom &&
      matchesDateTo
    );
  });

  // Calculate totals
  const totalAmount = filteredData.reduce((sum, item) => sum + item.amount, 0);
  const purchaseAmount = filteredData
    .filter((item) => {
      const categoryInfo = getCategoryInfo(item.category);
      return categoryInfo && categoryInfo.mainCategory === 'purchase';
    })
    .reduce((sum, item) => sum + item.amount, 0);
  const expenseAmount = filteredData
    .filter((item) => {
      const categoryInfo = getCategoryInfo(item.category);
      return categoryInfo && categoryInfo.mainCategory === 'expense';
    })
    .reduce((sum, item) => sum + item.amount, 0);

  // Handle form submission
  const handleSubmit = () => {
    if (
      !formData.category ||
      !formData.description ||
      !formData.amount ||
      !formData.paymentMethod
    ) {
      setSnackbar({
        open: true,
        message: 'يرجى ملء جميع الحقول المطلوبة',
        severity: 'error',
      });
      return;
    }

    const newItem: PurchaseExpense = {
      id: editingItem?.id || Date.now().toString(),
      category: formData.category,
      date: formData.date,
      description: formData.description,
      amount: parseFloat(formData.amount),
      paymentMethod: formData.paymentMethod,
      invoiceNumber: formData.invoiceNumber || undefined,
      supplier: formData.supplier || undefined,
      notes: formData.notes || undefined,
      createdAt: editingItem?.createdAt || new Date().toISOString(),
    };

    let updatedData;
    if (editingItem) {
      updatedData = purchasesExpenses.map((item) =>
        item.id === editingItem.id ? newItem : item
      );
    } else {
      updatedData = [...purchasesExpenses, newItem];
    }

    setPurchasesExpenses(updatedData);
    localStorage.setItem('purchasesExpenses', JSON.stringify(updatedData));

    setSnackbar({
      open: true,
      message: editingItem ? 'تم التحديث بنجاح' : 'تم الإضافة بنجاح',
      severity: 'success',
    });

    handleCloseDialog();
  };

  const handleEdit = (item: PurchaseExpense) => {
    setEditingItem(item);
    setFormData({
      category: item.category,
      date: item.date,
      description: item.description,
      amount: item.amount.toString(),
      paymentMethod: item.paymentMethod,
      invoiceNumber: item.invoiceNumber || '',
      supplier: item.supplier || '',
      notes: item.notes || '',
    });
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      const updatedData = purchasesExpenses.filter((item) => item.id !== id);
      setPurchasesExpenses(updatedData);
      localStorage.setItem('purchasesExpenses', JSON.stringify(updatedData));

      setSnackbar({
        open: true,
        message: 'تم الحذف بنجاح',
        severity: 'success',
      });
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingItem(null);
    setFormData({
      category: '',
      date: new Date().toISOString().split('T')[0],
      description: '',
      amount: '',
      paymentMethod: '',
      invoiceNumber: '',
      supplier: '',
      notes: '',
    });
  };

  const handleResetFilters = () => {
    setFilterCategory('');
    setFilterCostType('');
    setFilterPaymentMethod('');
    setFilterDateFrom('');
    setFilterDateTo('');
    setSearchTerm('');
    setPage(0);
  };

  // Payment Methods Management Functions
  const handleAddPaymentMethod = () => {
    setEditingPaymentMethod(null);
    setPaymentMethodForm({
      name: '',
      nameEn: '',
      icon: '💵', // Default icon
      active: true,
    });
    setPaymentMethodDialog(true);
  };

  const handleEditPaymentMethod = (method: PaymentMethod) => {
    setEditingPaymentMethod(method);
    setPaymentMethodForm({
      name: method.name,
      nameEn: method.nameEn,
      icon: method.icon,
      active: method.active,
    });
    setPaymentMethodDialog(true);
  };

  const handleDeletePaymentMethod = (id: string) => {
    if (window.confirm('هل أنت متأكد من حذف طريقة الدفع هذه؟')) {
      const updatedMethods = paymentMethods.filter(
        (method) => method.id !== id
      );
      setPaymentMethods(updatedMethods);
      localStorage.setItem('paymentMethods', JSON.stringify(updatedMethods));

      setSnackbar({
        open: true,
        message: 'تم حذف طريقة الدفع بنجاح',
        severity: 'success',
      });
    }
  };

  const handleSavePaymentMethod = () => {
    if (
      !paymentMethodForm.name ||
      !paymentMethodForm.nameEn ||
      !paymentMethodForm.icon
    ) {
      setSnackbar({
        open: true,
        message: 'يرجى ملء جميع الحقول المطلوبة',
        severity: 'error',
      });
      return;
    }

    const newMethod: PaymentMethod = {
      id: editingPaymentMethod?.id || Date.now().toString(),
      name: paymentMethodForm.name,
      nameEn: paymentMethodForm.nameEn,
      icon: paymentMethodForm.icon,
      active: paymentMethodForm.active,
    };

    let updatedMethods;
    if (editingPaymentMethod) {
      updatedMethods = paymentMethods.map((method) =>
        method.id === editingPaymentMethod.id ? newMethod : method
      );
    } else {
      updatedMethods = [...paymentMethods, newMethod];
    }

    setPaymentMethods(updatedMethods);
    localStorage.setItem('paymentMethods', JSON.stringify(updatedMethods));

    setSnackbar({
      open: true,
      message: editingPaymentMethod
        ? 'تم تحديث طريقة الدفع بنجاح'
        : 'تم إضافة طريقة الدفع بنجاح',
      severity: 'success',
    });

    setPaymentMethodDialog(false);
    setEditingPaymentMethod(null);
    setPaymentMethodForm({
      name: '',
      nameEn: '',
      icon: '',
      active: true,
    });
  };

  const handleClosePaymentMethodDialog = () => {
    setPaymentMethodDialog(false);
    setEditingPaymentMethod(null);
    setPaymentMethodForm({
      name: '',
      nameEn: '',
      icon: '',
      active: true,
    });
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
        }}
      >
        <Typography variant="h4" component="h1" fontWeight="bold">
          💰 المشتريات والمصروفات
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenDialog(true)}
          sx={{
            background: 'linear-gradient(45deg, #ff9800 30%, #ffb74d 90%)',
            '&:hover': {
              background: 'linear-gradient(45deg, #f57c00 30%, #ffa726 90%)',
            },
          }}
        >
          إضافة عملية جديدة
        </Button>
      </Box>

      {/* Search and Filters */}
      <Box sx={{ mb: 3 }}>
        <TextField
          placeholder="البحث في المشتريات والمصروفات..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ mb: 2, minWidth: 300 }}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            },
          }}
        />
      </Box>

      {/* Filters */}
      <Box
        sx={{
          display: 'flex',
          gap: 2,
          mb: 3,
          flexWrap: 'wrap',
          alignItems: 'center',
        }}
      >
        {/* Category Filter */}
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>التصنيف</InputLabel>
          <Select
            value={filterCategory}
            label="التصنيف"
            onChange={(e) => setFilterCategory(e.target.value)}
          >
            <MenuItem value="">الكل</MenuItem>
            {getActiveCategories().map((category) => (
              <MenuItem key={category.id} value={category.name}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography>{category.icon}</Typography>
                  <Typography variant="body2">{category.name}</Typography>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Cost Type Filter */}
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>نوع التكلفة</InputLabel>
          <Select
            value={filterCostType}
            label="نوع التكلفة"
            onChange={(e) => setFilterCostType(e.target.value)}
          >
            <MenuItem value="">الكل</MenuItem>
            <MenuItem value="production">إنتاجية</MenuItem>
            <MenuItem value="operational">تشغيلية</MenuItem>
          </Select>
        </FormControl>

        {/* Payment Method Filter */}
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>طريقة الدفع</InputLabel>
          <Select
            value={filterPaymentMethod}
            label="طريقة الدفع"
            onChange={(e) => setFilterPaymentMethod(e.target.value)}
          >
            <MenuItem value="">الكل</MenuItem>
            {getActivePaymentMethods().map((method) => (
              <MenuItem key={method.id} value={method.name}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography>{method.icon}</Typography>
                  <Typography variant="body2">{method.name}</Typography>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Date From Filter */}
        <TextField
          label="من تاريخ"
          type="date"
          size="small"
          value={filterDateFrom}
          onChange={(e) => setFilterDateFrom(e.target.value)}
          sx={{ minWidth: 150 }}
          slotProps={{
            inputLabel: { shrink: true },
          }}
        />

        {/* Date To Filter */}
        <TextField
          label="إلى تاريخ"
          type="date"
          size="small"
          value={filterDateTo}
          onChange={(e) => setFilterDateTo(e.target.value)}
          sx={{ minWidth: 150 }}
          slotProps={{
            inputLabel: { shrink: true },
          }}
        />

        {/* Reset Filters Button */}
        <Button
          variant="outlined"
          onClick={handleResetFilters}
          size="small"
          sx={{
            color: 'primary.main',
            borderColor: 'primary.main',
            '&:hover': {
              backgroundColor: 'primary.light',
              borderColor: 'primary.dark',
            },
          }}
        >
          إعادة تعيين
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'primary.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'primary.contrastText' }}
            >
              {formatCurrency(totalAmount)}
            </Typography>
            <Typography
              variant="caption"
              sx={{ color: 'primary.contrastText' }}
            >
              إجمالي المبلغ
            </Typography>
          </Paper>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'success.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'success.contrastText' }}
            >
              {formatCurrency(purchaseAmount)}
            </Typography>
            <Typography
              variant="caption"
              sx={{ color: 'success.contrastText' }}
            >
              إجمالي المشتريات
            </Typography>
          </Paper>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'error.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'error.contrastText' }}
            >
              {formatCurrency(expenseAmount)}
            </Typography>
            <Typography variant="caption" sx={{ color: 'error.contrastText' }}>
              إجمالي المصروفات
            </Typography>
          </Paper>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'info.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'info.contrastText' }}
            >
              {filteredData.length}
            </Typography>
            <Typography variant="caption" sx={{ color: 'info.contrastText' }}>
              عدد العمليات
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Data Table */}
      <TableContainer
        component={Paper}
        sx={{ borderRadius: 2, overflow: 'hidden' }}
      >
        <Table>
          <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>التصنيف</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الوصف</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>طريقة الدفع</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>المورد</TableCell>
              <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>
                العمليات
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredData
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((item) => {
                const categoryInfo = getCategoryInfo(item.category);
                return (
                  <TableRow key={item.id} hover>
                    <TableCell>
                      {new Date(item.date).toLocaleDateString('ar-SA')}
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        <Typography>{categoryInfo?.icon || '📋'}</Typography>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {item.category}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Chip
                              label={
                                categoryInfo?.mainCategory === 'purchase'
                                  ? 'مشتريات'
                                  : 'مصروفات'
                              }
                              color={
                                categoryInfo?.mainCategory === 'purchase'
                                  ? 'primary'
                                  : 'secondary'
                              }
                              size="small"
                              variant="outlined"
                            />
                            <Chip
                              label={
                                categoryInfo?.costType === 'production'
                                  ? 'إنتاجية'
                                  : 'تشغيلية'
                              }
                              color={
                                categoryInfo?.costType === 'production'
                                  ? 'success'
                                  : 'info'
                              }
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {item.description}
                      </Typography>
                      {item.invoiceNumber && (
                        <Typography variant="caption" color="text.secondary">
                          فاتورة: {item.invoiceNumber}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        color="primary.main"
                      >
                        {formatCurrency(item.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getPaymentMethodLabel(item.paymentMethod)}
                        color="default"
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {item.supplier || '-'}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>
                      <Box
                        sx={{
                          display: 'flex',
                          gap: 1,
                          justifyContent: 'center',
                        }}
                      >
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(item)}
                          sx={{ color: 'primary.main' }}
                          title="تعديل"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(item.id)}
                          sx={{ color: 'error.main' }}
                          title="حذف"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <TablePagination
        component="div"
        count={filteredData.length}
        page={page}
        onPageChange={(e, newPage) => setPage(newPage)}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={(e) => {
          setRowsPerPage(parseInt(e.target.value, 10));
          setPage(0);
        }}
        labelRowsPerPage="عدد الصفوف في الصفحة:"
        labelDisplayedRows={({ from, to, count }) =>
          `${from}-${to} من ${count}`
        }
      />

      {/* Add/Edit Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
          },
        }}
      >
        <DialogTitle
          sx={{
            fontWeight: 'bold',
            borderBottom: '1px solid #e0e0e0',
            background: 'linear-gradient(45deg, #ff9800 30%, #ffb74d 90%)',
            color: 'white',
          }}
        >
          {editingItem ? 'تعديل عملية' : 'إضافة عملية جديدة'}
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <Grid container spacing={3}>
            {/* Category */}
            <Grid size={6}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                التصنيف <span style={{ color: 'red' }}>*</span>
              </Typography>
              <FormControl fullWidth variant="outlined">
                <Select
                  value={formData.category}
                  onChange={(e) =>
                    setFormData({ ...formData, category: e.target.value })
                  }
                  displayEmpty
                  sx={{ borderRadius: 2 }}
                >
                  <MenuItem value="" disabled>
                    <em>اختر التصنيف</em>
                  </MenuItem>
                  {getActiveCategories().map((category) => (
                    <MenuItem key={category.id} value={category.name}>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        <Typography>{category.icon}</Typography>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {category.name}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Chip
                              label={
                                category.mainCategory === 'purchase'
                                  ? 'مشتريات'
                                  : 'مصروفات'
                              }
                              color={
                                category.mainCategory === 'purchase'
                                  ? 'primary'
                                  : 'secondary'
                              }
                              size="small"
                              variant="outlined"
                            />
                            <Chip
                              label={
                                category.costType === 'production'
                                  ? 'إنتاجية'
                                  : 'تشغيلية'
                              }
                              color={
                                category.costType === 'production'
                                  ? 'success'
                                  : 'info'
                              }
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        </Box>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Date */}
            <Grid size={6}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                التاريخ <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                type="date"
                variant="outlined"
                value={formData.date}
                onChange={(e) =>
                  setFormData({ ...formData, date: e.target.value })
                }
                sx={{ borderRadius: 2 }}
                slotProps={{
                  inputLabel: { shrink: true },
                }}
              />
            </Grid>

            {/* Description */}
            <Grid size={12}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                الوصف/البيان <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder="مثال: شراء علف شعير، فاتورة كهرباء شهر يناير"
                multiline
                rows={2}
                sx={{ borderRadius: 2 }}
              />
            </Grid>

            {/* Amount */}
            <Grid size={6}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                المبلغ (ريال سعودي) <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                type="number"
                variant="outlined"
                value={formData.amount}
                onChange={(e) =>
                  setFormData({ ...formData, amount: e.target.value })
                }
                placeholder="0.00"
                sx={{ borderRadius: 2 }}
                slotProps={{
                  htmlInput: { min: 0, step: 0.01 },
                }}
              />
            </Grid>

            {/* Payment Method */}
            <Grid size={6}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                طريقة الدفع <span style={{ color: 'red' }}>*</span>
              </Typography>
              <FormControl fullWidth variant="outlined">
                <Select
                  value={formData.paymentMethod}
                  onChange={(e) =>
                    setFormData({ ...formData, paymentMethod: e.target.value })
                  }
                  displayEmpty
                  sx={{ borderRadius: 2 }}
                >
                  <MenuItem value="" disabled>
                    <em>اختر طريقة الدفع</em>
                  </MenuItem>
                  {getActivePaymentMethods().map((method) => (
                    <MenuItem key={method.id} value={method.name}>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        <Typography>{method.icon}</Typography>
                        <Typography variant="body2">{method.name}</Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Box
                sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}
              >
                <Typography variant="caption" color="text.secondary">
                  لا تجد طريقة الدفع المطلوبة؟
                </Typography>
                <Button
                  size="small"
                  variant="text"
                  onClick={handleAddPaymentMethod}
                  sx={{ fontSize: '0.75rem' }}
                >
                  إدارة طرق الدفع
                </Button>
              </Box>
            </Grid>

            {/* Invoice Number */}
            <Grid size={6}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                رقم الفاتورة
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                value={formData.invoiceNumber}
                onChange={(e) =>
                  setFormData({ ...formData, invoiceNumber: e.target.value })
                }
                placeholder="مثال: INV-2024-001"
                sx={{ borderRadius: 2 }}
              />
            </Grid>

            {/* Supplier */}
            <Grid size={6}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                المورد/الجهة
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                value={formData.supplier}
                onChange={(e) =>
                  setFormData({ ...formData, supplier: e.target.value })
                }
                placeholder="مثال: شركة الأعلاف المتحدة"
                sx={{ borderRadius: 2 }}
              />
            </Grid>

            {/* Notes */}
            <Grid size={12}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                ملاحظات
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                value={formData.notes}
                onChange={(e) =>
                  setFormData({ ...formData, notes: e.target.value })
                }
                placeholder="أي ملاحظات إضافية..."
                multiline
                rows={3}
                sx={{ borderRadius: 2 }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3, borderTop: '1px solid #e0e0e0' }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            إلغاء
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            sx={{
              borderRadius: 2,
              background: 'linear-gradient(45deg, #ff9800 30%, #ffb74d 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #f57c00 30%, #ffa726 90%)',
              },
            }}
            disabled={
              !formData.category ||
              !formData.description ||
              !formData.amount ||
              !formData.paymentMethod
            }
          >
            {editingItem ? 'تحديث' : 'إضافة'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Payment Methods Management Dialog */}
      <Dialog
        open={paymentMethodDialog}
        onClose={handleClosePaymentMethodDialog}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
          },
        }}
      >
        <DialogTitle
          sx={{
            fontWeight: 'bold',
            borderBottom: '1px solid #e0e0e0',
            background: 'linear-gradient(45deg, #2196f3 30%, #64b5f6 90%)',
            color: 'white',
          }}
        >
          {editingPaymentMethod ? 'تعديل طريقة دفع' : 'إضافة طريقة دفع جديدة'}
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <Grid container spacing={3}>
            {/* Name in Arabic */}
            <Grid size={6}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                الاسم بالعربية <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                value={paymentMethodForm.name}
                onChange={(e) =>
                  setPaymentMethodForm({
                    ...paymentMethodForm,
                    name: e.target.value,
                  })
                }
                placeholder="مثال: نقدي، شيك، تحويل بنكي"
                sx={{ borderRadius: 2 }}
              />
            </Grid>

            {/* Name in English */}
            <Grid size={6}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                الاسم بالإنجليزية <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                value={paymentMethodForm.nameEn}
                onChange={(e) =>
                  setPaymentMethodForm({
                    ...paymentMethodForm,
                    nameEn: e.target.value,
                  })
                }
                placeholder="Example: Cash, Check, Bank Transfer"
                sx={{ borderRadius: 2 }}
              />
            </Grid>

            {/* Icon */}
            <Grid size={12}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                الأيقونة <span style={{ color: 'red' }}>*</span>
              </Typography>

              <IconSelector
                selectedIcon={paymentMethodForm.icon}
                onIconSelect={(icon) =>
                  setPaymentMethodForm({
                    ...paymentMethodForm,
                    icon: icon,
                  })
                }
                title="أيقونة طريقة الدفع"
                maxHeight={300}
              />
            </Grid>

            {/* Active Status */}
            <Grid size={6}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 1 }}
              >
                الحالة
              </Typography>
              <FormControl fullWidth variant="outlined">
                <Select
                  value={paymentMethodForm.active ? 'active' : 'inactive'}
                  onChange={(e) =>
                    setPaymentMethodForm({
                      ...paymentMethodForm,
                      active: e.target.value === 'active',
                    })
                  }
                  sx={{ borderRadius: 2 }}
                >
                  <MenuItem value="active">نشط</MenuItem>
                  <MenuItem value="inactive">غير نشط</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Current Payment Methods List */}
            <Grid size={12}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                color="text.primary"
                sx={{ mb: 2 }}
              >
                طرق الدفع الحالية
              </Typography>
              <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                {paymentMethods.map((method) => (
                  <Box
                    key={method.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      p: 1,
                      border: '1px solid #e0e0e0',
                      borderRadius: 1,
                      mb: 1,
                      backgroundColor: method.active
                        ? 'transparent'
                        : '#f5f5f5',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography>{method.icon}</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {method.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        ({method.nameEn})
                      </Typography>
                      {!method.active && (
                        <Chip label="غير نشط" size="small" color="default" />
                      )}
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton
                        size="small"
                        onClick={() => handleEditPaymentMethod(method)}
                        sx={{ color: 'primary.main' }}
                        title="تعديل"
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeletePaymentMethod(method.id)}
                        sx={{ color: 'error.main' }}
                        title="حذف"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3, borderTop: '1px solid #e0e0e0' }}>
          <Button
            onClick={handleClosePaymentMethodDialog}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            إلغاء
          </Button>
          <Button
            onClick={handleSavePaymentMethod}
            variant="contained"
            sx={{
              borderRadius: 2,
              background: 'linear-gradient(45deg, #2196f3 30%, #64b5f6 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
              },
            }}
            disabled={
              !paymentMethodForm.name ||
              !paymentMethodForm.nameEn ||
              !paymentMethodForm.icon
            }
          >
            {editingPaymentMethod ? 'تحديث' : 'إضافة'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          severity={snackbar.severity}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PurchasesExpenses;

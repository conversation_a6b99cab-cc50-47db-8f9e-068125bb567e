const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addMoreData() {
  try {
    console.log('🌱 إضافة المزيد من البيانات التجريبية...');

    // إضافة أنواع الحيوانات
    const sheepType = await prisma.animalType.create({
      data: {
        name: 'sheep',
        nameAr: 'أغنام',
        nameEn: 'Sheep',
      },
    });

    const goatType = await prisma.animalType.create({
      data: {
        name: 'goat',
        nameAr: 'ماعز',
        nameEn: 'Goat',
      },
    });

    // إضافة السلالات
    const najdiBreed = await prisma.breed.create({
      data: {
        name: 'najdi',
        nameAr: 'نجدي',
        nameEn: 'Najdi',
        animalTypeId: sheepType.id,
        description: 'سلالة أغنام نجدية محلية',
      },
    });

    const shamiBreed = await prisma.breed.create({
      data: {
        name: 'shami',
        nameAr: 'شامي',
        nameEn: 'Sham<PERSON>',
        animalTypeId: goatType.id,
        description: 'سلالة ماعز شامية',
      },
    });

    // إضافة حيوانات
    const animals = [];
    for (let i = 1; i <= 10; i++) {
      const animal = await prisma.animal.create({
        data: {
          internalId: `A${i.toString().padStart(3, '0')}`,
          tagNumber: `T${i.toString().padStart(3, '0')}`,
          animalTypeId: i % 2 === 0 ? goatType.id : sheepType.id,
          breedId: i % 2 === 0 ? shamiBreed.id : najdiBreed.id,
          gender: i % 3 === 0 ? 'MALE' : 'FEMALE',
          category: i <= 2 ? 'MOTHER' : i <= 4 ? 'FATHER' : 'NEWBORN',
          source: i <= 5 ? 'PURCHASED' : 'INTERNAL',
          birthDate: new Date(2022 + (i % 3), i % 12, 15),
          birthWeight: 3.0 + i * 0.2,
          currentWeight: 25.0 + i * 2.5,
          status: 'ALIVE',
          barnLocation: `حظيرة ${String.fromCharCode(65 + (i % 3))}`,
          notes: `حيوان رقم ${i}`,
        },
      });
      animals.push(animal);
    }

    // إنشاء دورة إنتاجية أولاً
    const reproductionCycle = await prisma.reproductionCycle.create({
      data: {
        cycleNumber: 1,
        animalTypeId: sheepType.id,
        motherId: animals[0].id,
        fatherId: animals[1].id,
        pregnancyStart: new Date(2024, 0, 1),
        status: 'COMPLETED',
        totalCost: 1000,
        totalRevenue: 5000,
        notes: 'دورة إنتاجية تجريبية',
      },
    });

    // إضافة مواليد
    const births = [];
    for (let i = 1; i <= 5; i++) {
      const birth = await prisma.birth.create({
        data: {
          reproductionCycleId: reproductionCycle.id,
          motherId: animals[0].id, // استخدام أول حيوان كأم
          fatherId: animals[1].id, // استخدام ثاني حيوان كفحل
          gender: i % 2 === 0 ? 'MALE' : 'FEMALE',
          birthDate: new Date(2024, i % 12, 15),
          birthWeight: 3.0 + i * 0.1,
          birthType: 'SINGLE',
          status: 'ALIVE',
          notes: `مولود رقم ${i}`,
        },
      });
      births.push(birth);
    }

    // إضافة علاجات
    const treatments = [];
    for (let i = 1; i <= 8; i++) {
      const treatment = await prisma.treatment.create({
        data: {
          animalId: animals[i % animals.length].id,
          diseaseType: i % 2 === 0 ? 'تطعيم وقائي' : 'علاج طفيليات',
          medicine: i % 2 === 0 ? 'لقاح الحمى القلاعية' : 'مضاد طفيليات',
          dosage: `${2 + i} مل`,
          date: new Date(2024, i % 12, 15),
          administeredBy: i % 2 === 0 ? 'د. سالم أحمد' : 'د. محمد حسن',
          cost: 25 + i * 5,
          notes: `علاج رقم ${i}`,
        },
      });
      treatments.push(treatment);
    }

    // إضافة سجلات أوزان
    const weightRecords = [];
    for (let i = 1; i <= 15; i++) {
      const weightRecord = await prisma.weightRecord.create({
        data: {
          animalId: animals[i % animals.length].id,
          weight: 25.0 + i * 1.5,
          date: new Date(2024, i % 12, 15),
          notes: `قياس وزن رقم ${i}`,
        },
      });
      weightRecords.push(weightRecord);
    }

    // إضافة المزيد من الموظفين
    const moreEmployees = [];
    const employeeNames = ['خالد سعد', 'فهد محمد', 'عبدالله أحمد', 'ناصر علي'];
    const positions = ['مساعد راعي', 'عامل تنظيف', 'سائق', 'حارس'];

    for (let i = 0; i < employeeNames.length; i++) {
      const employee = await prisma.employee.create({
        data: {
          name: employeeNames[i],
          idNumber: `${1111111111 + i}`,
          position: positions[i],
          monthlySalary: 3000 + i * 500,
          hireDate: new Date(2023, i + 2, 1),
          status: 'ACTIVE',
          phone: `050${1234567 + i}`,
        },
      });
      moreEmployees.push(employee);
    }

    // إضافة المزيد من المبيعات
    const moreSales = [];
    for (let i = 1; i <= 6; i++) {
      const sale = await prisma.sale.create({
        data: {
          saleDate: new Date(2024, i % 12, 15),
          saleType:
            i % 3 === 0
              ? 'MARKET'
              : i % 3 === 1
              ? 'INDIVIDUAL'
              : 'SLAUGHTER_DELIVERY',
          totalPrice: 5000 + i * 1000,
          customerName: `عميل رقم ${i}`,
          customerPhone: `050${2222222 + i}`,
          notes: `بيع رقم ${i}`,
        },
      });
      moreSales.push(sale);
    }

    // إضافة المزيد من المشتريات
    const morePurchases = [];
    const purchaseTypes = [
      'ANIMALS',
      'EQUIPMENT',
      'TOOLS',
      'MEDICINE',
      'MAINTENANCE',
      'OTHER',
    ];
    const descriptions = [
      'حيوانات جديدة',
      'معدات المزرعة',
      'أدوات العمل',
      'أدوية بيطرية',
      'صيانة المعدات',
      'مشتريات متنوعة',
    ];

    for (let i = 0; i < purchaseTypes.length; i++) {
      const purchase = await prisma.purchase.create({
        data: {
          type: purchaseTypes[i],
          description: descriptions[i],
          quantity: 10 + i * 5,
          unitPrice: 100 + i * 50,
          totalCost: (10 + i * 5) * (100 + i * 50),
          purchaseDate: new Date(2024, i + 1, 15),
          supplier: `مورد رقم ${i + 1}`,
          notes: `مشترى رقم ${i + 1}`,
        },
      });
      morePurchases.push(purchase);
    }

    // إضافة المزيد من المصروفات
    const moreExpenses = [];
    const expenseCategories = [
      'كهرباء',
      'مياه',
      'صيانة',
      'وقود',
      'تأمين',
      'رواتب',
    ];
    const expenseDescriptions = [
      'فاتورة كهرباء',
      'فاتورة مياه',
      'صيانة دورية',
      'وقود المولد',
      'تأمين المزرعة',
      'رواتب إضافية',
    ];

    for (let i = 0; i < expenseCategories.length; i++) {
      const expense = await prisma.expense.create({
        data: {
          description: expenseDescriptions[i],
          category: expenseCategories[i],
          amount: 500 + i * 200,
          date: new Date(2024, i + 1, 15),
          notes: `مصروف رقم ${i + 1}`,
        },
      });
      moreExpenses.push(expense);
    }

    console.log('✅ تم إضافة المزيد من البيانات التجريبية بنجاح!');
    console.log(`📊 الإحصائيات الجديدة:`);
    console.log(`   - الحيوانات: ${animals.length}`);
    console.log(`   - المواليد: ${births.length}`);
    console.log(`   - الموظفين: ${moreEmployees.length} (إضافي)`);
    console.log(`   - المبيعات: ${moreSales.length} (إضافي)`);
    console.log(`   - المشتريات: ${morePurchases.length} (إضافي)`);
    console.log(`   - المصروفات: ${moreExpenses.length} (إضافي)`);
    console.log(`   - العلاجات: ${treatments.length}`);
    console.log(`   - سجلات الأوزان: ${weightRecords.length}`);
  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addMoreData();

version: '3.8'

services:
  # Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DATABASE_URL=file:./farm.db
    volumes:
      - ./backend/prisma:/app/prisma
      - backend_data:/app/data
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - farm_network

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - farm_network

  # Database Service (PostgreSQL for production)
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=farm_management
      - POSTGRES_USER=farm_user
      - POSTGRES_PASSWORD=farm_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - farm_network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - farm_network

volumes:
  postgres_data:
  redis_data:
  backend_data:

networks:
  farm_network:
    driver: bridge

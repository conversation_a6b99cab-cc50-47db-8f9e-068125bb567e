import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogA<PERSON>,
  Button,
  Box,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Alert,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Grid,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Save as SaveIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import DataAnalysisService from '../../services/dropdown/DataAnalysisService';

interface DataAnalysisDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

const DataAnalysisDialog: React.FC<DataAnalysisDialogProps> = ({
  open,
  onClose,
  onSuccess,
  onError,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveResult, setSaveResult] = useState<any>(null);

  const steps = ['تحليل البيانات', 'مراجعة النتائج', 'حفظ البيانات'];

  const dataAnalysisService = new DataAnalysisService();

  useEffect(() => {
    if (open) {
      setActiveStep(0);
      setAnalysisResult(null);
      setSaveResult(null);
    }
  }, [open]);

  const handleAnalyzeData = async () => {
    setIsAnalyzing(true);
    try {
      // محاكاة تأخير للتحليل
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const result = dataAnalysisService.analyzeAllData();
      setAnalysisResult(result);
      setActiveStep(1);
    } catch (error) {
      console.error('Error analyzing data:', error);
      onError('حدث خطأ أثناء تحليل البيانات');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSaveData = async () => {
    if (!analysisResult) return;

    setIsSaving(true);
    try {
      const allResults = [
        ...analysisResult.animals,
        ...analysisResult.employees,
        ...analysisResult.purchases,
        ...analysisResult.sales,
      ];

      const result = dataAnalysisService.saveExtractedData(allResults);
      setSaveResult(result);
      setActiveStep(2);

      if (result.success) {
        onSuccess(`تم حفظ ${result.savedCategories.length} فئة بنجاح`);
      } else {
        onError(`فشل في حفظ بعض الفئات: ${result.errors.join(', ')}`);
      }
    } catch (error) {
      console.error('Error saving data:', error);
      onError('حدث خطأ أثناء حفظ البيانات');
    } finally {
      setIsSaving(false);
    }
  };

  const renderAnalysisStep = () => (
    <Box sx={{ textAlign: 'center', py: 4 }}>
      <AnalyticsIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
      <Typography variant="h6" gutterBottom>
        تحليل البيانات الموجودة
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        سيتم تحليل جميع السجلات الموجودة في النظام واستخراج القيم الفريدة لإنشاء القوائم المنسدلة
      </Typography>
      
      {isAnalyzing && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress sx={{ mb: 1 }} />
          <Typography variant="caption" color="text.secondary">
            جاري تحليل البيانات...
          </Typography>
        </Box>
      )}

      <Button
        variant="contained"
        onClick={handleAnalyzeData}
        disabled={isAnalyzing}
        size="large"
        startIcon={<AnalyticsIcon />}
      >
        {isAnalyzing ? 'جاري التحليل...' : 'بدء التحليل'}
      </Button>
    </Box>
  );

  const renderResultsStep = () => {
    if (!analysisResult) return null;

    const sections = [
      { title: '🐑 الحيوانات', data: analysisResult.animals, color: '#4caf50' },
      { title: '👥 الموظفين', data: analysisResult.employees, color: '#2196f3' },
      { title: '🛒 المشتريات', data: analysisResult.purchases, color: '#ff9800' },
      { title: '💰 المبيعات', data: analysisResult.sales, color: '#9c27b0' },
    ];

    return (
      <Box>
        {/* ملخص عام */}
        <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              📊 ملخص التحليل
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Typography variant="h4" fontWeight="bold">
                  {analysisResult.summary.totalCategories}
                </Typography>
                <Typography variant="caption">فئة محللة</Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography variant="h4" fontWeight="bold">
                  {analysisResult.summary.totalExtractedItems}
                </Typography>
                <Typography variant="caption">عنصر مستخرج</Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography variant="h4" fontWeight="bold">
                  {analysisResult.summary.totalNewItems}
                </Typography>
                <Typography variant="caption">عنصر جديد</Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* تفاصيل كل قسم */}
        {sections.map((section, index) => (
          section.data.length > 0 && (
            <Accordion key={index} defaultExpanded={index === 0}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="h6">{section.title}</Typography>
                  <Chip 
                    label={`${section.data.reduce((sum, item) => sum + item.newItems.length, 0)} جديد`}
                    size="small"
                    sx={{ bgcolor: section.color, color: 'white' }}
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <List>
                  {section.data.map((result, resultIndex) => (
                    <ListItem key={resultIndex} divider>
                      <ListItemIcon>
                        {result.newItems.length > 0 ? (
                          <CheckCircleIcon color="success" />
                        ) : (
                          <InfoIcon color="info" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={result.category}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              مستخرج: {result.totalCount} | موجود: {result.existingItems.length} | جديد: {result.newItems.length}
                            </Typography>
                            {result.newItems.length > 0 && (
                              <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {result.newItems.slice(0, 5).map((item, itemIndex) => (
                                  <Chip
                                    key={itemIndex}
                                    label={`${item.icon} ${item.name}`}
                                    size="small"
                                    variant="outlined"
                                    color="primary"
                                  />
                                ))}
                                {result.newItems.length > 5 && (
                                  <Chip
                                    label={`+${result.newItems.length - 5} أخرى`}
                                    size="small"
                                    variant="outlined"
                                  />
                                )}
                              </Box>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          )
        ))}

        {analysisResult.summary.totalNewItems === 0 && (
          <Alert severity="info" sx={{ mt: 2 }}>
            لم يتم العثور على عناصر جديدة. جميع البيانات موجودة مسبقاً في القوائم المنسدلة.
          </Alert>
        )}
      </Box>
    );
  };

  const renderSaveStep = () => (
    <Box sx={{ textAlign: 'center', py: 4 }}>
      {isSaving ? (
        <Box>
          <LinearProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            جاري حفظ البيانات...
          </Typography>
        </Box>
      ) : saveResult ? (
        <Box>
          {saveResult.success ? (
            <CheckCircleIcon sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
          ) : (
            <WarningIcon sx={{ fontSize: 64, color: 'warning.main', mb: 2 }} />
          )}
          <Typography variant="h6" gutterBottom>
            {saveResult.success ? 'تم الحفظ بنجاح!' : 'حدثت بعض الأخطاء'}
          </Typography>
          
          {saveResult.savedCategories.length > 0 && (
            <Alert severity="success" sx={{ mt: 2, textAlign: 'left' }}>
              <Typography variant="subtitle2" gutterBottom>
                تم حفظ الفئات التالية:
              </Typography>
              {saveResult.savedCategories.map((category, index) => (
                <Typography key={index} variant="body2">
                  • {category}
                </Typography>
              ))}
            </Alert>
          )}

          {saveResult.errors.length > 0 && (
            <Alert severity="error" sx={{ mt: 2, textAlign: 'left' }}>
              <Typography variant="subtitle2" gutterBottom>
                الأخطاء:
              </Typography>
              {saveResult.errors.map((error, index) => (
                <Typography key={index} variant="body2">
                  • {error}
                </Typography>
              ))}
            </Alert>
          )}
        </Box>
      ) : null}
    </Box>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AnalyticsIcon />
          <Typography variant="h6">تحليل البيانات وإنشاء القوائم المنسدلة</Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Stepper */}
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Step Content */}
        {activeStep === 0 && renderAnalysisStep()}
        {activeStep === 1 && renderResultsStep()}
        {activeStep === 2 && renderSaveStep()}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} startIcon={<CloseIcon />}>
          إغلاق
        </Button>
        
        {activeStep === 1 && analysisResult?.summary.totalNewItems > 0 && (
          <Button
            variant="contained"
            onClick={handleSaveData}
            disabled={isSaving}
            startIcon={<SaveIcon />}
          >
            {isSaving ? 'جاري الحفظ...' : 'حفظ البيانات'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default DataAnalysisDialog;

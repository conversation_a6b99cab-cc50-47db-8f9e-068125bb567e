import {
  Delete as DeleteIcon,
  Download as DownloadIcon,
  History as HistoryIcon,
  Restore as RestoreIcon,
  Save as SaveIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import {
  <PERSON>ert,
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  IconButton,
  InputLabel,
  LinearProgress,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';

interface BackupItem {
  id: string;
  name: string;
  date: string;
  size: number;
  modules: string[];
  itemCount: number;
  description?: string;
}

interface DropdownBackupManagerProps {
  open: boolean;
  onClose: () => void;
  categories: any;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

const DropdownBackupManager: React.FC<DropdownBackupManagerProps> = ({
  open,
  onClose,
  categories,
  onSuccess,
  onError,
}) => {
  const [backups, setBackups] = useState<BackupItem[]>([]);
  const [selectedModules, setSelectedModules] = useState<string[]>([]);
  const [backupName, setBackupName] = useState('');
  const [backupDescription, setBackupDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);

  useEffect(() => {
    if (open) {
      loadBackups();
      setSelectedModules(Object.keys(categories));
      setBackupName(`نسخة احتياطية ${new Date().toLocaleDateString('ar-SA')}`);
    }
  }, [open, categories]);

  const loadBackups = () => {
    try {
      const savedBackups = localStorage.getItem('dropdown_backups');
      if (savedBackups) {
        setBackups(JSON.parse(savedBackups));
      }
    } catch (error) {
      console.error('Error loading backups:', error);
    }
  };

  const saveBackups = (newBackups: BackupItem[]) => {
    try {
      localStorage.setItem('dropdown_backups', JSON.stringify(newBackups));
      setBackups(newBackups);
    } catch (error) {
      console.error('Error saving backups:', error);
      onError('حدث خطأ أثناء حفظ قائمة النسخ الاحتياطية');
    }
  };

  const createBackup = async () => {
    if (!backupName.trim()) {
      onError('يرجى إدخال اسم للنسخة الاحتياطية');
      return;
    }

    setIsCreating(true);
    try {
      const backupData: any = {};
      let totalItems = 0;
      const includedModules: string[] = [];

      selectedModules.forEach((moduleId) => {
        const module = categories[moduleId];
        if (module) {
          includedModules.push(module.name);
          module.categories.forEach((category: any) => {
            const data = localStorage.getItem(category.storageKey);
            if (data) {
              const items = JSON.parse(data);
              backupData[category.storageKey] = items;
              totalItems += Array.isArray(items) ? items.length : 0;
            }
          });
        }
      });

      const backup: BackupItem = {
        id: Date.now().toString(),
        name: backupName,
        date: new Date().toISOString(),
        size: JSON.stringify(backupData).length,
        modules: includedModules,
        itemCount: totalItems,
        description: backupDescription,
      };

      // Save backup data
      localStorage.setItem(
        `dropdown_backup_${backup.id}`,
        JSON.stringify(backupData)
      );

      // Update backups list
      const newBackups = [backup, ...backups];
      saveBackups(newBackups);

      onSuccess('تم إنشاء النسخة الاحتياطية بنجاح');
      setBackupName(`نسخة احتياطية ${new Date().toLocaleDateString('ar-SA')}`);
      setBackupDescription('');
    } catch (error) {
      console.error('Error creating backup:', error);
      onError('حدث خطأ أثناء إنشاء النسخة الاحتياطية');
    } finally {
      setIsCreating(false);
    }
  };

  const restoreBackup = async (backup: BackupItem) => {
    if (
      !window.confirm(
        `هل أنت متأكد من استعادة النسخة الاحتياطية "${backup.name}"؟\nسيتم استبدال البيانات الحالية.`
      )
    ) {
      return;
    }

    setIsRestoring(true);
    try {
      const backupData = localStorage.getItem(`dropdown_backup_${backup.id}`);
      if (!backupData) {
        onError('لم يتم العثور على بيانات النسخة الاحتياطية');
        return;
      }

      const data = JSON.parse(backupData);
      Object.entries(data).forEach(([key, value]) => {
        localStorage.setItem(key, JSON.stringify(value));
      });

      onSuccess('تم استعادة النسخة الاحتياطية بنجاح');

      // Refresh the page to reflect changes
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error restoring backup:', error);
      onError('حدث خطأ أثناء استعادة النسخة الاحتياطية');
    } finally {
      setIsRestoring(false);
    }
  };

  const downloadBackup = (backup: BackupItem) => {
    try {
      const backupData = localStorage.getItem(`dropdown_backup_${backup.id}`);
      if (!backupData) {
        onError('لم يتم العثور على بيانات النسخة الاحتياطية');
        return;
      }

      const fullBackup = {
        metadata: backup,
        data: JSON.parse(backupData),
      };

      const dataStr = JSON.stringify(fullBackup, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${backup.name.replace(/\s+/g, '_')}_${
        backup.date.split('T')[0]
      }.json`;
      link.click();
      URL.revokeObjectURL(url);

      onSuccess('تم تحميل النسخة الاحتياطية بنجاح');
    } catch (error) {
      console.error('Error downloading backup:', error);
      onError('حدث خطأ أثناء تحميل النسخة الاحتياطية');
    }
  };

  const deleteBackup = (backup: BackupItem) => {
    if (
      !window.confirm(`هل أنت متأكد من حذف النسخة الاحتياطية "${backup.name}"؟`)
    ) {
      return;
    }

    try {
      // Remove backup data
      localStorage.removeItem(`dropdown_backup_${backup.id}`);

      // Update backups list
      const newBackups = backups.filter((b) => b.id !== backup.id);
      saveBackups(newBackups);

      onSuccess('تم حذف النسخة الاحتياطية بنجاح');
    } catch (error) {
      console.error('Error deleting backup:', error);
      onError('حدث خطأ أثناء حذف النسخة الاحتياطية');
    }
  };

  const uploadBackup = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const uploadedData = JSON.parse(e.target?.result as string);

        if (uploadedData.metadata && uploadedData.data) {
          // Full backup file
          const backup: BackupItem = {
            ...uploadedData.metadata,
            id: Date.now().toString(), // Generate new ID
          };

          // Save backup data
          localStorage.setItem(
            `dropdown_backup_${backup.id}`,
            JSON.stringify(uploadedData.data)
          );

          // Update backups list
          const newBackups = [backup, ...backups];
          saveBackups(newBackups);

          onSuccess('تم رفع النسخة الاحتياطية بنجاح');
        } else {
          // Direct data import
          Object.entries(uploadedData).forEach(([key, value]) => {
            localStorage.setItem(key, JSON.stringify(value));
          });

          onSuccess('تم استيراد البيانات بنجاح');
        }
      } catch (error) {
        console.error('Error uploading backup:', error);
        onError('حدث خطأ أثناء رفع النسخة الاحتياطية');
      }
    };
    reader.readAsText(file);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <HistoryIcon />
          <Typography variant="h6">إدارة النسخ الاحتياطية</Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Create New Backup Section */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            إنشاء نسخة احتياطية جديدة
          </Typography>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="اسم النسخة الاحتياطية"
              value={backupName}
              onChange={(e) => setBackupName(e.target.value)}
              fullWidth
              required
            />

            <TextField
              label="وصف (اختياري)"
              value={backupDescription}
              onChange={(e) => setBackupDescription(e.target.value)}
              fullWidth
              multiline
              rows={2}
            />

            <FormControl fullWidth>
              <InputLabel>الوحدات المراد نسخها</InputLabel>
              <Select
                multiple
                value={selectedModules}
                onChange={(e) => setSelectedModules(e.target.value as string[])}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip
                        key={value}
                        label={categories[value]?.name}
                        size="small"
                      />
                    ))}
                  </Box>
                )}
              >
                {Object.entries(categories).map(
                  ([id, module]: [string, any]) => (
                    <MenuItem key={id} value={id}>
                      {module.icon} {module.name}
                    </MenuItem>
                  )
                )}
              </Select>
            </FormControl>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={createBackup}
                disabled={isCreating || selectedModules.length === 0}
                sx={{ flexGrow: 1 }}
              >
                {isCreating ? 'جاري الإنشاء...' : 'إنشاء نسخة احتياطية'}
              </Button>

              <Button
                variant="outlined"
                component="label"
                startIcon={<UploadIcon />}
              >
                رفع نسخة
                <input
                  type="file"
                  accept=".json"
                  hidden
                  onChange={uploadBackup}
                />
              </Button>
            </Box>
          </Box>

          {isCreating && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress />
              <Typography variant="caption" color="text.secondary">
                جاري إنشاء النسخة الاحتياطية...
              </Typography>
            </Box>
          )}
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Existing Backups Section */}
        <Box>
          <Typography variant="h6" gutterBottom>
            النسخ الاحتياطية الموجودة ({backups.length})
          </Typography>

          {backups.length === 0 ? (
            <Alert severity="info">
              لا توجد نسخ احتياطية. قم بإنشاء نسخة احتياطية أولى لحفظ بياناتك.
            </Alert>
          ) : (
            <List>
              {backups.map((backup) => (
                <ListItem
                  key={backup.id}
                  divider
                  sx={{
                    pr: { xs: 14, md: 16 }, // مساحة أكبر للأزرار الثلاثة في RTL
                    direction: 'rtl', // تأكيد اتجاه RTL
                  }}
                >
                  <ListItemText
                    primary={
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: { xs: 0.5, md: 1 },
                          flexWrap: 'wrap',
                          pr: 1, // مساحة إضافية من اليمين
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          fontWeight="bold"
                          sx={{ fontSize: { xs: '0.9rem', md: '1rem' } }}
                        >
                          {backup.name}
                        </Typography>
                        <Chip
                          label={`${backup.itemCount} عنصر`}
                          size="small"
                          color="primary"
                          variant="outlined"
                          sx={{
                            fontSize: { xs: '0.7rem', md: '0.75rem' },
                            height: { xs: 20, md: 24 },
                          }}
                        />
                        <Chip
                          label={formatFileSize(backup.size)}
                          size="small"
                          color="info"
                          variant="outlined"
                          sx={{
                            fontSize: { xs: '0.7rem', md: '0.75rem' },
                            height: { xs: 20, md: 24 },
                          }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box sx={{ mt: 1, pr: 1 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' } }}
                        >
                          📅 {new Date(backup.date).toLocaleString('ar-SA')}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' } }}
                        >
                          📦 الوحدات: {backup.modules.join('، ')}
                        </Typography>
                        {backup.description && (
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' } }}
                          >
                            📝 {backup.description}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction
                    sx={{
                      left: { xs: 8, md: 16 }, // موضع الأزرار من اليسار في RTL
                      right: 'auto', // إلغاء الموضع الافتراضي
                      display: 'flex',
                      gap: 0.5,
                    }}
                  >
                    <Tooltip title="استعادة">
                      <IconButton
                        size="small"
                        onClick={() => restoreBackup(backup)}
                        disabled={isRestoring}
                        color="primary"
                        sx={{
                          bgcolor: 'action.hover',
                          '&:hover': {
                            bgcolor: 'primary.light',
                            color: 'primary.contrastText',
                          },
                          '&:disabled': {
                            bgcolor: 'action.disabledBackground',
                          },
                        }}
                      >
                        <RestoreIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="تحميل">
                      <IconButton
                        size="small"
                        onClick={() => downloadBackup(backup)}
                        color="info"
                        sx={{
                          bgcolor: 'action.hover',
                          '&:hover': {
                            bgcolor: 'info.light',
                            color: 'info.contrastText',
                          },
                        }}
                      >
                        <DownloadIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="حذف">
                      <IconButton
                        size="small"
                        onClick={() => deleteBackup(backup)}
                        color="error"
                        sx={{
                          bgcolor: 'action.hover',
                          '&:hover': {
                            bgcolor: 'error.light',
                            color: 'error.contrastText',
                          },
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}

          {isRestoring && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress />
              <Typography variant="caption" color="text.secondary">
                جاري استعادة النسخة الاحتياطية...
              </Typography>
            </Box>
          )}
        </Box>

        {/* Warning */}
        <Alert severity="warning" sx={{ mt: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            تنبيه مهم:
          </Typography>
          <Typography variant="body2">
            • استعادة النسخة الاحتياطية ستستبدل جميع البيانات الحالية
            <br />
            • تأكد من إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
            <br />• النسخ الاحتياطية محفوظة محلياً في متصفحك فقط
          </Typography>
        </Alert>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>إغلاق</Button>
      </DialogActions>
    </Dialog>
  );
};

export default DropdownBackupManager;

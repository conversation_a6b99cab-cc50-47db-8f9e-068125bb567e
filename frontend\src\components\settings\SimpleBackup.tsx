import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Alert,
  List,
  ListItem,
  ListItemText,
  Chip,
} from '@mui/material';
import { Download } from '@mui/icons-material';

interface TableInfo {
  key: string;
  name: string;
  recordCount: number;
}

const SimpleBackup: React.FC = () => {
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string>('');

  useEffect(() => {
    loadTables();
  }, []);

  const loadTables = async () => {
    setLoading(true);
    setMessage('جاري تحميل قائمة الجداول...');
    
    try {
      const response = await fetch('http://localhost:3001/api/backup/tables');
      
      if (!response.ok) {
        throw new Error(`خطأ HTTP: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success && Array.isArray(result.data)) {
        setTables(result.data);
        setMessage(`✅ تم تحميل ${result.data.length} جدول بنجاح`);
      } else {
        throw new Error('البيانات المستلمة غير صحيحة');
      }
    } catch (error) {
      console.error('خطأ في تحميل الجداول:', error);
      setMessage(`❌ فشل في تحميل الجداول: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const exportTable = async (tableKey: string, format: 'excel' | 'json') => {
    setLoading(true);
    setMessage(`جاري تصدير ${tableKey} بصيغة ${format}...`);
    
    try {
      const response = await fetch(
        `http://localhost:3001/api/backup/export/${tableKey}?format=${format}`
      );
      
      if (!response.ok) {
        throw new Error(`خطأ HTTP: ${response.status}`);
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${tableKey}_${new Date().toISOString().slice(0, 10)}.${format === 'excel' ? 'xlsx' : 'json'}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      setMessage(`✅ تم تصدير ${tableKey} بنجاح`);
    } catch (error) {
      console.error('خطأ في التصدير:', error);
      setMessage(`❌ فشل في تصدير ${tableKey}: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        نظام النسخ الاحتياطية المبسط
      </Typography>

      {message && (
        <Alert 
          severity={message.includes('✅') ? 'success' : message.includes('❌') ? 'error' : 'info'}
          sx={{ mb: 2 }}
        >
          {message}
        </Alert>
      )}

      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              الجداول المتاحة ({tables.length})
            </Typography>
            <Button 
              variant="outlined" 
              onClick={loadTables}
              disabled={loading}
            >
              تحديث القائمة
            </Button>
          </Box>

          {tables.length === 0 ? (
            <Typography color="textSecondary">
              {loading ? 'جاري التحميل...' : 'لا توجد جداول متاحة'}
            </Typography>
          ) : (
            <List>
              {tables.map((table) => (
                <ListItem key={table.key} divider>
                  <ListItemText
                    primary={table.name}
                    secondary={`المفتاح: ${table.key}`}
                  />
                  <Chip 
                    label={`${table.recordCount} سجل`} 
                    size="small" 
                    sx={{ mr: 2 }}
                  />
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<Download />}
                    onClick={() => exportTable(table.key, 'excel')}
                    disabled={loading}
                    sx={{ mr: 1 }}
                  >
                    Excel
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Download />}
                    onClick={() => exportTable(table.key, 'json')}
                    disabled={loading}
                  >
                    JSON
                  </Button>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default SimpleBackup;

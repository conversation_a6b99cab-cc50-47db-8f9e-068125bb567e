# نظام إدارة النسخ الاحتياطية

## نظرة عامة

نظام إدارة النسخ الاحتياطية هو جزء من نظام إدارة المزارع يوفر إمكانيات شاملة لتصدير واستيراد بيانات قاعدة البيانات. يدعم النظام تصدير البيانات بصيغ Excel و JSON مع إمكانية اختيار الجداول المطلوبة.

## الميزات الرئيسية

### 1. تصدير البيانات
- **تصدير Excel**: تصدير البيانات بصيغة .xlsx مع دعم عدة أوراق عمل
- **تصدير JSON**: تصدير البيانات بصيغة JSON منظمة
- **اختيار الجداول**: إمكانية اختيار جداول محددة للتصدير
- **معلومات الجداول**: عرض عدد السجلات لكل جدول

### 2. استيراد البيانات
- **دعم ملفات Excel و JSON**: استيراد البيانات من ملفات .xlsx و .json
- **التحقق من صحة البيانات**: فحص البيانات قبل الاستيراد
- **معاينة البيانات**: عرض عينة من البيانات المستوردة
- **تقارير الأخطاء والتحذيرات**: عرض تفصيلي للمشاكل المحتملة

### 3. واجهة المستخدم
- **واجهة سهلة الاستخدام**: تصميم بديهي باللغة العربية
- **رسائل تأكيد**: تأكيدات واضحة لجميع العمليات
- **شريط التقدم**: مؤشرات بصرية لحالة العمليات
- **معالجة الأخطاء**: رسائل خطأ واضحة ومفيدة

## الجداول المدعومة

النظام يدعم تصدير واستيراد الجداول التالية:

1. **الحيوانات** (animals)
   - معرف داخلي، رقم العلامة، نوع الحيوان، السلالة
   - الجنس، الفئة، تاريخ الميلاد، الوزن الحالي
   - الحالة، موقع الحظيرة

2. **المواليد** (births)
   - معرف الأم والأب، تاريخ الولادة، نوع الولادة
   - عدد المواليد، الحالة، الموسم

3. **الموظفين** (employees)
   - الاسم، رقم الهوية، المنصب، الراتب الشهري
   - تاريخ التوظيف، الحالة، رقم الهاتف

4. **المبيعات** (sales)
   - تاريخ البيع، نوع البيع، السعر الإجمالي
   - اسم العميل، طريقة الدفع، الملاحظات

5. **المشتريات** (purchases)
   - النوع، الوصف، الكمية، سعر الوحدة
   - السعر الإجمالي، المورد، تاريخ الشراء

6. **المصروفات** (expenses)
   - الوصف، الفئة، المبلغ، التاريخ
   - طريقة الدفع

7. **العلاجات** (treatments)
   - معرف الحيوان، نوع المرض، الدواء، الجرعة
   - تاريخ العلاج، الطبيب البيطري، التكلفة

8. **سجلات الأوزان** (weight_records)
   - معرف الحيوان، الوزن، التاريخ، الملاحظات

## API Endpoints

### 1. الحصول على قائمة الجداول
```
GET /api/backup/tables
```

**الاستجابة:**
```json
{
  "success": true,
  "data": [
    {
      "key": "animals",
      "name": "الحيوانات",
      "recordCount": 150
    }
  ]
}
```

### 2. تصدير البيانات
```
GET /api/backup/export/:tables?format=excel|json
```

**المعاملات:**
- `tables`: قائمة الجداول مفصولة بفواصل (مثل: animals,births,employees)
- `format`: صيغة التصدير (excel أو json)

**مثال:**
```
GET /api/backup/export/animals,births?format=excel
```

### 3. التحقق من صحة ملف الاستيراد
```
POST /api/backup/validate
Content-Type: multipart/form-data

file: [ملف Excel أو JSON]
```

**الاستجابة:**
```json
{
  "success": true,
  "validation": {
    "isValid": true,
    "recordCount": 50,
    "errors": [],
    "warnings": ["لا يوجد حقل ID في البيانات"],
    "preview": [...]
  }
}
```

### 4. استيراد البيانات
```
POST /api/backup/import
Content-Type: multipart/form-data

file: [ملف Excel أو JSON]
tableName: [اسم الجدول]
replaceExisting: [true|false]
```

**الاستجابة:**
```json
{
  "success": true,
  "recordsImported": 50,
  "message": "تم استيراد 50 سجل بنجاح"
}
```

## كيفية الاستخدام

### تصدير البيانات

1. انتقل إلى صفحة الإعدادات
2. اختر تبويب "النسخ الاحتياطية لقاعدة البيانات"
3. حدد الجداول المطلوب تصديرها
4. اختر صيغة التصدير (Excel أو JSON)
5. انقر على زر التصدير
6. سيتم تنزيل الملف تلقائياً

### استيراد البيانات

1. انتقل إلى صفحة الإعدادات
2. اختر تبويب "النسخ الاحتياطية لقاعدة البيانات"
3. انقر على "اختيار ملف للاستيراد"
4. حدد ملف Excel أو JSON
5. راجع معاينة البيانات والتحذيرات
6. انقر على "استيراد البيانات" للتأكيد

## متطلبات الملفات

### ملفات Excel
- يجب أن تكون بصيغة .xlsx
- الصف الأول يجب أن يحتوي على أسماء الأعمدة
- البيانات يجب أن تبدأ من الصف الثاني

### ملفات JSON
- يجب أن تكون بصيغة JSON صحيحة
- يمكن أن تحتوي على مصفوفة من الكائنات أو كائن واحد
- أسماء الحقول يجب أن تتطابق مع أعمدة قاعدة البيانات

## الأمان والتحقق

- **التحقق من نوع الملف**: يتم قبول ملفات .xlsx و .json فقط
- **حد حجم الملف**: الحد الأقصى 50 ميجابايت
- **التحقق من صحة البيانات**: فحص شامل للبيانات قبل الاستيراد
- **معالجة الأخطاء**: رسائل خطأ واضحة ومفيدة

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق الدعم الفني.

## التحديثات المستقبلية

- دعم المزيد من صيغ الملفات (CSV, XML)
- جدولة النسخ الاحتياطية التلقائية
- ضغط الملفات الكبيرة
- تشفير البيانات الحساسة
- سجل تفصيلي لعمليات الاستيراد والتصدير

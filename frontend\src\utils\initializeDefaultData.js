// أداة لإعادة تعيين وتحميل جميع البيانات الافتراضية للقوائم المنسدلة

export const initializeAllDefaultData = () => {
  console.log('🔄 بدء تحميل البيانات الافتراضية...');

  // 1. المناصب الوظيفية
  const defaultPositions = [
    {
      id: '1',
      name: 'مدير المزرعة',
      nameEn: 'Farm Manager',
      icon: '👨‍💼',
      active: true,
      order: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'راعي',
      nameEn: 'Shepherd',
      icon: '🐑',
      active: true,
      order: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '3',
      name: 'طبيب بيطري',
      nameEn: 'Veterinarian',
      icon: '⚕️',
      active: true,
      order: 2,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '4',
      name: 'عامل تنظيف',
      nameEn: 'Cleaner',
      icon: '🧹',
      active: true,
      order: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '5',
      name: 'حارس أمن',
      nameEn: 'Security Guard',
      icon: '🛡️',
      active: true,
      order: 4,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '6',
      name: 'محاسب',
      nameEn: 'Accountant',
      icon: '💰',
      active: true,
      order: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  // 2. أنواع البدلات
  const defaultAllowances = [
    {
      id: '1',
      name: 'بدل سكن',
      nameEn: 'Housing Allowance',
      icon: '🏠',
      active: true,
      type: 'fixed',
      defaultAmount: 500,
      order: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'بدل مواصلات',
      nameEn: 'Transportation Allowance',
      icon: '🚗',
      active: true,
      type: 'fixed',
      defaultAmount: 300,
      order: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '3',
      name: 'بدل خبرة',
      nameEn: 'Experience Allowance',
      icon: '🎓',
      active: true,
      type: 'fixed',
      defaultAmount: 1000,
      order: 2,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '4',
      name: 'بدل طبيعة عمل',
      nameEn: 'Nature of Work Allowance',
      icon: '⚡',
      active: true,
      type: 'percentage',
      defaultAmount: 15,
      order: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '5',
      name: 'بدل إشراف',
      nameEn: 'Supervision Allowance',
      icon: '👨‍💼',
      active: true,
      type: 'fixed',
      defaultAmount: 800,
      order: 4,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '6',
      name: 'بدل هاتف',
      nameEn: 'Phone Allowance',
      icon: '📱',
      active: true,
      type: 'fixed',
      defaultAmount: 200,
      order: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  // 3. أنواع الخصومات
  const defaultDeductions = [
    {
      id: '1',
      name: 'تأمينات اجتماعية',
      nameEn: 'Social Insurance',
      icon: '🛡️',
      active: true,
      type: 'percentage',
      defaultAmount: 10,
      order: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'ضريبة دخل',
      nameEn: 'Income Tax',
      icon: '💸',
      active: true,
      type: 'percentage',
      defaultAmount: 5,
      order: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '3',
      name: 'قرض شخصي',
      nameEn: 'Personal Loan',
      icon: '💰',
      active: true,
      type: 'fixed',
      defaultAmount: 500,
      order: 2,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '4',
      name: 'غياب بدون إذن',
      nameEn: 'Unauthorized Absence',
      icon: '❌',
      active: true,
      type: 'fixed',
      defaultAmount: 100,
      order: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '5',
      name: 'تأخير',
      nameEn: 'Late Arrival',
      icon: '⏰',
      active: true,
      type: 'fixed',
      defaultAmount: 50,
      order: 4,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  // 4. أنواع الحيوانات
  const defaultAnimalTypes = [
    {
      id: '1',
      name: 'أغنام',
      nameEn: 'Sheep',
      icon: '🐑',
      active: true,
      order: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'ماعز',
      nameEn: 'Goats',
      icon: '🐐',
      active: true,
      order: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '3',
      name: 'أبقار',
      nameEn: 'Cattle',
      icon: '🐄',
      active: true,
      order: 2,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '4',
      name: 'جمال',
      nameEn: 'Camels',
      icon: '🐪',
      active: true,
      order: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  // 5. السلالات
  const defaultBreeds = [
    {
      id: '1',
      name: 'نجدي',
      nameEn: 'Najdi',
      icon: '🐑',
      active: true,
      animalTypeId: '1',
      order: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'حري',
      nameEn: 'Harri',
      icon: '🐑',
      active: true,
      animalTypeId: '1',
      order: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '3',
      name: 'عارضي',
      nameEn: 'Ardi',
      icon: '🐐',
      active: true,
      animalTypeId: '2',
      order: 2,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '4',
      name: 'شامي',
      nameEn: 'Damascus',
      icon: '🐐',
      active: true,
      animalTypeId: '2',
      order: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '5',
      name: 'هولشتاين',
      nameEn: 'Holstein',
      icon: '🐄',
      active: true,
      animalTypeId: '3',
      order: 4,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '6',
      name: 'مجاهيم',
      nameEn: 'Magaheem',
      icon: '🐪',
      active: true,
      animalTypeId: '4',
      order: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  // حفظ جميع البيانات
  const dataToSave = {
    employeePositions: defaultPositions,
    employeeAllowances: defaultAllowances,
    employeeDeductions: defaultDeductions,
    animalTypes: defaultAnimalTypes,
    breeds: defaultBreeds,
  };

  Object.entries(dataToSave).forEach(([key, data]) => {
    localStorage.setItem(key, JSON.stringify(data));
    console.log(`✅ تم حفظ ${key}: ${data.length} عنصر`);
  });

  console.log('🎉 تم تحميل جميع البيانات الافتراضية بنجاح!');
  
  // إعادة تحميل الصفحة لتطبيق التغييرات
  window.location.reload();
};

// دالة لمسح جميع البيانات وإعادة التعيين
export const resetAllData = () => {
  const keys = [
    'employeePositions',
    'employeeAllowances', 
    'employeeDeductions',
    'animalTypes',
    'breeds',
    'animalCategories',
    'animalLocations',
    'birthTypes',
    'birthStatuses',
    'birthComplications',
    'birthSeasons',
    'expenseCategories',
    'paymentMethods',
    'salesMethods',
    'productCategories',
    'measurementUnits',
    'customerTypes'
  ];

  keys.forEach(key => {
    localStorage.removeItem(key);
    console.log(`🗑️ تم مسح ${key}`);
  });

  console.log('🔄 تم مسح جميع البيانات. سيتم إعادة تحميل البيانات الافتراضية...');
  
  // إعادة تحميل البيانات الافتراضية
  setTimeout(() => {
    initializeAllDefaultData();
  }, 1000);
};

// تصدير الدوال للاستخدام
window.initializeDefaultData = initializeAllDefaultData;
window.resetAllData = resetAllData;

import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useTranslation } from 'react-i18next';

const Feeds: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        {t('feeds')}
      </Typography>
      
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          صفحة الأعلاف قيد التطوير
        </Typography>
        <Typography variant="body1" sx={{ mt: 2 }}>
          ستتضمن هذه الصفحة إدارة أنواع الأعلاف والمشتريات والاستهلاك
        </Typography>
      </Paper>
    </Box>
  );
};

export default Feeds;

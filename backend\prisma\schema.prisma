// Farm Management System - Prisma Schema
// نظام إدارة المزرعة - مخطط قاعدة البيانات

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./farm.db"
}

// جدول أنواع الحيوانات
model AnimalType {
  id          String   @id @default(cuid())
  name        String   @unique // غنم، ماعز
  nameAr      String   // الاسم بالعربية
  nameEn      String   // الاسم بالإنجليزية
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  breeds      Breed[]
  animals     Animal[]
  reproductionStages ReproductionStage[]
  reproductionCycles ReproductionCycle[]

  @@map("animal_types")
}

// جدول السلالات
model Breed {
  id            String      @id @default(cuid())
  name          String
  nameAr        String
  nameEn        String
  animalTypeId  String
  description   String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  animalType    AnimalType  @relation(fields: [animalTypeId], references: [id])
  animals       Animal[]

  @@map("breeds")
}

// جدول الحيوانات الرئيسي
model Animal {
  id                String        @id @default(cuid())
  internalId        String        @unique // الرقم التعريفي الداخلي
  tagNumber         String?       // رقم التاغ (قابل لإعادة الاستخدام)
  animalTypeId      String        // نوع الحيوان
  breedId           String        // السلالة
  gender            Gender        // الجنس
  category          AnimalCategory // الفئة
  source            AnimalSource  // المصدر
  birthDate         DateTime?     // تاريخ الميلاد
  birthWeight       Float?        // الوزن عند الولادة
  currentWeight     Float?        // الوزن الحالي
  status            AnimalStatus  // الحالة
  barnLocation      String?       // موقع الحظيرة
  notes             String?       // ملاحظات
  isEligibleForSacrifice Boolean @default(false) // مؤهل للأضحية
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  animalType        AnimalType    @relation(fields: [animalTypeId], references: [id])
  breed             Breed         @relation(fields: [breedId], references: [id])

  // العلاقات
  motherId          String?       // معرف الأم
  mother            Animal?       @relation("MotherOffspring", fields: [motherId], references: [id])
  offspring         Animal[]      @relation("MotherOffspring")

  fatherId          String?       // معرف الفحل
  father            Animal?       @relation("FatherOffspring", fields: [fatherId], references: [id])
  fatherOffspring   Animal[]      @relation("FatherOffspring")

  // السجلات المرتبطة
  treatments        Treatment[]
  movements         AnimalMovement[]
  saleAnimals       SaleAnimal[]
  reproductionCyclesAsMother ReproductionCycle[] @relation("MotherCycles")
  reproductionCyclesAsFather ReproductionCycle[] @relation("FatherCycles")
  births            Birth[]       @relation("MotherBirths")
  fatherBirths      Birth[]       @relation("FatherBirths")
  weightRecords     WeightRecord[]
  fatteningPrograms FatteningAnimal[]

  @@map("animals")
}

// تعدادات الحيوانات
enum Gender {
  MALE    // ذكر
  FEMALE  // أنثى
}

enum AnimalCategory {
  MOTHER      // أم
  FATHER      // فحل
  NEWBORN     // مولود
  WEANED      // مفطوم
  FATTENING   // تسمين
  FOR_SALE    // للبيع
}

enum AnimalSource {
  PURCHASED   // شراء
  INTERNAL    // إنتاج داخلي
}

enum AnimalStatus {
  ALIVE       // حية
  DEAD        // نافقة
  SOLD        // مبيعة
}

// جدول سجل الأوزان
model WeightRecord {
  id        String   @id @default(cuid())
  animalId  String
  weight    Float
  date      DateTime
  notes     String?
  createdAt DateTime @default(now())

  animal    Animal   @relation(fields: [animalId], references: [id])

  @@map("weight_records")
}

// جدول الدورة الإنتاجية
model ReproductionCycle {
  id              String              @id @default(cuid())
  cycleNumber     Int                 // رقم الدورة للأم
  animalTypeId    String              // نوع الحيوان
  motherId        String              // الأم
  fatherId        String?             // الفحل
  pregnancyStart  DateTime            // تاريخ بداية الحمل
  status          ReproductionStatus  // حالة الدورة
  totalCost       Float?              // التكلفة الإجمالية
  totalRevenue    Float?              // الإيرادات الإجمالية
  notes           String?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  animalType      AnimalType          @relation(fields: [animalTypeId], references: [id])
  mother          Animal              @relation("MotherCycles", fields: [motherId], references: [id])
  father          Animal?             @relation("FatherCycles", fields: [fatherId], references: [id])

  births          Birth[]
  stageTransitions StageTransition[]
  feedConsumptions FeedConsumption[]

  @@map("reproduction_cycles")
}

enum ReproductionStatus {
  ONGOING     // جارية
  COMPLETED   // مكتملة
  FAILED      // فاشلة
}

// جدول مراحل الدورة الإنتاجية
model ReproductionStage {
  id            String     @id @default(cuid())
  animalTypeId  String     // نوع الحيوان
  stageName     String     // اسم المرحلة
  stageNameAr   String     // الاسم بالعربية
  stageNameEn   String     // الاسم بالإنجليزية
  durationDays  Int        // عدد الأيام
  dailyBarley   Float?     // كمية الشعير اليومية
  dailyAlfalfa  Float?     // كمية البرسيم اليومية
  dailyOther    Float?     // أعلاف أخرى
  isEditable    Boolean    @default(true) // قابل للتعديل
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  animalType    AnimalType @relation(fields: [animalTypeId], references: [id])
  transitions   StageTransition[]

  @@map("reproduction_stages")
}

// جدول انتقالات المراحل
model StageTransition {
  id                    String              @id @default(cuid())
  reproductionCycleId   String
  reproductionStageId   String
  startDate             DateTime
  endDate               DateTime?
  isActive              Boolean             @default(false)
  createdAt             DateTime            @default(now())

  reproductionCycle     ReproductionCycle   @relation(fields: [reproductionCycleId], references: [id])
  reproductionStage     ReproductionStage   @relation(fields: [reproductionStageId], references: [id])

  @@map("stage_transitions")
}

// جدول المواليد
model Birth {
  id                    String              @id @default(cuid())
  reproductionCycleId   String
  motherId              String
  fatherId              String?
  gender                Gender
  birthDate             DateTime
  birthWeight           Float?
  birthType             BirthType           // نوع الولادة
  siblingCount          Int                 @default(1) // عدد الإخوة التوائم
  status                BirthStatus         // الحالة
  weaningDate           DateTime?           // تاريخ الفطام
  notes                 String?
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  reproductionCycle     ReproductionCycle   @relation(fields: [reproductionCycleId], references: [id])
  mother                Animal              @relation("MotherBirths", fields: [motherId], references: [id])
  father                Animal?             @relation("FatherBirths", fields: [fatherId], references: [id])

  @@map("births")
}

enum BirthType {
  SINGLE    // مفرد
  TWIN      // توأم
  TRIPLET   // ثلاثي
  MULTIPLE  // متعدد
}

enum BirthStatus {
  ALIVE     // حي
  DEAD      // نافق
  WEANED    // مفطوم
}

// جدول أنواع الأعلاف
model FeedType {
  id          String   @id @default(cuid())
  name        String   @unique
  nameAr      String
  nameEn      String
  type        FeedCategory // نوع العلف
  weightKg    Float    // الوزن بالكيلو
  pricePerKg  Float    // السعر للكيلو
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  purchases   FeedPurchase[]
  consumptions FeedConsumption[]
  weaningPlans WeaningFeedPlan[]

  @@map("feed_types")
}

enum FeedCategory {
  CONCENTRATE // مركز
  ROUGHAGE    // مالئ
  SUPPLEMENT  // مكمل
}

// جدول مشتريات الأعلاف
model FeedPurchase {
  id          String   @id @default(cuid())
  feedTypeId  String
  quantity    Float    // الكمية
  unit        String   // الوحدة (ربطة، كيس، طن)
  totalWeight Float    // الوزن الإجمالي
  totalCost   Float    // التكلفة الإجمالية
  purchaseDate DateTime
  supplier    String?  // المورد
  notes       String?
  createdAt   DateTime @default(now())

  feedType    FeedType @relation(fields: [feedTypeId], references: [id])

  @@map("feed_purchases")
}

// جدول استهلاك الأعلاف
model FeedConsumption {
  id                    String              @id @default(cuid())
  date                  DateTime
  feedTypeId            String
  quantity              Float               // الكمية
  unit                  String              // الوحدة
  targetCategory        String              // الفئة المستهدفة
  reproductionCycleId   String?             // ارتباط بالدورة الإنتاجية
  fatteningProgramId    String?             // ارتباط ببرنامج التسمين
  notes                 String?
  createdAt             DateTime            @default(now())

  feedType              FeedType            @relation(fields: [feedTypeId], references: [id])
  reproductionCycle     ReproductionCycle?  @relation(fields: [reproductionCycleId], references: [id])
  fatteningProgram      FatteningProgram?   @relation(fields: [fatteningProgramId], references: [id])

  @@map("feed_consumptions")
}

// جدول تغذية المواليد حسب المرحلة
model WeaningFeedPlan {
  id              String   @id @default(cuid())
  ageStage        String   // المرحلة العمرية
  ageStageAr      String   // المرحلة بالعربية
  ageStageEn      String   // المرحلة بالإنجليزية
  approximateWeight Float  // الوزن التقريبي
  feedTypeId      String
  dailyQuantity   Float    // الكمية اليومية
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  feedType        FeedType @relation(fields: [feedTypeId], references: [id])

  @@map("weaning_feed_plans")
}

// جدول برامج التسمين
model FatteningProgram {
  id          String   @id @default(cuid())
  name        String
  description String?
  startDate   DateTime
  endDate     DateTime?
  targetWeight Float?   // الوزن المستهدف
  status      ProgramStatus
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  animals     FatteningAnimal[]
  feedConsumptions FeedConsumption[]

  @@map("fattening_programs")
}

// جدول الحيوانات في برامج التسمين
model FatteningAnimal {
  id                  String            @id @default(cuid())
  fatteningProgramId  String
  animalId            String
  startWeight         Float?
  currentWeight       Float?
  targetWeight        Float?
  joinDate            DateTime
  exitDate            DateTime?
  status              FatteningStatus
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt

  fatteningProgram    FatteningProgram  @relation(fields: [fatteningProgramId], references: [id])
  animal              Animal            @relation(fields: [animalId], references: [id])

  @@map("fattening_animals")
}

enum ProgramStatus {
  ACTIVE      // نشط
  COMPLETED   // مكتمل
  CANCELLED   // ملغي
}

enum FatteningStatus {
  ACTIVE      // نشط
  COMPLETED   // مكتمل
  TRANSFERRED // منقول
  SOLD        // مباع
}

// جدول العلاجات والتحصينات
model Treatment {
  id          String      @id @default(cuid())
  animalId    String
  diseaseType String      // نوع المرض أو التطعيم
  medicine    String      // العلاج المستخدم
  dosage      String      // الجرعة
  date        DateTime    // تاريخ العلاج
  administeredBy String   // من قام به
  isIsolated  Boolean     @default(false) // تم العزل؟
  notes       String?
  cost        Float?      // تكلفة العلاج
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  animal      Animal      @relation(fields: [animalId], references: [id])

  @@map("treatments")
}

// جدول تنقلات الحيوانات
model AnimalMovement {
  id          String   @id @default(cuid())
  animalId    String
  fromLocation String  // من موقع
  toLocation  String   // إلى موقع
  date        DateTime // تاريخ النقل
  reason      String?  // سبب النقل
  notes       String?
  createdAt   DateTime @default(now())

  animal      Animal   @relation(fields: [animalId], references: [id])

  @@map("animal_movements")
}

// جدول الموظفين
model Employee {
  id          String          @id @default(cuid())
  name        String
  idNumber    String          @unique // رقم الهوية
  position    String          // الوظيفة
  monthlySalary Float         // الراتب الشهري
  hireDate    DateTime        // تاريخ التوظيف
  status      EmployeeStatus  // حالة التوظيف
  phone       String?
  address     String?
  notes       String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  salaryRecords SalaryRecord[]

  @@map("employees")
}

enum EmployeeStatus {
  ACTIVE      // نشط
  INACTIVE    // غير نشط
  TERMINATED  // منتهي الخدمة
}

// جدول سجلات الرواتب
model SalaryRecord {
  id          String   @id @default(cuid())
  employeeId  String
  month       Int      // الشهر
  year        Int      // السنة
  baseSalary  Float    // الراتب الأساسي
  bonuses     Float    @default(0) // المكافآت
  deductions  Float    @default(0) // الخصومات
  totalPaid   Float    // المبلغ المدفوع
  payDate     DateTime // تاريخ الدفع
  notes       String?
  createdAt   DateTime @default(now())

  employee    Employee @relation(fields: [employeeId], references: [id])

  @@unique([employeeId, month, year])
  @@map("salary_records")
}

// جدول المبيعات
model Sale {
  id              String     @id @default(cuid())
  saleDate        DateTime   // تاريخ البيع
  saleType        SaleType   // نوع البيع
  totalPrice      Float      // السعر الإجمالي
  additionalCosts Float      @default(0) // مصاريف إضافية
  customerName    String?    // اسم العميل
  customerPhone   String?    // هاتف العميل
  notes           String?
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  animals         SaleAnimal[]

  @@map("sales")
}

enum SaleType {
  MARKET          // سوق
  INDIVIDUAL      // فردي
  SLAUGHTER_DELIVERY // ذبح وتوصيل
}

// جدول الحيوانات المباعة
model SaleAnimal {
  id        String   @id @default(cuid())
  saleId    String
  animalId  String
  price     Float    // سعر الحيوان
  weight    Float?   // الوزن عند البيع
  createdAt DateTime @default(now())

  sale      Sale     @relation(fields: [saleId], references: [id])
  animal    Animal   @relation(fields: [animalId], references: [id])

  @@map("sale_animals")
}

// جدول المشتريات
model Purchase {
  id          String        @id @default(cuid())
  type        PurchaseType  // نوع المشترى
  description String        // وصف المشترى
  quantity    Float?        // الكمية
  unitPrice   Float         // سعر الوحدة
  totalCost   Float         // التكلفة الإجمالية
  purchaseDate DateTime     // تاريخ الشراء
  supplier    String?       // المورد
  notes       String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@map("purchases")
}

enum PurchaseType {
  ANIMALS     // حيوانات جديدة
  EQUIPMENT   // معدات
  TOOLS       // أدوات
  MEDICINE    // أدوية
  MAINTENANCE // صيانة
  OTHER       // أخرى
}

// جدول المصروفات الشهرية
model Expense {
  id          String   @id @default(cuid())
  description String   // وصف المصروف
  category    String   // الفئة
  amount      Float    // المبلغ
  date        DateTime // التاريخ
  partnerId   String?  // مرتبط بشريك (اختياري)
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  partner     Partner? @relation(fields: [partnerId], references: [id])

  @@map("expenses")
}

// جدول الشركاء
model Partner {
  id          String   @id @default(cuid())
  name        String   @unique // اسم الشريك
  phone       String?
  email       String?
  address     String?
  sharePercentage Float // نسبة الشراكة
  joinDate    DateTime // تاريخ الانضمام
  status      PartnerStatus
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  shares      PartnerShare[]
  expenses    Expense[]

  @@map("partners")
}

enum PartnerStatus {
  ACTIVE      // نشط
  INACTIVE    // غير نشط
  TERMINATED  // منتهي الشراكة
}

// جدول توزيع الأرباح والخسائر
model PartnerShare {
  id          String   @id @default(cuid())
  partnerId   String
  period      String   // الفترة (شهر/سنة)
  contributions Float  @default(0) // المساهمات
  profits     Float    @default(0) // الأرباح
  losses      Float    @default(0) // الخسائر
  netAmount   Float    // المبلغ الصافي
  date        DateTime // تاريخ التوزيع
  notes       String?
  createdAt   DateTime @default(now())

  partner     Partner  @relation(fields: [partnerId], references: [id])

  @@map("partner_shares")
}

// جدول التنبيهات
model Alert {
  id          String      @id @default(cuid())
  type        AlertType   // نوع التنبيه
  title       String      // العنوان
  titleAr     String      // العنوان بالعربية
  titleEn     String      // العنوان بالإنجليزية
  message     String      // الرسالة
  messageAr   String      // الرسالة بالعربية
  messageEn   String      // الرسالة بالإنجليزية
  severity    AlertSeverity // مستوى الأهمية
  isRead      Boolean     @default(false) // مقروء؟
  relatedId   String?     // معرف العنصر المرتبط
  relatedType String?     // نوع العنصر المرتبط
  createdAt   DateTime    @default(now())

  @@map("alerts")
}

enum AlertType {
  BIRTH_OVERDUE       // تأخر ولادة
  HIGH_MORTALITY      // ارتفاع معدل النفوق
  LOW_TWINS           // انخفاض عدد التوائم
  HIGH_FEED_CONSUMPTION // استهلاك علف عالي
  LOW_FEED_STOCK      // انخفاض مخزون العلف
  VACCINATION_DUE     // موعد تحصين
  WEIGHT_CONCERN      // قلق بشأن الوزن
  GENERAL             // عام
}

enum AlertSeverity {
  LOW     // منخفض
  MEDIUM  // متوسط
  HIGH    // عالي
  CRITICAL // حرج
}

// جدول سجل المستخدمين (Audit Log)
model AuditLog {
  id          String   @id @default(cuid())
  userId      String?  // معرف المستخدم
  userName    String   // اسم المستخدم
  action      String   // الإجراء المتخذ
  tableName   String   // اسم الجدول
  recordId    String?  // معرف السجل
  oldValues   String?  // القيم القديمة (JSON)
  newValues   String?  // القيم الجديدة (JSON)
  ipAddress   String?  // عنوان IP
  userAgent   String?  // معلومات المتصفح
  createdAt   DateTime @default(now())

  @@map("audit_logs")
}

// جدول إعدادات النظام
model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique // مفتاح الإعداد
  value       String   // القيمة
  description String?  // وصف الإعداد
  category    String   // فئة الإعداد
  isEditable  Boolean  @default(true) // قابل للتعديل
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_settings")
}

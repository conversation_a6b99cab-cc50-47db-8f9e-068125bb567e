import {
    AccountBalance as Account<PERSON><PERSON>ceIcon,
    <PERSON><PERSON> as PetsIcon,
    TrendingDown as TrendingDownIcon,
    TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import {
    Alert,
    Box,
    Card,
    CardContent,
    CircularProgress,
    Grid,
    Paper,
    Typography,
    useTheme,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Bar,
    <PERSON>C<PERSON>,
    CartesianGrid,
    Cell,
    Legend,
    Pie,
    PieChart,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis,
} from 'recharts';
import { apiService } from '../services/api';
import type { DashboardStats } from '../types';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response = await apiService.getDashboardStats();
        setStats(response.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('فشل في تحميل بيانات لوحة التحكم');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const monthlyData = [
    { month: 'يناير', sheep: 170, goats: 60, revenue: 42000, expenses: 25000 },
    { month: 'فبراير', sheep: 175, goats: 62, revenue: 44000, expenses: 26000 },
    { month: 'مارس', sheep: 180, goats: 65, revenue: 45000, expenses: 28000 },
  ];

  const animalDistribution = stats ? [
    { name: t('sheep'), value: stats.totalSheep, color: '#2E7D32' },
    { name: t('goats'), value: stats.totalGoats, color: '#FF8F00' },
  ] : [];

  const StatCard = ({
    title,
    value,
    icon,
    color = 'primary',
    trend,
    isCurrency = false
  }: {
    title: string;
    value: number;
    icon: React.ReactNode;
    color?: 'primary' | 'secondary' | 'success' | 'error';
    trend?: 'up' | 'down';
    isCurrency?: boolean;
  }) => (
    <Card sx={{
      height: '100%',
      background: `linear-gradient(135deg, ${theme.palette[color].main}15 0%, ${theme.palette[color].main}05 100%)`,
      border: `1px solid ${theme.palette[color].main}20`,
      transition: 'all 0.3s ease',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: `0 12px 40px ${theme.palette[color].main}30`,
      }
    }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box
            sx={{
              p: 2,
              borderRadius: 3,
              background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,
              color: 'white',
              mr: 2,
              boxShadow: `0 8px 24px ${theme.palette[color].main}40`,
            }}
          >
            {icon}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h3" component="div" fontWeight="bold" sx={{ mb: 0.5 }}>
              {isCurrency ? `${value.toLocaleString()}` : value.toLocaleString()}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
              {title}
            </Typography>
            {isCurrency && (
              <Typography variant="caption" color="text.secondary">
                ريال سعودي
              </Typography>
            )}
          </Box>
          {trend && (
            <Box sx={{
              color: trend === 'up' ? 'success.main' : 'error.main',
              display: 'flex',
              alignItems: 'center',
              gap: 0.5
            }}>
              {trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          {t('dashboard')}
        </Typography>
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      </Box>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <Box sx={{ p: { xs: 0, sm: 0 } }}>
      {/* Page Header */}
      <Box sx={{
        mb: 4,
        p: 3,
        background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
        borderRadius: 3,
        color: 'white',
        boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',
      }}>
        <Typography variant="h3" component="h1" fontWeight="bold" sx={{ mb: 1 }}>
          {t('dashboard')}
        </Typography>
        <Typography variant="h6" sx={{ opacity: 0.9 }}>
          مرحباً بك في نظام إدارة المزرعة
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('totalAnimals')}
            value={stats.totalAnimals}
            icon={<PetsIcon />}
            color="primary"
            trend="up"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('pregnantAnimals')}
            value={stats.pregnantAnimals}
            icon={<PetsIcon />}
            color="secondary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('monthlyRevenue')}
            value={stats.totalRevenue}
            icon={<AccountBalanceIcon />}
            color="success"
            trend="up"
            isCurrency
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('netProfit')}
            value={stats.netProfit}
            icon={<TrendingUpIcon />}
            color="success"
            trend="up"
            isCurrency
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Monthly Trends */}
        <Grid item xs={12} md={8}>
          <Paper sx={{
            p: 4,
            borderRadius: 3,
            background: theme.palette.background.paper,
            border: `1px solid ${theme.palette.divider}`,
            boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
          }}>
            <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{
                p: 1.5,
                borderRadius: 2,
                background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
                color: 'white',
              }}>
                📊
              </Box>
              <Typography variant="h5" fontWeight="bold">
                الاتجاهات الشهرية
              </Typography>
            </Box>
            <ResponsiveContainer width="100%" height={350}>
              <BarChart data={monthlyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                <XAxis
                  dataKey="month"
                  tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
                />
                <YAxis
                  tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: theme.palette.background.paper,
                    border: `1px solid ${theme.palette.divider}`,
                    borderRadius: 8,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  }}
                />
                <Legend />
                <Bar
                  dataKey="sheep"
                  fill="url(#sheepGradient)"
                  name={t('sheep')}
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="goats"
                  fill="url(#goatsGradient)"
                  name={t('goats')}
                  radius={[4, 4, 0, 0]}
                />
                <defs>
                  <linearGradient id="sheepGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#4caf50" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#4caf50" stopOpacity={0.3}/>
                  </linearGradient>
                  <linearGradient id="goatsGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#ff9800" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#ff9800" stopOpacity={0.3}/>
                  </linearGradient>
                </defs>
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Animal Distribution */}
        <Grid item xs={12} md={4}>
          <Paper sx={{
            p: 4,
            borderRadius: 3,
            background: theme.palette.background.paper,
            border: `1px solid ${theme.palette.divider}`,
            boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
          }}>
            <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{
                p: 1.5,
                borderRadius: 2,
                background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
                color: 'white',
              }}>
                🥧
              </Box>
              <Typography variant="h5" fontWeight="bold">
                توزيع الحيوانات
              </Typography>
            </Box>
            <ResponsiveContainer width="100%" height={350}>
              <PieChart>
                <Pie
                  data={animalDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  stroke="none"
                >
                  {animalDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: theme.palette.background.paper,
                    border: `1px solid ${theme.palette.divider}`,
                    borderRadius: 8,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* API Status */}
      <Box sx={{ mt: 3 }}>
        <Alert severity="success">
          ✅ متصل بـ API - البيانات محدثة من الخادم
        </Alert>
      </Box>
    </Box>
  );
};

export default Dashboard;

import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Checkbox,
  FormControlLabel,
  Grid,
  LinearProgress,
  Typography,
  Chip,
  Divider,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Download as DownloadIcon,
  TableChart as TableIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';
import { DatabaseBackupService } from '../../services/DatabaseBackupService';

interface ExportManagerProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

interface TableInfo {
  key: string;
  name: string;
  nameEn: string;
  icon: string;
  description: string;
  estimatedRecords: number;
}

const ExportManager: React.FC<ExportManagerProps> = ({ onSuccess, onError }) => {
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportFormat, setExportFormat] = useState<'excel' | 'json'>('excel');

  // تعريف الجداول المتاحة للتصدير
  const availableTables: TableInfo[] = [
    {
      key: 'animals',
      name: 'الحيوانات',
      nameEn: 'Animals',
      icon: '🐑',
      description: 'بيانات جميع الحيوانات في المزرعة',
      estimatedRecords: 125,
    },
    {
      key: 'births',
      name: 'المواليد',
      nameEn: 'Births',
      icon: '🐣',
      description: 'سجلات المواليد والولادات',
      estimatedRecords: 45,
    },
    {
      key: 'employees',
      name: 'الموظفين',
      nameEn: 'Employees',
      icon: '👥',
      description: 'بيانات الموظفين والرواتب',
      estimatedRecords: 8,
    },
    {
      key: 'sales',
      name: 'المبيعات',
      nameEn: 'Sales',
      icon: '💰',
      description: 'سجلات المبيعات والعملاء',
      estimatedRecords: 32,
    },
    {
      key: 'purchases',
      name: 'المشتريات',
      nameEn: 'Purchases',
      icon: '🛒',
      description: 'سجلات المشتريات والموردين',
      estimatedRecords: 28,
    },
    {
      key: 'expenses',
      name: 'المصروفات',
      nameEn: 'Expenses',
      icon: '💸',
      description: 'المصروفات والتكاليف التشغيلية',
      estimatedRecords: 156,
    },
    {
      key: 'treatments',
      name: 'العلاجات',
      nameEn: 'Treatments',
      icon: '💊',
      description: 'سجلات العلاجات والتطعيمات',
      estimatedRecords: 89,
    },
    {
      key: 'weight_records',
      name: 'سجلات الأوزان',
      nameEn: 'Weight Records',
      icon: '⚖️',
      description: 'تسجيلات أوزان الحيوانات',
      estimatedRecords: 234,
    },
  ];

  const handleTableToggle = (tableKey: string) => {
    setSelectedTables(prev =>
      prev.includes(tableKey)
        ? prev.filter(key => key !== tableKey)
        : [...prev, tableKey]
    );
  };

  const handleSelectAll = () => {
    if (selectedTables.length === availableTables.length) {
      setSelectedTables([]);
    } else {
      setSelectedTables(availableTables.map(table => table.key));
    }
  };

  const handleExport = async () => {
    if (selectedTables.length === 0) {
      onError('يرجى اختيار جدول واحد على الأقل للتصدير');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      const backupService = new DatabaseBackupService();
      
      // محاكاة تقدم التصدير
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      const result = await backupService.exportTables(selectedTables, exportFormat);
      
      clearInterval(progressInterval);
      setExportProgress(100);

      if (result.success) {
        onSuccess(`تم تصدير ${selectedTables.length} جدول بنجاح. تم حفظ ${result.filesCount} ملف.`);
        
        // إعادة تعيين الحالة بعد ثانيتين
        setTimeout(() => {
          setIsExporting(false);
          setExportProgress(0);
        }, 2000);
      } else {
        throw new Error(result.error || 'فشل في التصدير');
      }
    } catch (error) {
      console.error('Export error:', error);
      onError('حدث خطأ أثناء تصدير البيانات');
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const totalEstimatedRecords = selectedTables.reduce((total, tableKey) => {
    const table = availableTables.find(t => t.key === tableKey);
    return total + (table?.estimatedRecords || 0);
  }, 0);

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <Grid container spacing={3}>
        {/* Export Configuration */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: 'fit-content' }}>
            <CardHeader
              title="إعدادات التصدير"
              titleTypographyProps={{
                variant: 'h6',
                fontWeight: 'bold',
                color: 'primary.main',
              }}
            />
            <CardContent>
              <Box sx={{ mb: 3 }}>
                <FormControl fullWidth>
                  <InputLabel>تنسيق التصدير</InputLabel>
                  <Select
                    value={exportFormat}
                    label="تنسيق التصدير"
                    onChange={(e) => setExportFormat(e.target.value as 'excel' | 'json')}
                  >
                    <MenuItem value="excel">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <TableIcon color="success" />
                        <span>Excel (.xlsx)</span>
                      </Box>
                    </MenuItem>
                    <MenuItem value="json">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <span>📄</span>
                        <span>JSON (.json)</span>
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  ملخص التصدير
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Chip
                    label={`${selectedTables.length} جدول محدد`}
                    color={selectedTables.length > 0 ? 'primary' : 'default'}
                    size="small"
                  />
                  <Chip
                    label={`${totalEstimatedRecords} سجل متوقع`}
                    color={totalEstimatedRecords > 0 ? 'success' : 'default'}
                    size="small"
                  />
                </Box>
              </Box>

              <Button
                fullWidth
                variant="contained"
                startIcon={isExporting ? <CheckIcon /> : <DownloadIcon />}
                onClick={handleExport}
                disabled={isExporting || selectedTables.length === 0}
                sx={{
                  py: 1.5,
                  background: isExporting 
                    ? 'linear-gradient(45deg, #4caf50 30%, #66bb6a 90%)'
                    : 'linear-gradient(45deg, #1e3a8a 30%, #3b82f6 90%)',
                  '&:hover': {
                    background: isExporting
                      ? 'linear-gradient(45deg, #4caf50 30%, #66bb6a 90%)'
                      : 'linear-gradient(45deg, #1e40af 30%, #2563eb 90%)',
                  },
                }}
              >
                {isExporting ? 'جاري التصدير...' : 'بدء التصدير'}
              </Button>

              {isExporting && (
                <Box sx={{ mt: 2 }}>
                  <LinearProgress 
                    variant="determinate" 
                    value={exportProgress}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    {exportProgress}% مكتمل
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Table Selection */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader
              title="اختيار الجداول للتصدير"
              action={
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleSelectAll}
                >
                  {selectedTables.length === availableTables.length ? 'إلغاء الكل' : 'تحديد الكل'}
                </Button>
              }
              titleTypographyProps={{
                variant: 'h6',
                fontWeight: 'bold',
                color: 'primary.main',
              }}
            />
            <CardContent>
              <Grid container spacing={2}>
                {availableTables.map((table) => (
                  <Grid item xs={12} sm={6} key={table.key}>
                    <Card
                      variant="outlined"
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        border: selectedTables.includes(table.key) 
                          ? '2px solid' 
                          : '1px solid',
                        borderColor: selectedTables.includes(table.key)
                          ? 'primary.main'
                          : 'divider',
                        '&:hover': {
                          borderColor: 'primary.main',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                        },
                      }}
                      onClick={() => handleTableToggle(table.key)}
                    >
                      <CardContent sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                          <Typography sx={{ fontSize: '1.5rem' }}>
                            {table.icon}
                          </Typography>
                          <Box sx={{ flexGrow: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={selectedTables.includes(table.key)}
                                    onChange={() => handleTableToggle(table.key)}
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                }
                                label=""
                                sx={{ m: 0 }}
                              />
                              <Typography variant="subtitle2" fontWeight="bold">
                                {table.name}
                              </Typography>
                            </Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              {table.description}
                            </Typography>
                            <Chip
                              label={`${table.estimatedRecords} سجل`}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {selectedTables.length === 0 && (
                <Alert severity="info" sx={{ mt: 2 }}>
                  يرجى اختيار جدول واحد على الأقل لبدء عملية التصدير
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ExportManager;

# نظام التصميم الموحد للمزرعة

## نظرة عامة
هذا النظام يوفر مكونات موحدة وألوان متسقة لجميع صفحات التطبيق.

## المكونات الأساسية

### 1. PageHeader
مكون هيدر موحد لجميع الصفحات

```tsx
import { PageHeader } from '../components';

<PageHeader 
  pageKey="animals" 
  subtitle="إدارة وتتبع جميع الحيوانات في المزرعة"
  isDarkMode={isDarkMode}
/>
```

### 2. SummaryCards
مكون كروت الملخص الموحدة

```tsx
import { SummaryCards } from '../components';

<SummaryCards
  cards={[
    {
      title: 'إجمالي الحيوانات',
      value: 150,
      type: 'total',
      icon: '🐑'
    },
    {
      title: 'موجود',
      value: 120,
      type: 'success',
      icon: '✅'
    }
  ]}
  isDarkMode={isDarkMode}
/>
```

## نظام الألوان

### ألوان الصفحات
- **animals**: أخضر (#4caf50)
- **births**: أزرق (#2196f3)
- **sales**: برتقالي (#ff9800)
- **purchases**: بنفسجي (#9c27b0)
- **treatments**: أحمر (#f44336)
- **settings**: رمادي مزرق (#607d8b)

### ألوان كروت الملخص
- **total**: أزرق
- **success**: أخضر
- **warning**: برتقالي
- **error**: أحمر
- **info**: بنفسجي

## الدوال المساعدة

### getPageHeaderProps
```tsx
const headerProps = getPageHeaderProps('animals', isDarkMode);
```

### getFilterSectionProps
```tsx
const filterProps = getFilterSectionProps(isDarkMode);
```

### getTableContainerProps
```tsx
const tableProps = getTableContainerProps(isDarkMode);
```

## الاستخدام

1. استيراد المكونات:
```tsx
import { PageHeader, SummaryCards, getFilterSectionProps } from '../components';
```

2. استخدام المكونات في الصفحة:
```tsx
const MyPage = () => {
  const { isDarkMode } = useThemeStore();
  
  return (
    <Box>
      <PageHeader pageKey="animals" isDarkMode={isDarkMode} />
      <SummaryCards cards={summaryData} isDarkMode={isDarkMode} />
      {/* باقي المحتوى */}
    </Box>
  );
};
```

## المزايا

1. **التناسق**: جميع الصفحات تستخدم نفس التصميم
2. **سهولة الصيانة**: تحديث واحد يؤثر على جميع الصفحات
3. **الأداء**: مكونات محسنة ومعاد استخدامها
4. **إمكانية الوصول**: دعم للوضع المظلم والفاتح
5. **المرونة**: سهولة التخصيص والتوسع

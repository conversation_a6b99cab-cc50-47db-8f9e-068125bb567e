import {
  CheckCircle,
  Download,
  Error,
  Upload,
  Warning,
} from '@mui/icons-material';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Checkbox,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  FormGroup,
  Grid,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';

interface TableInfo {
  key: string;
  name: string;
  recordCount: number;
}

interface ValidationResult {
  isValid: boolean;
  recordCount: number;
  errors: string[];
  warnings: string[];
  preview: any[];
}

const BackupManagement: React.FC = () => {
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: 'success' | 'error' | 'info';
    text: string;
  } | null>(null);
  const [previewDialog, setPreviewDialog] = useState(false);
  const [validationResult, setValidationResult] =
    useState<ValidationResult | null>(null);
  const [uploadFile, setUploadFile] = useState<File | null>(null);

  useEffect(() => {
    fetchTables();
  }, []);

  const fetchTables = async () => {
    try {
      // استخدام مسار مباشر للاختبار
      const url = 'http://localhost:3001/api/backup/tables';
      console.log('Fetching tables from:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Response data:', data);

      if (data.success && Array.isArray(data.data)) {
        setTables(data.data);
        setMessage({
          type: 'success',
          text: `تم جلب ${data.data.length} جدول بنجاح`,
        });
      } else {
        throw new Error(data.error || 'فشل في جلب البيانات');
      }
    } catch (error) {
      console.error('Error fetching tables:', error);
      setMessage({
        type: 'error',
        text: `فشل في جلب قائمة الجداول: ${error.message}`,
      });
    }
  };

  const handleTableSelection = (tableKey: string) => {
    setSelectedTables((prev) =>
      prev.includes(tableKey)
        ? prev.filter((key) => key !== tableKey)
        : [...prev, tableKey]
    );
  };

  const handleSelectAll = () => {
    if (selectedTables.length === tables.length) {
      setSelectedTables([]);
    } else {
      setSelectedTables(tables.map((table) => table.key));
    }
  };

  const handleExport = async (format: 'excel' | 'json') => {
    if (selectedTables.length === 0) {
      setMessage({
        type: 'error',
        text: 'يرجى اختيار جدول واحد على الأقل للتصدير',
      });
      return;
    }

    setLoading(true);
    try {
      const tablesParam = selectedTables.join(',');
      const response = await fetch(
        `http://localhost:3001/api/backup/export/${tablesParam}?format=${format}`
      );

      if (!response.ok) {
        throw new Error('فشل في تصدير البيانات');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `backup_${new Date().toISOString().slice(0, 10)}.${
        format === 'excel' ? 'xlsx' : 'json'
      }`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setMessage({ type: 'success', text: 'تم تصدير البيانات بنجاح' });
    } catch (error) {
      console.error('Export error:', error);
      setMessage({ type: 'error', text: 'فشل في تصدير البيانات' });
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadFile(file);
      validateFile(file);
    }
  };

  const validateFile = async (file: File) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(
        'http://localhost:3001/api/backup/validate',
        {
          method: 'POST',
          body: formData,
        }
      );

      const data = await response.json();
      if (data.success) {
        setValidationResult(data.validation);
        setPreviewDialog(true);
      } else {
        setMessage({
          type: 'error',
          text: data.error || 'فشل في التحقق من صحة الملف',
        });
      }
    } catch (error) {
      console.error('Validation error:', error);
      setMessage({ type: 'error', text: 'فشل في التحقق من صحة الملف' });
    } finally {
      setLoading(false);
    }
  };

  const handleImport = async (
    tableName: string,
    replaceExisting: boolean = false
  ) => {
    if (!uploadFile) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', uploadFile);
      formData.append('tableName', tableName);
      formData.append('replaceExisting', replaceExisting.toString());

      const response = await fetch('http://localhost:3001/api/backup/import', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      if (data.success) {
        setMessage({ type: 'success', text: data.message });
        setPreviewDialog(false);
        setUploadFile(null);
        fetchTables(); // تحديث عدد السجلات
      } else {
        setMessage({
          type: 'error',
          text: data.error || 'فشل في استيراد البيانات',
        });
      }
    } catch (error) {
      console.error('Import error:', error);
      setMessage({ type: 'error', text: 'فشل في استيراد البيانات' });
    } finally {
      setLoading(false);
    }
  };

  const getValidationIcon = (validation: ValidationResult) => {
    if (!validation.isValid) return <Error color="error" />;
    if (validation.warnings.length > 0) return <Warning color="warning" />;
    return <CheckCircle color="success" />;
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        إدارة النسخ الاحتياطية
      </Typography>

      {message && (
        <Alert
          severity={message.type}
          onClose={() => setMessage(null)}
          sx={{ mb: 2 }}
        >
          {message.text}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* تصدير البيانات */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                تصدير البيانات
              </Typography>

              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedTables.length === tables.length}
                    indeterminate={
                      selectedTables.length > 0 &&
                      selectedTables.length < tables.length
                    }
                    onChange={handleSelectAll}
                  />
                }
                label="تحديد الكل"
              />

              <FormGroup sx={{ mt: 1, mb: 2 }}>
                {tables.map((table) => (
                  <FormControlLabel
                    key={table.key}
                    control={
                      <Checkbox
                        checked={selectedTables.includes(table.key)}
                        onChange={() => handleTableSelection(table.key)}
                      />
                    }
                    label={
                      <Box
                        display="flex"
                        justifyContent="space-between"
                        width="100%"
                      >
                        <span>{table.name}</span>
                        <Chip
                          label={`${table.recordCount} سجل`}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    }
                  />
                ))}
              </FormGroup>

              <Box display="flex" gap={1}>
                <Button
                  variant="contained"
                  startIcon={<Download />}
                  onClick={() => handleExport('excel')}
                  disabled={loading || selectedTables.length === 0}
                  fullWidth
                >
                  تصدير Excel
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Download />}
                  onClick={() => handleExport('json')}
                  disabled={loading || selectedTables.length === 0}
                  fullWidth
                >
                  تصدير JSON
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* استيراد البيانات */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                استيراد البيانات
              </Typography>

              <input
                accept=".xlsx,.json"
                style={{ display: 'none' }}
                id="upload-file"
                type="file"
                onChange={handleFileUpload}
              />
              <label htmlFor="upload-file">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<Upload />}
                  fullWidth
                  disabled={loading}
                >
                  اختيار ملف للاستيراد
                </Button>
              </label>

              {uploadFile && (
                <Box mt={2}>
                  <Typography variant="body2" color="textSecondary">
                    الملف المحدد: {uploadFile.name}
                  </Typography>
                </Box>
              )}

              {loading && <LinearProgress sx={{ mt: 2 }} />}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* معاينة الاستيراد */}
      <Dialog
        open={previewDialog}
        onClose={() => setPreviewDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            {validationResult && getValidationIcon(validationResult)}
            معاينة البيانات المستوردة
          </Box>
        </DialogTitle>
        <DialogContent>
          {validationResult && (
            <>
              <Box mb={2}>
                <Typography variant="body1">
                  عدد السجلات: {validationResult.recordCount}
                </Typography>

                {validationResult.errors.length > 0 && (
                  <Alert severity="error" sx={{ mt: 1 }}>
                    <Typography variant="subtitle2">أخطاء:</Typography>
                    <ul>
                      {validationResult.errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </Alert>
                )}

                {validationResult.warnings.length > 0 && (
                  <Alert severity="warning" sx={{ mt: 1 }}>
                    <Typography variant="subtitle2">تحذيرات:</Typography>
                    <ul>
                      {validationResult.warnings.map((warning, index) => (
                        <li key={index}>{warning}</li>
                      ))}
                    </ul>
                  </Alert>
                )}
              </Box>

              {validationResult.preview.length > 0 && (
                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        {Object.keys(validationResult.preview[0]).map((key) => (
                          <TableCell key={key}>{key}</TableCell>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {validationResult.preview.map((row, index) => (
                        <TableRow key={index}>
                          {Object.values(row).map((value: any, cellIndex) => (
                            <TableCell key={cellIndex}>
                              {typeof value === 'object'
                                ? JSON.stringify(value)
                                : String(value)}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>إلغاء</Button>
          {validationResult?.isValid && (
            <Button
              variant="contained"
              onClick={() => handleImport('animals', false)}
              disabled={loading}
            >
              استيراد البيانات
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BackupManagement;

---
name: 🐛 تقرير خطأ | Bug Report
about: أنشئ تقريراً لمساعدتنا في تحسين النظام
title: '[BUG] '
labels: 'bug'
assignees: ''
---

## 🐛 وصف الخطأ
وصف واضح ومختصر للخطأ.

## 🔄 خطوات إعادة الإنتاج
خطوات لإعادة إنتاج السلوك:
1. اذهب إلى '...'
2. اضغط على '....'
3. مرر إلى '....'
4. شاهد الخطأ

## ✅ السلوك المتوقع
وصف واضح وموجز لما كان متوقعاً أن يحدث.

## 📸 لقطات الشاشة
إذا كان ممكناً، أضف لقطات شاشة لتوضيح المشكلة.

## 🖥️ معلومات النظام
**Desktop (يرجى إكمال المعلومات التالية):**
- OS: [e.g. Windows 10, macOS Big Sur, Ubuntu 20.04]
- Browser: [e.g. Chrome 91, Firefox 89, Safari 14]
- Version: [e.g. 22]

**Smartphone (يرجى إكمال المعلومات التالية):**
- Device: [e.g. iPhone12, Samsung Galaxy S21]
- OS: [e.g. iOS14.1, Android 11]
- Browser: [e.g. Safari, Chrome]
- Version: [e.g. 22]

## 🔧 معلومات التطوير
**إذا كنت مطوراً:**
- Node.js Version: [e.g. 18.17.0]
- npm Version: [e.g. 9.6.7]
- Database: [e.g. SQLite, PostgreSQL]

## 📝 سياق إضافي
أضف أي سياق آخر حول المشكلة هنا.

## 🔍 سجلات الأخطاء
إذا كان متاحاً، أضف سجلات الأخطاء من:
- Browser Console
- Server Logs
- Database Logs

```
أضف سجلات الأخطاء هنا
```

## ⚡ الأولوية
- [ ] منخفضة - مشكلة تجميلية أو تحسين
- [ ] متوسطة - يؤثر على بعض الوظائف
- [ ] عالية - يؤثر على وظائف رئيسية
- [ ] حرجة - يمنع استخدام النظام

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Tab,
  Tabs,
  <PERSON>pography,
  Alert,
  Snackbar,
} from '@mui/material';
import ExportManager from '../../components/database-backup/ExportManager';
import ImportManager from '../../components/database-backup/ImportManager';
import BackupHistory from '../../components/database-backup/BackupHistory';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`backup-tabpanel-${index}`}
      aria-labelledby={`backup-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 0 }}>{children}</Box>}
    </div>
  );
}

const DatabaseBackup: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'warning' | 'info',
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSuccess = (message: string) => {
    setSnackbar({
      open: true,
      message,
      severity: 'success',
    });
  };

  const handleError = (message: string) => {
    setSnackbar({
      open: true,
      message,
      severity: 'error',
    });
  };

  return (
    <Box>
      {/* Page Header */}
      <Box
        sx={{
          mb: 3,
          p: { xs: 2, md: 3 },
          background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)',
          borderRadius: 2,
          color: 'white',
          boxShadow: '0 4px 20px rgba(30, 58, 138, 0.2)',
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          fontWeight="bold"
          sx={{ mb: 1, fontSize: { xs: '1.5rem', md: '2rem' } }}
        >
          💾 النسخ الاحتياطية لقاعدة البيانات
        </Typography>
        <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
          إدارة تصدير واستيراد بيانات قاعدة البيانات بتنسيق Excel
        </Typography>
      </Box>

      {/* Main Content */}
      <Card
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
        }}
      >
        {/* Tabs */}
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: 'background.paper',
            '& .MuiTab-root': {
              fontWeight: 500,
              fontSize: { xs: '0.875rem', md: '1rem' },
              textTransform: 'none',
              minHeight: { xs: 56, md: 64 },
              px: { xs: 2, md: 3 },
              transition: 'all 0.3s ease',
              '&:hover': {
                bgcolor: 'action.hover',
              },
            },
            '& .MuiTabs-indicator': {
              height: 3,
              borderRadius: '3px 3px 0 0',
            },
          }}
        >
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <span>📤</span>
                <span>تصدير البيانات</span>
              </Box>
            }
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <span>📥</span>
                <span>استيراد البيانات</span>
              </Box>
            }
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <span>📋</span>
                <span>تاريخ النسخ الاحتياطية</span>
              </Box>
            }
          />
        </Tabs>

        {/* Tab Panels */}
        <TabPanel value={tabValue} index={0}>
          <ExportManager onSuccess={handleSuccess} onError={handleError} />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <ImportManager onSuccess={handleSuccess} onError={handleError} />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <BackupHistory onSuccess={handleSuccess} onError={handleError} />
        </TabPanel>
      </Card>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DatabaseBackup;

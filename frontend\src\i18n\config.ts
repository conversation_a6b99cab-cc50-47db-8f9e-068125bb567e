import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Translation resources
const resources = {
  ar: {
    translation: {
      // Navigation
      dashboard: 'لوحة التحكم',
      animals: 'الحيوانات',
      reproduction: 'الدورة الإنتاجية',
      feeds: 'الأعلاف',
      treatments: 'العلاجات',
      employees: 'الموظفين',
      sales: 'المبيعات',
      reports: 'التقارير',
      settings: 'الإعدادات',
      
      // Common
      add: 'إضافة',
      edit: 'تعديل',
      delete: 'حذف',
      save: 'حفظ',
      cancel: 'إلغاء',
      search: 'بحث',
      filter: 'تصفية',
      export: 'تصدير',
      import: 'استيراد',
      loading: 'جاري التحميل...',
      noData: 'لا توجد بيانات',
      
      // Animals
      animalId: 'رقم الحيوان',
      tagNumber: 'رقم التاغ',
      animalType: 'نوع الحيوان',
      breed: 'السلالة',
      gender: 'الجنس',
      category: 'الفئة',
      birthDate: 'تاريخ الميلاد',
      weight: 'الوزن',
      status: 'الحالة',
      location: 'الموقع',
      notes: 'ملاحظات',
      
      // Gender
      male: 'ذكر',
      female: 'أنثى',
      
      // Animal Categories
      mother: 'أم',
      father: 'فحل',
      newborn: 'مولود',
      weaned: 'مفطوم',
      fattening: 'تسمين',
      forSale: 'للبيع',
      
      // Animal Status
      alive: 'حية',
      dead: 'نافقة',
      sold: 'مبيعة',
      
      // Animal Types
      sheep: 'أغنام',
      goats: 'ماعز',
      
      // Dashboard
      totalAnimals: 'إجمالي الحيوانات',
      totalSheep: 'إجمالي الأغنام',
      totalGoats: 'إجمالي الماعز',
      pregnantAnimals: 'الحيوانات الحامل',
      newborns: 'المواليد الجديدة',
      monthlyRevenue: 'الإيرادات الشهرية',
      monthlyExpenses: 'المصروفات الشهرية',
      netProfit: 'صافي الربح',
      
      // Feeds
      feedType: 'نوع العلف',
      quantity: 'الكمية',
      unit: 'الوحدة',
      price: 'السعر',
      supplier: 'المورد',
      purchaseDate: 'تاريخ الشراء',
      
      // Treatments
      disease: 'المرض',
      medicine: 'الدواء',
      dosage: 'الجرعة',
      treatmentDate: 'تاريخ العلاج',
      veterinarian: 'الطبيب البيطري',
      
      // Theme
      darkMode: 'الوضع الليلي',
      lightMode: 'الوضع النهاري',
      language: 'اللغة',
      arabic: 'العربية',
      english: 'الإنجليزية',
      
      // Messages
      success: 'تم بنجاح',
      error: 'حدث خطأ',
      confirmDelete: 'هل أنت متأكد من الحذف؟',
      dataUpdated: 'تم تحديث البيانات',
      dataSaved: 'تم حفظ البيانات',
      
      // Farm Management
      farmName: 'اسم المزرعة',
      farmManagement: 'إدارة المزرعة',
      livestockManagement: 'إدارة الثروة الحيوانية',
      
      // Time periods
      today: 'اليوم',
      thisWeek: 'هذا الأسبوع',
      thisMonth: 'هذا الشهر',
      thisYear: 'هذا العام',

      // Additional
      all: 'الكل',
      view: 'عرض',
      actions: 'الإجراءات',
    }
  },
  en: {
    translation: {
      // Navigation
      dashboard: 'Dashboard',
      animals: 'Animals',
      reproduction: 'Reproduction',
      feeds: 'Feeds',
      treatments: 'Treatments',
      employees: 'Employees',
      sales: 'Sales',
      reports: 'Reports',
      settings: 'Settings',
      
      // Common
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      cancel: 'Cancel',
      search: 'Search',
      filter: 'Filter',
      export: 'Export',
      import: 'Import',
      loading: 'Loading...',
      noData: 'No data available',
      
      // Animals
      animalId: 'Animal ID',
      tagNumber: 'Tag Number',
      animalType: 'Animal Type',
      breed: 'Breed',
      gender: 'Gender',
      category: 'Category',
      birthDate: 'Birth Date',
      weight: 'Weight',
      status: 'Status',
      location: 'Location',
      notes: 'Notes',
      
      // Gender
      male: 'Male',
      female: 'Female',
      
      // Animal Categories
      mother: 'Mother',
      father: 'Father',
      newborn: 'Newborn',
      weaned: 'Weaned',
      fattening: 'Fattening',
      forSale: 'For Sale',
      
      // Animal Status
      alive: 'Alive',
      dead: 'Dead',
      sold: 'Sold',
      
      // Animal Types
      sheep: 'Sheep',
      goats: 'Goats',
      
      // Dashboard
      totalAnimals: 'Total Animals',
      totalSheep: 'Total Sheep',
      totalGoats: 'Total Goats',
      pregnantAnimals: 'Pregnant Animals',
      newborns: 'Newborns',
      monthlyRevenue: 'Monthly Revenue',
      monthlyExpenses: 'Monthly Expenses',
      netProfit: 'Net Profit',
      
      // Feeds
      feedType: 'Feed Type',
      quantity: 'Quantity',
      unit: 'Unit',
      price: 'Price',
      supplier: 'Supplier',
      purchaseDate: 'Purchase Date',
      
      // Treatments
      disease: 'Disease',
      medicine: 'Medicine',
      dosage: 'Dosage',
      treatmentDate: 'Treatment Date',
      veterinarian: 'Veterinarian',
      
      // Theme
      darkMode: 'Dark Mode',
      lightMode: 'Light Mode',
      language: 'Language',
      arabic: 'Arabic',
      english: 'English',
      
      // Messages
      success: 'Success',
      error: 'Error',
      confirmDelete: 'Are you sure you want to delete?',
      dataUpdated: 'Data updated',
      dataSaved: 'Data saved',
      
      // Farm Management
      farmName: 'Farm Name',
      farmManagement: 'Farm Management',
      livestockManagement: 'Livestock Management',
      
      // Time periods
      today: 'Today',
      thisWeek: 'This Week',
      thisMonth: 'This Month',
      thisYear: 'This Year',

      // Additional
      all: 'All',
      view: 'View',
      actions: 'Actions',
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'ar', // default language
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;

# 🤝 دليل المساهمة | Contributing Guide

نرحب بمساهماتكم في تطوير نظام إدارة المزرعة! هذا الدليل سيساعدكم على البدء.

## 📋 جدول المحتويات

- [كيفية المساهمة](#كيفية-المساهمة)
- [إعداد البيئة التطويرية](#إعداد-البيئة-التطويرية)
- [معايير الكود](#معايير-الكود)
- [عملية المراجعة](#عملية-المراجعة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح الميزات](#اقتراح-الميزات)

## 🚀 كيفية المساهمة

### 1. Fork المشروع
```bash
# انسخ المشروع إلى حسابك
git clone https://github.com/your-username/farm-management-system.git
cd farm-management-system
```

### 2. إنشاء فرع جديد
```bash
# أنشئ فرع للميزة الجديدة
git checkout -b feature/amazing-feature

# أو للإصلاح
git checkout -b fix/bug-description
```

### 3. تطوير الميزة
- اتبع معايير الكود المحددة
- أضف اختبارات للكود الجديد
- تأكد من أن جميع الاختبارات تمر
- حدث التوثيق إذا لزم الأمر

### 4. Commit التغييرات
```bash
git add .
git commit -m "feat: add amazing feature"
```

### 5. Push للفرع
```bash
git push origin feature/amazing-feature
```

### 6. إنشاء Pull Request
- اذهب إلى GitHub وأنشئ Pull Request
- اكتب وصفاً واضحاً للتغييرات
- اربط أي Issues ذات صلة

## 🛠️ إعداد البيئة التطويرية

### المتطلبات
- Node.js 18+
- npm أو yarn
- Git

### خطوات الإعداد
```bash
# 1. استنساخ المشروع
git clone https://github.com/your-username/farm-management-system.git
cd farm-management-system

# 2. تثبيت التبعيات
npm run install:all

# 3. إعداد قاعدة البيانات
npm run db:setup

# 4. تشغيل التطبيق
npm run dev
```

### متغيرات البيئة
```bash
# انسخ ملفات البيئة النموذجية
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# عدل القيم حسب بيئتك
```

## 📝 معايير الكود

### TypeScript
- استخدم TypeScript في جميع الملفات
- تجنب استخدام `any` إلا عند الضرورة
- اكتب أنواع واضحة ومفصلة

### React
- استخدم Functional Components مع Hooks
- اتبع نمط تسمية PascalCase للمكونات
- استخدم TypeScript interfaces للـ props

### Node.js
- استخدم async/await بدلاً من callbacks
- اكتب error handling شامل
- استخدم middleware للمنطق المشترك

### قاعدة البيانات
- استخدم Prisma للتفاعل مع قاعدة البيانات
- اكتب migrations واضحة
- أضف indexes للاستعلامات المتكررة

### التسمية
```typescript
// المتغيرات والدوال - camelCase
const userName = 'john';
const getUserData = () => {};

// الثوابت - UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';

// المكونات - PascalCase
const UserProfile = () => {};

// الملفات - kebab-case
user-profile.component.tsx
api-service.ts
```

### التعليقات
```typescript
/**
 * جلب بيانات المستخدم من API
 * @param userId معرف المستخدم
 * @returns بيانات المستخدم
 */
async function getUserData(userId: string): Promise<User> {
  // تنفيذ الدالة
}
```

## 🔍 عملية المراجعة

### قبل إرسال Pull Request
- [ ] تأكد من أن الكود يتبع معايير المشروع
- [ ] شغل ESLint و Prettier
- [ ] تأكد من أن جميع الاختبارات تمر
- [ ] اختبر الميزة يدوياً
- [ ] حدث التوثيق إذا لزم الأمر

### معايير المراجعة
- وضوح الكود وقابليته للقراءة
- الأداء والكفاءة
- الأمان
- التوافق مع المعايير
- جودة الاختبارات

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ
- تأكد من أن الخطأ لم يُبلغ عنه مسبقاً
- جرب إعادة إنتاج الخطأ
- جمع معلومات النظام

### معلومات مطلوبة
```markdown
**وصف الخطأ**
وصف واضح ومختصر للخطأ.

**خطوات إعادة الإنتاج**
1. اذهب إلى '...'
2. اضغط على '....'
3. مرر إلى '....'
4. شاهد الخطأ

**السلوك المتوقع**
وصف واضح لما كان متوقعاً أن يحدث.

**لقطات الشاشة**
إذا كان ممكناً، أضف لقطات شاشة لتوضيح المشكلة.

**معلومات النظام:**
- OS: [e.g. Windows 10]
- Browser: [e.g. Chrome 91]
- Node.js Version: [e.g. 18.17.0]
```

## 💡 اقتراح الميزات

### قبل الاقتراح
- تأكد من أن الميزة لم تُقترح مسبقاً
- فكر في الفائدة للمستخدمين
- اعتبر التعقيد والتكلفة

### قالب الاقتراح
```markdown
**هل اقتراحك مرتبط بمشكلة؟**
وصف واضح للمشكلة. مثال: أشعر بالإحباط عندما [...]

**وصف الحل المقترح**
وصف واضح ومختصر لما تريده أن يحدث.

**وصف البدائل**
وصف واضح لأي حلول أو ميزات بديلة فكرت فيها.

**سياق إضافي**
أضف أي سياق أو لقطات شاشة أخرى حول طلب الميزة هنا.
```

## 📚 الموارد المفيدة

- [React Documentation](https://reactjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [Material-UI Documentation](https://mui.com/)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Express.js Guide](https://expressjs.com/en/guide/routing.html)

## 🎯 أولويات التطوير

### عالية الأولوية
- إصلاح الأخطاء الأمنية
- تحسين الأداء
- إصلاح الأخطاء الحرجة

### متوسطة الأولوية
- ميزات جديدة مطلوبة
- تحسين تجربة المستخدم
- تحسين التوثيق

### منخفضة الأولوية
- تحسينات التصميم
- ميزات إضافية
- تحسينات الكود

## 📞 التواصل

- **GitHub Issues**: للأخطاء والاقتراحات
- **GitHub Discussions**: للأسئلة العامة
- **Email**: للمسائل الحساسة

---

**شكراً لمساهمتكم في تطوير نظام إدارة المزرعة! 🙏**

# 🐑 نظام إدارة مزرعة الأغنام والماعز | Farm Management System

نظام متكامل لإدارة مزرعة تربية وإنتاج وتسمين الأغنام والماعز مع واجهة عصرية ودعم ثنائي اللغة (عربي/إنجليزي).

## 🌟 المميزات الرئيسية

### 📊 إدارة شاملة
- **إدارة الحيوانات**: تتبع كامل للأغنام والماعز مع السلالات المختلفة
- **الدورة الإنتاجية**: إدارة دورات التكاثر والحمل والولادة
- **إدارة الأعلاف**: تتبع أنواع الأعلاف والمشتريات والاستهلاك
- **العلاجات والتحصينات**: سجل كامل للعلاجات والأدوية
- **إدارة الموظفين**: تتبع الموظفين والرواتب
- **المبيعات والمشتريات**: إدارة العمليات التجارية
- **التقارير المالية**: تحليل الأرباح والخسائر

### 🎨 واجهة المستخدم
- **تصميم عصري**: واجهة Material-UI حديثة ومتجاوبة
- **دعم ثنائي اللغة**: عربي وإنجليزي مع تبديل فوري
- **الوضع الليلي/النهاري**: تبديل بين الثيمات
- **متوافق مع الجوال**: تصميم متجاوب لجميع الأجهزة

### 📈 التحليلات والتقارير
- **لوحة تحكم تفاعلية**: مؤشرات أداء رئيسية
- **رسوم بيانية**: تصور البيانات بطريقة واضحة
- **تصدير البيانات**: إمكانية التصدير إلى Excel
- **تنبيهات ذكية**: تنبيهات للأحداث المهمة

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** مع TypeScript
- **Material-UI (MUI)** للواجهة
- **React Router** للتنقل
- **Zustand** لإدارة الحالة
- **React i18next** للترجمة
- **Recharts** للرسوم البيانية
- **Axios** للاتصال بـ API

### Backend
- **Node.js** مع Express
- **TypeScript** للأمان في الكود
- **Prisma ORM** لقاعدة البيانات
- **SQLite** كقاعدة بيانات
- **JWT** للمصادقة
- **Helmet** للأمان

## 🚀 التشغيل السريع

### المتطلبات
- Node.js 18+ 
- npm أو yarn

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd fms_vsc
```

### 2. تشغيل Backend
```bash
cd backend
npm install
npm run db:generate
npm run db:push
npm run db:seed
npm start
```

### 3. تشغيل Frontend
```bash
cd frontend
npm install
npm run dev
```

### 4. الوصول للتطبيق
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health

## 📁 هيكل المشروع

```
fms_vsc/
├── backend/                 # خادم Node.js
│   ├── src/
│   │   ├── routes/         # مسارات API
│   │   ├── controllers/    # منطق العمليات
│   │   ├── middleware/     # الوسطاء
│   │   ├── types/          # أنواع TypeScript
│   │   ├── utils/          # أدوات مساعدة
│   │   ├── server.ts       # الخادم الرئيسي
│   │   └── seed.ts         # بيانات أولية
│   ├── prisma/
│   │   └── schema.prisma   # مخطط قاعدة البيانات
│   └── package.json
├── frontend/               # تطبيق React
│   ├── src/
│   │   ├── components/     # مكونات الواجهة
│   │   ├── pages/          # صفحات التطبيق
│   │   ├── store/          # إدارة الحالة
│   │   ├── i18n/           # ملفات الترجمة
│   │   ├── services/       # خدمات API
│   │   ├── types/          # أنواع TypeScript
│   │   └── App.tsx         # التطبيق الرئيسي
│   └── package.json
└── README.md
```

## 🗄️ قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع Prisma ORM وتشمل الجداول التالية:

### الجداول الرئيسية
- **AnimalType**: أنواع الحيوانات (أغنام، ماعز)
- **Breed**: السلالات
- **Animal**: الحيوانات
- **ReproductionCycle**: الدورات الإنتاجية
- **Birth**: المواليد
- **FeedType**: أنواع الأعلاف
- **Treatment**: العلاجات
- **Employee**: الموظفين
- **Sale**: المبيعات
- **Expense**: المصروفات

## 🔧 الأوامر المفيدة

### Backend
```bash
npm run dev          # تشغيل في وضع التطوير
npm run build        # بناء المشروع
npm start            # تشغيل الإنتاج
npm run db:generate  # توليد Prisma Client
npm run db:push      # دفع التغييرات لقاعدة البيانات
npm run db:seed      # إدخال البيانات الأولية
npm run db:studio    # فتح Prisma Studio
```

### Frontend
```bash
npm run dev          # تشغيل في وضع التطوير
npm run build        # بناء للإنتاج
npm run preview      # معاينة البناء
```

## 🌐 API Endpoints

### الصحة والاختبار
- `GET /health` - فحص حالة الخادم
- `GET /api/test` - نقطة اختبار

### الحيوانات
- `GET /api/animals` - جلب جميع الحيوانات
- `GET /api/animals/:id` - جلب حيوان محدد
- `POST /api/animals` - إضافة حيوان جديد
- `PUT /api/animals/:id` - تحديث حيوان
- `DELETE /api/animals/:id` - حذف حيوان

### السلالات
- `GET /api/breeds` - جلب جميع السلالات
- `GET /api/breeds/:id` - جلب سلالة محددة
- `POST /api/breeds` - إضافة سلالة جديدة

## 🎯 الميزات المستقبلية

- [ ] نظام المصادقة والتفويض
- [ ] تطبيق الجوال (React Native)
- [ ] تقارير متقدمة مع PDF
- [ ] نظام الإشعارات
- [ ] تكامل مع أنظمة المحاسبة
- [ ] نسخ احتياطي تلقائي للسحابة
- [ ] تحليلات الذكاء الاصطناعي

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للدعم والاستفسارات، يرجى إنشاء Issue في GitHub.

---

**تم تطويره بـ ❤️ لخدمة مربي الأغنام والماعز**

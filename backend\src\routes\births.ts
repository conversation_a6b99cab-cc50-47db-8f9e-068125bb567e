import { PrismaClient } from '@prisma/client';
import { Router } from 'express';

const router = Router();
const prisma = new PrismaClient();

// GET /api/births - Get all births
router.get('/', async (req, res) => {
  try {
    const births = await prisma.birth.findMany({
      include: {
        mother: {
          include: {
            animalType: true,
            breed: true,
          },
        },
        father: {
          include: {
            animalType: true,
            breed: true,
          },
        },
        reproductionCycle: true,
      },
      orderBy: {
        birthDate: 'desc',
      },
    });

    res.json({
      success: true,
      births,
      count: births.length,
    });
  } catch (error) {
    console.error('Error fetching births:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch births',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/births/:id - Get birth by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const birth = await prisma.birth.findUnique({
      where: { id },
      include: {
        mother: {
          include: {
            animalType: true,
            breed: true,
          },
        },
        father: {
          include: {
            animalType: true,
            breed: true,
          },
        },
        reproductionCycle: true,
      },
    });

    if (!birth) {
      return res.status(404).json({
        success: false,
        error: 'Birth not found',
      });
    }

    return res.json({
      success: true,
      birth,
    });
  } catch (error) {
    console.error('Error fetching birth:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch birth',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// POST /api/births - Create new birth
router.post('/', async (req, res) => {
  try {
    const {
      reproductionCycleId,
      motherId,
      fatherId,
      gender,
      birthDate,
      birthWeight,
      birthType,
      siblingCount,
      status,
      weaningDate,
      notes,
    } = req.body;

    const birth = await prisma.birth.create({
      data: {
        reproductionCycleId,
        motherId,
        fatherId,
        gender,
        birthDate: new Date(birthDate),
        birthWeight,
        birthType,
        siblingCount: siblingCount || 1,
        status,
        weaningDate: weaningDate ? new Date(weaningDate) : null,
        notes,
      },
      include: {
        mother: {
          include: {
            animalType: true,
            breed: true,
          },
        },
        father: {
          include: {
            animalType: true,
            breed: true,
          },
        },
        reproductionCycle: true,
      },
    });

    res.status(201).json({
      success: true,
      birth,
    });
  } catch (error) {
    console.error('Error creating birth:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create birth',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// PUT /api/births/:id - Update birth
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      reproductionCycleId,
      motherId,
      fatherId,
      gender,
      birthDate,
      birthWeight,
      birthType,
      siblingCount,
      status,
      weaningDate,
      notes,
    } = req.body;

    const birth = await prisma.birth.update({
      where: { id },
      data: {
        reproductionCycleId,
        motherId,
        fatherId,
        gender,
        birthDate: birthDate ? new Date(birthDate) : undefined,
        birthWeight,
        birthType,
        siblingCount,
        status,
        weaningDate: weaningDate ? new Date(weaningDate) : null,
        notes,
      },
      include: {
        mother: {
          include: {
            animalType: true,
            breed: true,
          },
        },
        father: {
          include: {
            animalType: true,
            breed: true,
          },
        },
        reproductionCycle: true,
      },
    });

    res.json({
      success: true,
      birth,
    });
  } catch (error) {
    console.error('Error updating birth:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update birth',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// DELETE /api/births/:id - Delete birth
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.birth.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: 'Birth deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting birth:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete birth',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export default router;

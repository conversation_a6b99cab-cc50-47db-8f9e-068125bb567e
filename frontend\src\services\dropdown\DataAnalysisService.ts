/**
 * خدمة تحليل البيانات الموجودة في localStorage واستخراج القيم الفريدة
 * لإنشاء بيانات أولية للقوائم المنسدلة
 */

interface DropdownItem {
  id: string;
  name: string;
  nameEn?: string;
  icon: string;
  active: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
  source: 'extracted' | 'manual';
}

interface ExtractionResult {
  category: string;
  storageKey: string;
  extractedItems: string[];
  existingItems: DropdownItem[];
  newItems: DropdownItem[];
  totalCount: number;
}

class DataAnalysisService {
  // خريطة الأيقونات المناسبة لكل نوع من البيانات
  private iconMaps = {
    // أيقونات أنواع الحيوانات
    animalTypes: {
      غنم: '🐑',
      ماعز: '🐐',
      أبقار: '🐄',
      إبل: '🐪',
      خيول: '🐎',
      دجاج: '🐔',
      بط: '🦆',
      'ديك رومي': '🦃',
      أرانب: '🐰',
      قطط: '🐱',
      كلاب: '🐶',
      خنازير: '🐷',
      default: '🐑',
    },

    // أيقونات السلالات
    breeds: {
      نجدي: '🧬',
      حري: '🧬',
      عارضي: '🧬',
      شامي: '🧬',
      بلدي: '🧬',
      هولندي: '🧬',
      سواكني: '🧬',
      default: '🧬',
    },

    // أيقونات فئات الحيوانات
    animalCategories: {
      أم: '👩',
      فحل: '👨',
      مولود: '🍼',
      تسمين: '📈',
      للبيع: '💰',
      إحلال: '🔄',
      default: '📋',
    },

    // أيقونات المواقع
    locations: {
      حظيرة: '🏠',
      مرعى: '🌿',
      مستودع: '🏢',
      عيادة: '🏥',
      مكتب: '🏢',
      default: '📍',
    },

    // أيقونات المناصب الوظيفية
    positions: {
      راعي: '👨‍🌾',
      بيطري: '👨‍⚕️',
      مشرف: '👨‍💼',
      عامل: '👨‍🔧',
      محاسب: '👨‍💻',
      سائق: '👨‍✈️',
      حارس: '👮‍♂️',
      default: '👤',
    },

    // أيقونات أنواع البدلات
    allowances: {
      سكن: '🏠',
      مواصلات: '🚗',
      خبرة: '🎓',
      طعام: '🍽️',
      هاتف: '📱',
      default: '💰',
    },

    // أيقونات أنواع الخصومات
    deductions: {
      تأمينات: '🛡️',
      غياب: '❌',
      قروض: '💸',
      مخالفات: '⚠️',
      default: '📉',
    },

    // أيقونات تصنيفات المشتريات
    purchaseCategories: {
      أعلاف: '🌾',
      أدوية: '💊',
      معدات: '🔧',
      صيانة: '🛠️',
      وقود: '⛽',
      default: '📦',
    },

    // أيقونات طرق الدفع
    paymentMethods: {
      نقدي: '💵',
      شيك: '📄',
      تحويل: '🏦',
      بطاقة: '💳',
      آجل: '📅',
      default: '💰',
    },

    // أيقونات الموردين
    suppliers: {
      default: '🏢',
    },

    // أيقونات طرق البيع
    salesMethods: {
      جملة: '📦',
      مفرد: '🛍️',
      تجزئة: '🏪',
      مزاد: '🔨',
      default: '💰',
    },

    // أيقونات تصنيفات المنتجات
    productCategories: {
      لباني: '🥛',
      تسمين: '📈',
      إحلال: '🔄',
      منتجات: '📦',
      default: '📋',
    },

    // أيقونات وحدات القياس
    units: {
      رأس: '🐑',
      كيلو: '⚖️',
      طن: '📏',
      لتر: '🥛',
      صندوق: '📦',
      حبة: '⚪',
      default: '📏',
    },
  };

  /**
   * الحصول على أيقونة مناسبة للعنصر
   */
  private getIconForItem(category: string, itemName: string): string {
    const iconMap = this.iconMaps[category as keyof typeof this.iconMaps];
    if (!iconMap) return '📋';

    // البحث عن مطابقة جزئية في اسم العنصر
    for (const [key, icon] of Object.entries(iconMap)) {
      if (
        key !== 'default' &&
        itemName.toLowerCase().includes(key.toLowerCase())
      ) {
        return icon;
      }
    }

    return iconMap.default || '📋';
  }

  /**
   * استخراج القيم الفريدة من مصفوفة من الكائنات
   */
  private extractUniqueValues(records: any[], fieldPath: string): string[] {
    const values = new Set<string>();

    records.forEach((record) => {
      const value = this.getNestedValue(record, fieldPath);
      if (value && typeof value === 'string' && value.trim()) {
        values.add(value.trim());
      } else if (Array.isArray(value)) {
        // للحقول التي تحتوي على مصفوفات (مثل البدلات والخصومات)
        value.forEach((item) => {
          if (typeof item === 'string' && item.trim()) {
            values.add(item.trim());
          } else if (typeof item === 'object' && item.type) {
            values.add(item.type.trim());
          }
        });
      }
    });

    return Array.from(values).filter((v) => v.length > 0);
  }

  /**
   * الحصول على قيمة متداخلة من كائن
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * تحويل القيم المستخرجة إلى عناصر قائمة منسدلة
   */
  private convertToDropdownItems(
    values: string[],
    category: string,
    existingItems: DropdownItem[] = []
  ): DropdownItem[] {
    const existingNames = new Set(
      existingItems.map((item) => item.name.toLowerCase())
    );
    const now = new Date().toISOString();

    return values
      .filter((value) => !existingNames.has(value.toLowerCase()))
      .map((value, index) => ({
        id: `extracted_${Date.now()}_${index}`,
        name: value,
        nameEn: '',
        icon: this.getIconForItem(category, value),
        active: true,
        order: existingItems.length + index,
        createdAt: now,
        updatedAt: now,
        source: 'extracted' as const,
      }));
  }

  /**
   * تحليل بيانات الحيوانات
   */
  private analyzeAnimalsData(): ExtractionResult[] {
    const animalsData = this.getStorageData('animals');
    if (!animalsData.length) return [];

    const results: ExtractionResult[] = [];

    // أنواع الحيوانات
    const animalTypes = this.extractUniqueValues(animalsData, 'animalType');
    const existingAnimalTypes = this.getStorageData('animalTypes');
    const newAnimalTypes = this.convertToDropdownItems(
      animalTypes,
      'animalTypes',
      existingAnimalTypes
    );

    results.push({
      category: 'أنواع الحيوانات',
      storageKey: 'animalTypes',
      extractedItems: animalTypes,
      existingItems: existingAnimalTypes,
      newItems: newAnimalTypes,
      totalCount: animalTypes.length,
    });

    // السلالات
    const breeds = this.extractUniqueValues(animalsData, 'breed');
    const existingBreeds = this.getStorageData('breeds');
    const newBreeds = this.convertToDropdownItems(
      breeds,
      'breeds',
      existingBreeds
    );

    results.push({
      category: 'السلالات',
      storageKey: 'breeds',
      extractedItems: breeds,
      existingItems: existingBreeds,
      newItems: newBreeds,
      totalCount: breeds.length,
    });

    // فئات الحيوانات
    const categories = this.extractUniqueValues(animalsData, 'category');
    const existingCategories = this.getStorageData('animalCategories');
    const newCategories = this.convertToDropdownItems(
      categories,
      'animalCategories',
      existingCategories
    );

    results.push({
      category: 'فئات الحيوانات',
      storageKey: 'animalCategories',
      extractedItems: categories,
      existingItems: existingCategories,
      newItems: newCategories,
      totalCount: categories.length,
    });

    // المواقع
    const locations = this.extractUniqueValues(animalsData, 'location');
    const existingLocations = this.getStorageData('animalLocations');
    const newLocations = this.convertToDropdownItems(
      locations,
      'locations',
      existingLocations
    );

    results.push({
      category: 'المواقع',
      storageKey: 'animalLocations',
      extractedItems: locations,
      existingItems: existingLocations,
      newItems: newLocations,
      totalCount: locations.length,
    });

    return results;
  }

  /**
   * تحليل بيانات الموظفين
   */
  private analyzeEmployeesData(): ExtractionResult[] {
    const employeesData = this.getStorageData('employees');
    if (!employeesData.length) return [];

    const results: ExtractionResult[] = [];

    // المناصب الوظيفية
    const positions = this.extractUniqueValues(employeesData, 'position');
    const existingPositions = this.getStorageData('employeePositions');
    const newPositions = this.convertToDropdownItems(
      positions,
      'positions',
      existingPositions
    );

    results.push({
      category: 'المناصب الوظيفية',
      storageKey: 'employeePositions',
      extractedItems: positions,
      existingItems: existingPositions,
      newItems: newPositions,
      totalCount: positions.length,
    });

    // أنواع البدلات
    const allowances = this.extractUniqueValues(employeesData, 'allowances');
    const existingAllowances = this.getStorageData('employeeAllowances');
    const newAllowances = this.convertToDropdownItems(
      allowances,
      'allowances',
      existingAllowances
    );

    results.push({
      category: 'أنواع البدلات',
      storageKey: 'employeeAllowances',
      extractedItems: allowances,
      existingItems: existingAllowances,
      newItems: newAllowances,
      totalCount: allowances.length,
    });

    // أنواع الخصومات
    const deductions = this.extractUniqueValues(employeesData, 'deductions');
    const existingDeductions = this.getStorageData('employeeDeductions');
    const newDeductions = this.convertToDropdownItems(
      deductions,
      'deductions',
      existingDeductions
    );

    results.push({
      category: 'أنواع الخصومات',
      storageKey: 'employeeDeductions',
      extractedItems: deductions,
      existingItems: existingDeductions,
      newItems: newDeductions,
      totalCount: deductions.length,
    });

    return results;
  }

  /**
   * الحصول على البيانات من localStorage
   */
  private getStorageData(key: string): any[] {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error);
      return [];
    }
  }

  /**
   * حفظ البيانات في localStorage
   */
  private saveStorageData(key: string, data: any[]): boolean {
    try {
      localStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error(`Error saving ${key} to localStorage:`, error);
      return false;
    }
  }

  /**
   * تحليل بيانات المشتريات
   */
  private analyzePurchasesData(): ExtractionResult[] {
    const purchasesData = this.getStorageData('purchases');
    if (!purchasesData.length) return [];

    const results: ExtractionResult[] = [];

    // تصنيفات المشتريات
    const categories = this.extractUniqueValues(purchasesData, 'category');
    const existingCategories = this.getStorageData('purchaseCategories');
    const newCategories = this.convertToDropdownItems(
      categories,
      'purchaseCategories',
      existingCategories
    );

    results.push({
      category: 'تصنيفات المشتريات',
      storageKey: 'purchaseCategories',
      extractedItems: categories,
      existingItems: existingCategories,
      newItems: newCategories,
      totalCount: categories.length,
    });

    // طرق الدفع
    const paymentMethods = this.extractUniqueValues(
      purchasesData,
      'paymentMethod'
    );
    const existingPaymentMethods = this.getStorageData('paymentMethods');
    const newPaymentMethods = this.convertToDropdownItems(
      paymentMethods,
      'paymentMethods',
      existingPaymentMethods
    );

    results.push({
      category: 'طرق الدفع',
      storageKey: 'paymentMethods',
      extractedItems: paymentMethods,
      existingItems: existingPaymentMethods,
      newItems: newPaymentMethods,
      totalCount: paymentMethods.length,
    });

    // الموردين
    const suppliers = this.extractUniqueValues(purchasesData, 'supplier');
    const existingSuppliers = this.getStorageData('suppliers');
    const newSuppliers = this.convertToDropdownItems(
      suppliers,
      'suppliers',
      existingSuppliers
    );

    results.push({
      category: 'الموردين',
      storageKey: 'suppliers',
      extractedItems: suppliers,
      existingItems: existingSuppliers,
      newItems: newSuppliers,
      totalCount: suppliers.length,
    });

    return results;
  }

  /**
   * تحليل بيانات المبيعات
   */
  private analyzeSalesData(): ExtractionResult[] {
    const salesData = this.getStorageData('sales');
    if (!salesData.length) return [];

    const results: ExtractionResult[] = [];

    // طرق البيع
    const salesMethods = this.extractUniqueValues(salesData, 'salesMethod');
    const existingSalesMethods = this.getStorageData('salesMethods');
    const newSalesMethods = this.convertToDropdownItems(
      salesMethods,
      'salesMethods',
      existingSalesMethods
    );

    results.push({
      category: 'طرق البيع',
      storageKey: 'salesMethods',
      extractedItems: salesMethods,
      existingItems: existingSalesMethods,
      newItems: newSalesMethods,
      totalCount: salesMethods.length,
    });

    // تصنيفات المنتجات
    const productCategories = this.extractUniqueValues(
      salesData,
      'productCategory'
    );
    const existingProductCategories = this.getStorageData('productCategories');
    const newProductCategories = this.convertToDropdownItems(
      productCategories,
      'productCategories',
      existingProductCategories
    );

    results.push({
      category: 'تصنيفات المنتجات',
      storageKey: 'productCategories',
      extractedItems: productCategories,
      existingItems: existingProductCategories,
      newItems: newProductCategories,
      totalCount: productCategories.length,
    });

    // وحدات القياس
    const units = this.extractUniqueValues(salesData, 'unit');
    const existingUnits = this.getStorageData('measurementUnits');
    const newUnits = this.convertToDropdownItems(units, 'units', existingUnits);

    results.push({
      category: 'وحدات القياس',
      storageKey: 'measurementUnits',
      extractedItems: units,
      existingItems: existingUnits,
      newItems: newUnits,
      totalCount: units.length,
    });

    return results;
  }

  /**
   * تحليل جميع البيانات واستخراج القوائم المنسدلة
   */
  public analyzeAllData(): {
    animals: ExtractionResult[];
    employees: ExtractionResult[];
    purchases: ExtractionResult[];
    sales: ExtractionResult[];
    summary: {
      totalCategories: number;
      totalExtractedItems: number;
      totalNewItems: number;
    };
  } {
    const animals = this.analyzeAnimalsData();
    const employees = this.analyzeEmployeesData();
    const purchases = this.analyzePurchasesData();
    const sales = this.analyzeSalesData();

    const allResults = [...animals, ...employees, ...purchases, ...sales];

    return {
      animals,
      employees,
      purchases,
      sales,
      summary: {
        totalCategories: allResults.length,
        totalExtractedItems: allResults.reduce(
          (sum, result) => sum + result.totalCount,
          0
        ),
        totalNewItems: allResults.reduce(
          (sum, result) => sum + result.newItems.length,
          0
        ),
      },
    };
  }

  /**
   * حفظ العناصر الجديدة في localStorage
   */
  public saveExtractedData(results: ExtractionResult[]): {
    success: boolean;
    savedCategories: string[];
    errors: string[];
  } {
    const savedCategories: string[] = [];
    const errors: string[] = [];

    results.forEach((result) => {
      if (result.newItems.length > 0) {
        const combinedItems = [...result.existingItems, ...result.newItems];
        const success = this.saveStorageData(result.storageKey, combinedItems);

        if (success) {
          savedCategories.push(result.category);
        } else {
          errors.push(`فشل في حفظ ${result.category}`);
        }
      }
    });

    return {
      success: errors.length === 0,
      savedCategories,
      errors,
    };
  }

  /**
   * إنشاء تقرير تفصيلي للبيانات المستخرجة
   */
  public generateReport(
    analysisResult: ReturnType<typeof this.analyzeAllData>
  ): string {
    let report = '# تقرير تحليل البيانات واستخراج القوائم المنسدلة\n\n';

    report += `## ملخص عام\n`;
    report += `- إجمالي الفئات المحللة: ${analysisResult.summary.totalCategories}\n`;
    report += `- إجمالي العناصر المستخرجة: ${analysisResult.summary.totalExtractedItems}\n`;
    report += `- إجمالي العناصر الجديدة: ${analysisResult.summary.totalNewItems}\n\n`;

    const sections = [
      { title: 'الحيوانات', data: analysisResult.animals },
      { title: 'الموظفين', data: analysisResult.employees },
      { title: 'المشتريات', data: analysisResult.purchases },
      { title: 'المبيعات', data: analysisResult.sales },
    ];

    sections.forEach((section) => {
      if (section.data.length > 0) {
        report += `## ${section.title}\n\n`;
        section.data.forEach((result) => {
          report += `### ${result.category}\n`;
          report += `- العناصر المستخرجة: ${result.totalCount}\n`;
          report += `- العناصر الموجودة مسبقاً: ${result.existingItems.length}\n`;
          report += `- العناصر الجديدة: ${result.newItems.length}\n`;

          if (result.newItems.length > 0) {
            report += `- العناصر الجديدة: ${result.newItems
              .map((item) => `${item.icon} ${item.name}`)
              .join(', ')}\n`;
          }
          report += '\n';
        });
      }
    });

    return report;
  }
}

export default DataAnalysisService;

import {
  Agriculture,
  Brightness4,
  Brightness7,
  Language,
  Menu as MenuIcon,
  Notifications,
} from '@mui/icons-material';
import {
  AppBar,
  Box,
  Button,
  IconButton,
  Toolbar,
  Typography,
  useTheme,
} from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguageStore } from '../../store/languageStore';
import { useThemeStore } from '../../store/themeStore';

interface NavbarProps {
  onMenuClick: () => void;
  sidebarOpen: boolean;
}

const Navbar: React.FC<NavbarProps> = ({ onMenuClick, sidebarOpen }) => {
  const { t } = useTranslation();
  const { isDarkMode, toggleTheme } = useThemeStore();
  const { language, toggleLanguage } = useLanguageStore();
  const theme = useTheme();

  return (
    <AppBar
      position="fixed"
      elevation={0}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        background: isDarkMode
          ? 'linear-gradient(135deg, #1565c0 0%, #1976d2 100%)'
          : 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
        backdropFilter: 'blur(10px)',
        borderBottom: isDarkMode
          ? '1px solid rgba(255,255,255,0.1)'
          : '1px solid rgba(255,255,255,0.1)',
        transition: (theme) =>
          theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
      }}
    >
      <Toolbar sx={{ px: { xs: 2, sm: 3 } }}>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          onClick={onMenuClick}
          edge="start"
          sx={{
            mr: theme.direction === 'rtl' ? 0 : 2,
            ml: theme.direction === 'rtl' ? 2 : 0,
            borderRadius: 2,
            '&:hover': {
              backgroundColor: 'rgba(255,255,255,0.1)',
            },
          }}
        >
          <MenuIcon />
        </IconButton>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mr: theme.direction === 'rtl' ? 0 : 2,
            ml: theme.direction === 'rtl' ? 2 : 0,
          }}
        >
          <Agriculture
            sx={{
              mr: theme.direction === 'rtl' ? 0 : 1,
              ml: theme.direction === 'rtl' ? 1 : 0,
              fontSize: 28,
            }}
          />
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              fontWeight: 700,
              fontSize: '1.25rem',
              textShadow: '0 1px 2px rgba(0,0,0,0.1)',
            }}
          >
            {t('farmManagement')}
          </Typography>
        </Box>

        <Box sx={{ flexGrow: 1 }} />

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Notifications */}
          <IconButton
            color="inherit"
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.1)',
              },
            }}
          >
            <Notifications />
          </IconButton>

          {/* Language Toggle */}
          <Button
            color="inherit"
            startIcon={<Language />}
            onClick={toggleLanguage}
            size="small"
            sx={{
              borderRadius: 2,
              px: 2,
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.1)',
              },
            }}
          >
            {language === 'ar' ? 'EN' : 'ع'}
          </Button>

          {/* Theme Toggle */}
          <IconButton
            color="inherit"
            onClick={toggleTheme}
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.1)',
              },
            }}
          >
            {isDarkMode ? <Brightness7 /> : <Brightness4 />}
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;

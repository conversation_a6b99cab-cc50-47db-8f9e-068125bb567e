const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addSimpleData() {
  try {
    console.log('🌱 إضافة بيانات بسيطة إضافية...');

    // إضافة المزيد من الموظفين
    const moreEmployees = [];
    const employeeData = [
      { name: 'خالد سعد', idNumber: '1111111111', position: 'مساعد راعي', salary: 3500 },
      { name: 'فهد محمد', idNumber: '2222222222', position: 'عامل تنظيف', salary: 3000 },
      { name: 'عبدالله أحمد', idNumber: '3333333333', position: 'سائق', salary: 4000 },
      { name: 'ناصر علي', idNumber: '4444444444', position: 'حارس', salary: 3200 },
      { name: 'محمد سالم', idNumber: '5555555555', position: 'مشرف', salary: 6000 },
      { name: 'علي حسن', idNumber: '6666666666', position: 'بيطري', salary: 8500 },
    ];
    
    for (const emp of employeeData) {
      const employee = await prisma.employee.create({
        data: {
          name: emp.name,
          idNumber: emp.idNumber,
          position: emp.position,
          monthlySalary: emp.salary,
          hireDate: new Date(2023, Math.floor(Math.random() * 12), 1),
          status: 'ACTIVE',
          phone: `050${Math.floor(Math.random() * 9000000) + 1000000}`,
        },
      });
      moreEmployees.push(employee);
    }

    // إضافة المزيد من المبيعات
    const moreSales = [];
    const saleData = [
      { customer: 'شركة الأسواق المركزية', price: 25000, type: 'MARKET' },
      { customer: 'مطعم الأصالة', price: 8000, type: 'SLAUGHTER_DELIVERY' },
      { customer: 'أحمد الخالد', price: 4500, type: 'INDIVIDUAL' },
      { customer: 'شركة التوزيع الحديثة', price: 18000, type: 'MARKET' },
      { customer: 'مزرعة الرياض', price: 12000, type: 'INDIVIDUAL' },
      { customer: 'مطاعم الفخامة', price: 15000, type: 'SLAUGHTER_DELIVERY' },
      { customer: 'سوق الماشية المركزي', price: 35000, type: 'MARKET' },
      { customer: 'عبدالرحمن محمد', price: 6000, type: 'INDIVIDUAL' },
    ];
    
    for (let i = 0; i < saleData.length; i++) {
      const sale = await prisma.sale.create({
        data: {
          saleDate: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          saleType: saleData[i].type,
          totalPrice: saleData[i].price,
          customerName: saleData[i].customer,
          customerPhone: `050${Math.floor(Math.random() * 9000000) + 1000000}`,
          notes: `بيع ${i + 1} - ${saleData[i].type}`,
        },
      });
      moreSales.push(sale);
    }

    // إضافة المزيد من المشتريات
    const morePurchases = [];
    const purchaseData = [
      { type: 'ANIMALS', desc: 'شراء 5 رؤوس أغنام', cost: 15000 },
      { type: 'EQUIPMENT', desc: 'معدات حلب جديدة', cost: 8500 },
      { type: 'TOOLS', desc: 'أدوات صيانة وتنظيف', cost: 2500 },
      { type: 'MEDICINE', desc: 'أدوية وتطعيمات', cost: 1200 },
      { type: 'MAINTENANCE', desc: 'صيانة المولد الكهربائي', cost: 3000 },
      { type: 'OTHER', desc: 'علف مركز عالي الجودة', cost: 6000 },
      { type: 'EQUIPMENT', desc: 'نظام سقي أوتوماتيكي', cost: 12000 },
      { type: 'TOOLS', desc: 'أدوات قص وتشذيب', cost: 1800 },
    ];
    
    for (let i = 0; i < purchaseData.length; i++) {
      const purchase = await prisma.purchase.create({
        data: {
          type: purchaseData[i].type,
          description: purchaseData[i].desc,
          quantity: Math.floor(Math.random() * 50) + 1,
          unitPrice: Math.floor(purchaseData[i].cost / 10),
          totalCost: purchaseData[i].cost,
          purchaseDate: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          supplier: `مورد ${i + 1}`,
          notes: `مشترى رقم ${i + 1}`,
        },
      });
      morePurchases.push(purchase);
    }

    // إضافة المزيد من المصروفات
    const moreExpenses = [];
    const expenseData = [
      { desc: 'فاتورة كهرباء شهر نوفمبر', category: 'كهرباء', amount: 1500 },
      { desc: 'فاتورة مياه شهر نوفمبر', category: 'مياه', amount: 800 },
      { desc: 'صيانة دورية للمعدات', category: 'صيانة', amount: 2200 },
      { desc: 'وقود المولد والآلات', category: 'وقود', amount: 1800 },
      { desc: 'تأمين المزرعة السنوي', category: 'تأمين', amount: 5000 },
      { desc: 'مكافآت نهاية العام', category: 'رواتب', amount: 8000 },
      { desc: 'تنظيف وتطهير الحظائر', category: 'تنظيف', amount: 1200 },
      { desc: 'رسوم حكومية ولوائح', category: 'رسوم', amount: 2500 },
      { desc: 'نقل وشحن البضائع', category: 'نقل', amount: 1600 },
      { desc: 'مصاريف إدارية متنوعة', category: 'إدارية', amount: 900 },
    ];
    
    for (let i = 0; i < expenseData.length; i++) {
      const expense = await prisma.expense.create({
        data: {
          description: expenseData[i].desc,
          category: expenseData[i].category,
          amount: expenseData[i].amount,
          date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          notes: `مصروف رقم ${i + 1}`,
        },
      });
      moreExpenses.push(expense);
    }

    console.log('✅ تم إضافة البيانات الإضافية بنجاح!');
    console.log(`📊 الإحصائيات الجديدة:`);
    console.log(`   - الموظفين الجدد: ${moreEmployees.length}`);
    console.log(`   - المبيعات الجديدة: ${moreSales.length}`);
    console.log(`   - المشتريات الجديدة: ${morePurchases.length}`);
    console.log(`   - المصروفات الجديدة: ${moreExpenses.length}`);

    // عرض الإجمالي الحالي
    const totalCounts = await Promise.all([
      prisma.employee.count(),
      prisma.sale.count(),
      prisma.purchase.count(),
      prisma.expense.count(),
    ]);

    console.log(`\n📈 الإجمالي الحالي:`);
    console.log(`   - إجمالي الموظفين: ${totalCounts[0]}`);
    console.log(`   - إجمالي المبيعات: ${totalCounts[1]}`);
    console.log(`   - إجمالي المشتريات: ${totalCounts[2]}`);
    console.log(`   - إجمالي المصروفات: ${totalCounts[3]}`);

  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addSimpleData();

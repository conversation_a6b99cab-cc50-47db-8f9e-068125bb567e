import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Card<PERSON><PERSON>,
  Typography,
  Button,
  Box,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Divider,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
  Settings as SettingsIcon,
  Link as LinkIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import IconSelector from '../IconSelector';

interface DropdownItem {
  id: string;
  name: string;
  nameEn?: string;
  icon?: string;
  active: boolean;
  order?: number;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

interface CategoryConfig {
  id: string;
  name: string;
  nameEn: string;
  storageKey: string;
  icon: string;
  hasRelations?: boolean;
  relatedTo?: string[];
  parentCategory?: string;
  relationField?: string;
  hasMetadata?: boolean;
  metadataFields?: string[];
}

interface DropdownCategoryManagerProps {
  category: CategoryConfig;
  onUpdate: () => void;
}

const DropdownCategoryManager: React.FC<DropdownCategoryManagerProps> = ({
  category,
  onUpdate,
}) => {
  const { t } = useTranslation();
  
  // States
  const [items, setItems] = useState<DropdownItem[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<DropdownItem | null>(null);
  const [formData, setFormData] = useState<Partial<DropdownItem>>({});
  const [parentItems, setParentItems] = useState<DropdownItem[]>([]);
  const [usageCount, setUsageCount] = useState<Record<string, number>>({});

  // Load data on component mount
  useEffect(() => {
    loadItems();
    loadParentItems();
    calculateUsage();
  }, [category]);

  const loadItems = () => {
    try {
      const savedItems = localStorage.getItem(category.storageKey);
      if (savedItems) {
        const parsedItems = JSON.parse(savedItems);
        setItems(Array.isArray(parsedItems) ? parsedItems : []);
      } else {
        setItems([]);
      }
    } catch (error) {
      console.error('Error loading items:', error);
      setItems([]);
    }
  };

  const loadParentItems = () => {
    if (category.parentCategory) {
      try {
        const parentKey = getParentStorageKey();
        const savedParentItems = localStorage.getItem(parentKey);
        if (savedParentItems) {
          setParentItems(JSON.parse(savedParentItems));
        }
      } catch (error) {
        console.error('Error loading parent items:', error);
      }
    }
  };

  const getParentStorageKey = () => {
    // Map category IDs to storage keys
    const keyMap = {
      'animal_types': 'animalTypes',
      'employee_positions': 'employeePositions',
    };
    return keyMap[category.parentCategory] || category.parentCategory;
  };

  const calculateUsage = () => {
    // Calculate how many times each item is used in other records
    const usage: Record<string, number> = {};
    
    // Check usage in animals, employees, etc.
    const checkStorageKeys = ['animals', 'employees', 'purchases', 'sales'];
    
    checkStorageKeys.forEach(key => {
      try {
        const data = localStorage.getItem(key);
        if (data) {
          const records = JSON.parse(data);
          if (Array.isArray(records)) {
            records.forEach(record => {
              items.forEach(item => {
                if (Object.values(record).includes(item.name) || 
                    Object.values(record).includes(item.id)) {
                  usage[item.id] = (usage[item.id] || 0) + 1;
                }
              });
            });
          }
        }
      } catch (error) {
        console.error(`Error checking usage in ${key}:`, error);
      }
    });
    
    setUsageCount(usage);
  };

  const handleAddItem = () => {
    setEditingItem(null);
    setFormData({
      name: '',
      nameEn: '',
      icon: category.icon,
      active: true,
    });
    setDialogOpen(true);
  };

  const handleEditItem = (item: DropdownItem) => {
    setEditingItem(item);
    setFormData({ ...item });
    setDialogOpen(true);
  };

  const handleDeleteItem = (item: DropdownItem) => {
    const usage = usageCount[item.id] || 0;
    
    if (usage > 0) {
      alert(`لا يمكن حذف هذا العنصر لأنه مستخدم في ${usage} سجل`);
      return;
    }

    if (window.confirm(`هل أنت متأكد من حذف "${item.name}"؟`)) {
      const updatedItems = items.filter(i => i.id !== item.id);
      setItems(updatedItems);
      localStorage.setItem(category.storageKey, JSON.stringify(updatedItems));
      onUpdate();
    }
  };

  const handleSaveItem = () => {
    if (!formData.name?.trim()) {
      alert('يرجى إدخال اسم العنصر');
      return;
    }

    const now = new Date().toISOString();
    let updatedItems: DropdownItem[];

    if (editingItem) {
      // Update existing item
      updatedItems = items.map(item =>
        item.id === editingItem.id
          ? { ...item, ...formData, updatedAt: now }
          : item
      );
    } else {
      // Add new item
      const newItem: DropdownItem = {
        id: Date.now().toString(),
        name: formData.name!,
        nameEn: formData.nameEn || '',
        icon: formData.icon || category.icon,
        active: formData.active !== false,
        order: items.length,
        createdAt: now,
        updatedAt: now,
        ...formData,
      };
      updatedItems = [...items, newItem];
    }

    setItems(updatedItems);
    localStorage.setItem(category.storageKey, JSON.stringify(updatedItems));
    setDialogOpen(false);
    setEditingItem(null);
    setFormData({});
    onUpdate();
  };

  const handleToggleActive = (item: DropdownItem) => {
    const updatedItems = items.map(i =>
      i.id === item.id ? { ...i, active: !i.active } : i
    );
    setItems(updatedItems);
    localStorage.setItem(category.storageKey, JSON.stringify(updatedItems));
    onUpdate();
  };

  const activeItemsCount = items.filter(item => item.active).length;
  const totalItemsCount = items.length;

  return (
    <>
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
          },
        }}
      >
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ fontSize: '1.5rem', mr: 1 }}>
              {category.icon}
            </Typography>
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h6" component="h3" fontWeight="bold">
                {category.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {category.nameEn}
              </Typography>
            </Box>
            {category.hasRelations && (
              <Tooltip title="مرتبط بقوائم أخرى">
                <LinkIcon color="primary" />
              </Tooltip>
            )}
          </Box>

          <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
            <Chip
              label={`${activeItemsCount} نشط`}
              color="success"
              size="small"
              variant="outlined"
            />
            <Chip
              label={`${totalItemsCount} إجمالي`}
              color="primary"
              size="small"
              variant="outlined"
            />
            {category.hasRelations && (
              <Chip
                label="مترابط"
                color="info"
                size="small"
                variant="outlined"
                icon={<LinkIcon />}
              />
            )}
          </Box>

          {items.length > 0 ? (
            <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
              {items.slice(0, 5).map((item) => (
                <Box
                  key={item.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    py: 0.5,
                    borderBottom: '1px solid',
                    borderColor: 'divider',
                    '&:last-child': { borderBottom: 'none' },
                  }}
                >
                  <Typography sx={{ fontSize: '1rem', mr: 1 }}>
                    {item.icon}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      flexGrow: 1,
                      opacity: item.active ? 1 : 0.5,
                      textDecoration: item.active ? 'none' : 'line-through',
                    }}
                  >
                    {item.name}
                  </Typography>
                  {usageCount[item.id] > 0 && (
                    <Badge
                      badgeContent={usageCount[item.id]}
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      <Chip
                        label="مستخدم"
                        size="small"
                        color="warning"
                        variant="outlined"
                      />
                    </Badge>
                  )}
                  <Chip
                    label={item.active ? 'نشط' : 'معطل'}
                    color={item.active ? 'success' : 'default'}
                    size="small"
                    onClick={() => handleToggleActive(item)}
                    sx={{ cursor: 'pointer' }}
                  />
                </Box>
              ))}
              {items.length > 5 && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ display: 'block', textAlign: 'center', mt: 1 }}
                >
                  و {items.length - 5} عنصر آخر...
                </Typography>
              )}
            </Box>
          ) : (
            <Alert severity="info" sx={{ mt: 1 }}>
              لا توجد عناصر في هذه القائمة
            </Alert>
          )}
        </CardContent>

        <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
          <Button
            startIcon={<AddIcon />}
            onClick={handleAddItem}
            variant="contained"
            size="small"
            sx={{ flexGrow: 1, mr: 1 }}
          >
            إضافة
          </Button>
          <Button
            startIcon={<SettingsIcon />}
            onClick={() => setDialogOpen(true)}
            variant="outlined"
            size="small"
          >
            إدارة
          </Button>
        </CardActions>
      </Card>

      {/* Management Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography sx={{ fontSize: '1.5rem' }}>{category.icon}</Typography>
            <Typography variant="h6">
              إدارة {category.name}
            </Typography>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {editingItem || Object.keys(formData).length > 0 ? (
            // Add/Edit Form
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
              <TextField
                label="الاسم بالعربية"
                value={formData.name || ''}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
                fullWidth
              />
              
              <TextField
                label="الاسم بالإنجليزية"
                value={formData.nameEn || ''}
                onChange={(e) => setFormData({ ...formData, nameEn: e.target.value })}
                fullWidth
              />

              {category.parentCategory && (
                <FormControl fullWidth>
                  <InputLabel>الفئة الرئيسية</InputLabel>
                  <Select
                    value={formData[category.relationField] || ''}
                    label="الفئة الرئيسية"
                    onChange={(e) => setFormData({ 
                      ...formData, 
                      [category.relationField]: e.target.value 
                    })}
                  >
                    {parentItems.map((parent) => (
                      <MenuItem key={parent.id} value={parent.id}>
                        {parent.icon} {parent.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  الأيقونة
                </Typography>
                <IconSelector
                  selectedIcon={formData.icon || category.icon}
                  onIconSelect={(icon) => setFormData({ ...formData, icon })}
                />
              </Box>

              {category.hasMetadata && category.metadataFields?.map((field) => (
                <TextField
                  key={field}
                  label={field}
                  value={formData[field] || ''}
                  onChange={(e) => setFormData({ ...formData, [field]: e.target.value })}
                  fullWidth
                />
              ))}
            </Box>
          ) : (
            // Items List
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">
                  العناصر الحالية ({items.length})
                </Typography>
                <Button
                  startIcon={<AddIcon />}
                  onClick={handleAddItem}
                  variant="contained"
                  size="small"
                >
                  إضافة جديد
                </Button>
              </Box>
              
              <List sx={{ maxHeight: 400, overflow: 'auto' }}>
                {items.map((item) => (
                  <ListItem key={item.id} divider>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography sx={{ fontSize: '1.2rem' }}>
                            {item.icon}
                          </Typography>
                          <Typography
                            sx={{
                              opacity: item.active ? 1 : 0.5,
                              textDecoration: item.active ? 'none' : 'line-through',
                            }}
                          >
                            {item.name}
                          </Typography>
                          {item.nameEn && (
                            <Typography variant="body2" color="text.secondary">
                              ({item.nameEn})
                            </Typography>
                          )}
                          <Chip
                            label={item.active ? 'نشط' : 'معطل'}
                            color={item.active ? 'success' : 'default'}
                            size="small"
                            onClick={() => handleToggleActive(item)}
                            sx={{ cursor: 'pointer' }}
                          />
                          {usageCount[item.id] > 0 && (
                            <Chip
                              label={`مستخدم ${usageCount[item.id]} مرة`}
                              color="warning"
                              size="small"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => handleEditItem(item)}
                        sx={{ mr: 1 }}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        edge="end"
                        onClick={() => handleDeleteItem(item)}
                        color="error"
                        disabled={usageCount[item.id] > 0}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            إلغاء
          </Button>
          {(editingItem || Object.keys(formData).length > 0) && (
            <Button onClick={handleSaveItem} variant="contained">
              {editingItem ? 'تحديث' : 'إضافة'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DropdownCategoryManager;

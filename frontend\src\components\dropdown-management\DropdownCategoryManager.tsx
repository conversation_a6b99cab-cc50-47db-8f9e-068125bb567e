import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Link as LinkIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import IconSelector from '../IconSelector';

interface DropdownItem {
  id: string;
  name: string;
  nameEn?: string;
  icon?: string;
  active: boolean;
  order?: number;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

interface CategoryConfig {
  id: string;
  name: string;
  nameEn: string;
  storageKey: string;
  icon: string;
  hasRelations?: boolean;
  relatedTo?: string[];
  parentCategory?: string;
  relationField?: string;
  hasMetadata?: boolean;
  metadataFields?: string[];
}

interface DropdownCategoryManagerProps {
  category: CategoryConfig;
  onUpdate: () => void;
}

const DropdownCategoryManager: React.FC<DropdownCategoryManagerProps> = ({
  category,
  onUpdate,
}) => {
  const { t } = useTranslation();

  // States
  const [items, setItems] = useState<DropdownItem[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<DropdownItem | null>(null);
  const [formData, setFormData] = useState<Partial<DropdownItem>>({});
  const [parentItems, setParentItems] = useState<DropdownItem[]>([]);
  const [usageCount, setUsageCount] = useState<Record<string, number>>({});

  // Load data on component mount
  useEffect(() => {
    loadItems();
    loadParentItems();
    calculateUsage();
  }, [category]);

  const loadItems = () => {
    try {
      const savedItems = localStorage.getItem(category.storageKey);
      if (savedItems) {
        const parsedItems = JSON.parse(savedItems);
        setItems(Array.isArray(parsedItems) ? parsedItems : []);
      } else {
        setItems([]);
      }
    } catch (error) {
      console.error('Error loading items:', error);
      setItems([]);
    }
  };

  const loadParentItems = () => {
    if (category.parentCategory) {
      try {
        const parentKey = getParentStorageKey();
        const savedParentItems = localStorage.getItem(parentKey);
        if (savedParentItems) {
          setParentItems(JSON.parse(savedParentItems));
        }
      } catch (error) {
        console.error('Error loading parent items:', error);
      }
    }
  };

  const getParentStorageKey = () => {
    // Map category IDs to storage keys
    const keyMap = {
      animal_types: 'animalTypes',
      employee_positions: 'employeePositions',
    };
    return keyMap[category.parentCategory] || category.parentCategory;
  };

  const calculateUsage = () => {
    // Calculate how many times each item is used in other records
    const usage: Record<string, number> = {};

    // Check usage in animals, employees, etc.
    const checkStorageKeys = ['animals', 'employees', 'purchases', 'sales'];

    checkStorageKeys.forEach((key) => {
      try {
        const data = localStorage.getItem(key);
        if (data) {
          const records = JSON.parse(data);
          if (Array.isArray(records)) {
            records.forEach((record) => {
              items.forEach((item) => {
                if (
                  Object.values(record).includes(item.name) ||
                  Object.values(record).includes(item.id)
                ) {
                  usage[item.id] = (usage[item.id] || 0) + 1;
                }
              });
            });
          }
        }
      } catch (error) {
        console.error(`Error checking usage in ${key}:`, error);
      }
    });

    setUsageCount(usage);
  };

  const handleAddItem = () => {
    setEditingItem(null);
    setFormData({
      name: '',
      nameEn: '',
      icon: category.icon,
      active: true,
    });
    setDialogOpen(true);
  };

  const handleEditItem = (item: DropdownItem) => {
    setEditingItem(item);
    setFormData({ ...item });
    setDialogOpen(true);
  };

  const handleDeleteItem = (item: DropdownItem) => {
    const usage = usageCount[item.id] || 0;

    if (usage > 0) {
      alert(`لا يمكن حذف هذا العنصر لأنه مستخدم في ${usage} سجل`);
      return;
    }

    if (window.confirm(`هل أنت متأكد من حذف "${item.name}"؟`)) {
      const updatedItems = items.filter((i) => i.id !== item.id);
      setItems(updatedItems);
      localStorage.setItem(category.storageKey, JSON.stringify(updatedItems));
      onUpdate();
    }
  };

  const handleSaveItem = () => {
    if (!formData.name?.trim()) {
      alert('يرجى إدخال اسم العنصر');
      return;
    }

    const now = new Date().toISOString();
    let updatedItems: DropdownItem[];

    if (editingItem) {
      // Update existing item
      updatedItems = items.map((item) =>
        item.id === editingItem.id
          ? { ...item, ...formData, updatedAt: now }
          : item
      );
    } else {
      // Add new item
      const newItem: DropdownItem = {
        id: Date.now().toString(),
        name: formData.name!,
        nameEn: formData.nameEn || '',
        icon: formData.icon || category.icon,
        active: formData.active !== false,
        order: items.length,
        createdAt: now,
        updatedAt: now,
        ...formData,
      };
      updatedItems = [...items, newItem];
    }

    setItems(updatedItems);
    localStorage.setItem(category.storageKey, JSON.stringify(updatedItems));
    setDialogOpen(false);
    setEditingItem(null);
    setFormData({});
    onUpdate();
  };

  const handleToggleActive = (item: DropdownItem) => {
    const updatedItems = items.map((i) =>
      i.id === item.id ? { ...i, active: !i.active } : i
    );
    setItems(updatedItems);
    localStorage.setItem(category.storageKey, JSON.stringify(updatedItems));
    onUpdate();
  };

  const activeItemsCount = items.filter((item) => item.active).length;
  const totalItemsCount = items.length;

  return (
    <>
      <Card
        sx={{
          height: '100%',
          minHeight: { xs: '280px', md: '320px' },
          display: 'flex',
          flexDirection: 'column',
          transition: 'all 0.3s ease',
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2,
          bgcolor: 'background.paper',
          '&:hover': {
            borderColor: 'primary.main',
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
            transform: 'translateY(-2px)',
          },
        }}
      >
        <CardContent sx={{ flexGrow: 1, p: 2 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: 'primary.main',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.2rem',
                mr: 2,
              }}
            >
              {category.icon}
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              <Typography
                variant="h6"
                component="h3"
                fontWeight="bold"
                sx={{ fontSize: '1rem' }}
              >
                {category.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {category.nameEn}
              </Typography>
            </Box>
            {category.hasRelations && (
              <Tooltip title="مرتبط بقوائم أخرى">
                <LinkIcon color="primary" fontSize="small" />
              </Tooltip>
            )}
          </Box>

          {/* Statistics Chips */}
          <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
            <Chip
              label={`${activeItemsCount} نشط`}
              color="success"
              size="small"
              sx={{
                fontSize: '0.75rem',
                height: 24,
                '& .MuiChip-label': { px: 1 },
              }}
            />
            <Chip
              label={`${totalItemsCount} إجمالي`}
              color="primary"
              size="small"
              sx={{
                fontSize: '0.75rem',
                height: 24,
                '& .MuiChip-label': { px: 1 },
              }}
            />
            {category.hasRelations && (
              <Chip
                label="مترابط"
                color="warning"
                size="small"
                icon={<LinkIcon sx={{ fontSize: '0.8rem' }} />}
                sx={{
                  fontSize: '0.75rem',
                  height: 24,
                  '& .MuiChip-label': { px: 1 },
                }}
              />
            )}
          </Box>

          {/* Items Preview */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: 'bold' }}
            >
              العناصر ({items.length})
            </Typography>
            {items.length > 0 ? (
              <Box
                sx={{
                  maxHeight: { xs: 120, md: 140 },
                  overflow: 'auto',
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 1,
                  p: { xs: 0.5, md: 1 },
                  bgcolor: 'background.default',
                  '&::-webkit-scrollbar': {
                    width: '4px',
                  },
                  '&::-webkit-scrollbar-track': {
                    bgcolor: 'transparent',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    bgcolor: 'divider',
                    borderRadius: '2px',
                  },
                }}
              >
                {items.slice(0, 4).map((item) => (
                  <Box
                    key={item.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      py: { xs: 0.25, md: 0.5 },
                      px: { xs: 0.5, md: 1 },
                      borderRadius: 1,
                      mb: 0.5,
                      bgcolor: item.active ? 'transparent' : 'action.hover',
                      border: '1px solid transparent',
                      transition: 'all 0.2s ease',
                      '&:last-child': { mb: 0 },
                      '&:hover': {
                        bgcolor: 'action.selected',
                        borderColor: 'primary.light',
                      },
                    }}
                  >
                    <Typography sx={{ fontSize: '0.9rem', mr: 1 }}>
                      {item.icon}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        flexGrow: 1,
                        opacity: item.active ? 1 : 0.6,
                        textDecoration: item.active ? 'none' : 'line-through',
                        fontSize: '0.85rem',
                      }}
                    >
                      {item.name}
                    </Typography>
                    {usageCount[item.id] > 0 && (
                      <Chip
                        label={usageCount[item.id]}
                        size="small"
                        color="warning"
                        sx={{
                          minWidth: 'auto',
                          height: 18,
                          fontSize: '0.7rem',
                        }}
                      />
                    )}
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: item.active ? 'success.main' : 'grey.400',
                        ml: 1,
                      }}
                    />
                  </Box>
                ))}
                {items.length > 4 && (
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{
                      display: 'block',
                      textAlign: 'center',
                      mt: 1,
                      fontSize: '0.75rem',
                    }}
                  >
                    +{items.length - 4} عنصر آخر
                  </Typography>
                )}
              </Box>
            ) : (
              <Box
                sx={{
                  p: 2,
                  textAlign: 'center',
                  border: '1px dashed',
                  borderColor: 'divider',
                  borderRadius: 1,
                  bgcolor: 'action.hover',
                }}
              >
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontSize: '0.85rem' }}
                >
                  لا توجد عناصر
                </Typography>
              </Box>
            )}
          </Box>
        </CardContent>

        <CardActions sx={{ p: { xs: 1.5, md: 2 }, pt: 0 }}>
          <Box sx={{ display: 'flex', gap: { xs: 0.5, md: 1 }, width: '100%' }}>
            <Button
              startIcon={<AddIcon />}
              onClick={handleAddItem}
              variant="contained"
              size="small"
              sx={{
                flex: 1,
                fontSize: { xs: '0.75rem', md: '0.875rem' },
                py: { xs: 0.5, md: 0.75 },
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                '&:hover': {
                  boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                  transform: 'translateY(-1px)',
                },
                transition: 'all 0.3s ease',
              }}
            >
              إضافة
            </Button>
            <Button
              startIcon={<SettingsIcon />}
              onClick={() => setDialogOpen(true)}
              variant="outlined"
              size="small"
              sx={{
                flex: 1,
                fontSize: { xs: '0.75rem', md: '0.875rem' },
                py: { xs: 0.5, md: 0.75 },
                borderWidth: '1.5px',
                '&:hover': {
                  borderWidth: '1.5px',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                },
                transition: 'all 0.3s ease',
              }}
            >
              إدارة
            </Button>
          </Box>
        </CardActions>
      </Card>

      {/* Management Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography sx={{ fontSize: '1.5rem' }}>{category.icon}</Typography>
            <Typography variant="h6">إدارة {category.name}</Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          {editingItem || Object.keys(formData).length > 0 ? (
            // Add/Edit Form
            <Box
              sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}
            >
              <TextField
                label="الاسم بالعربية"
                value={formData.name || ''}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                required
                fullWidth
              />

              <TextField
                label="الاسم بالإنجليزية"
                value={formData.nameEn || ''}
                onChange={(e) =>
                  setFormData({ ...formData, nameEn: e.target.value })
                }
                fullWidth
              />

              {category.parentCategory && (
                <FormControl fullWidth>
                  <InputLabel>الفئة الرئيسية</InputLabel>
                  <Select
                    value={formData[category.relationField] || ''}
                    label="الفئة الرئيسية"
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        [category.relationField]: e.target.value,
                      })
                    }
                  >
                    {parentItems.map((parent) => (
                      <MenuItem key={parent.id} value={parent.id}>
                        {parent.icon} {parent.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  الأيقونة
                </Typography>
                <IconSelector
                  selectedIcon={formData.icon || category.icon}
                  onIconSelect={(icon) => setFormData({ ...formData, icon })}
                />
              </Box>

              {category.hasMetadata &&
                category.metadataFields?.map((field) => (
                  <TextField
                    key={field}
                    label={field}
                    value={formData[field] || ''}
                    onChange={(e) =>
                      setFormData({ ...formData, [field]: e.target.value })
                    }
                    fullWidth
                  />
                ))}
            </Box>
          ) : (
            // Items List
            <Box>
              <Box
                sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}
              >
                <Typography variant="h6">
                  العناصر الحالية ({items.length})
                </Typography>
                <Button
                  startIcon={<AddIcon />}
                  onClick={handleAddItem}
                  variant="contained"
                  size="small"
                >
                  إضافة جديد
                </Button>
              </Box>

              <List sx={{ maxHeight: 400, overflow: 'auto' }}>
                {items.map((item) => (
                  <ListItem
                    key={item.id}
                    divider
                    sx={{
                      pr: { xs: 10, md: 12 }, // إضافة مساحة للأزرار في RTL
                      direction: 'rtl', // تأكيد اتجاه RTL
                    }}
                  >
                    <ListItemText
                      primary={
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: { xs: 0.5, md: 1 },
                            flexWrap: 'wrap',
                            pr: 1, // مساحة إضافية من اليمين
                          }}
                        >
                          <Typography
                            sx={{ fontSize: { xs: '1rem', md: '1.2rem' } }}
                          >
                            {item.icon}
                          </Typography>
                          <Typography
                            sx={{
                              opacity: item.active ? 1 : 0.5,
                              textDecoration: item.active
                                ? 'none'
                                : 'line-through',
                              fontSize: { xs: '0.875rem', md: '1rem' },
                              fontWeight: 500,
                            }}
                          >
                            {item.name}
                          </Typography>
                          {item.nameEn && (
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                fontSize: { xs: '0.75rem', md: '0.875rem' },
                              }}
                            >
                              ({item.nameEn})
                            </Typography>
                          )}
                          <Chip
                            label={item.active ? 'نشط' : 'معطل'}
                            color={item.active ? 'success' : 'default'}
                            size="small"
                            onClick={() => handleToggleActive(item)}
                            sx={{
                              cursor: 'pointer',
                              fontSize: { xs: '0.7rem', md: '0.75rem' },
                              height: { xs: 20, md: 24 },
                            }}
                          />
                          {usageCount[item.id] > 0 && (
                            <Chip
                              label={`مستخدم ${usageCount[item.id]} مرة`}
                              color="warning"
                              size="small"
                              variant="outlined"
                              sx={{
                                fontSize: { xs: '0.7rem', md: '0.75rem' },
                                height: { xs: 20, md: 24 },
                              }}
                            />
                          )}
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction
                      sx={{
                        left: { xs: 8, md: 16 }, // موضع الأزرار من اليسار في RTL
                        right: 'auto', // إلغاء الموضع الافتراضي
                        display: 'flex',
                        gap: 0.5,
                      }}
                    >
                      <IconButton
                        size="small"
                        onClick={() => handleEditItem(item)}
                        sx={{
                          bgcolor: 'action.hover',
                          '&:hover': {
                            bgcolor: 'primary.light',
                            color: 'primary.contrastText',
                          },
                        }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteItem(item)}
                        color="error"
                        disabled={usageCount[item.id] > 0}
                        sx={{
                          bgcolor: 'action.hover',
                          '&:hover': {
                            bgcolor: 'error.light',
                            color: 'error.contrastText',
                          },
                          '&:disabled': {
                            bgcolor: 'action.disabledBackground',
                          },
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>إلغاء</Button>
          {(editingItem || Object.keys(formData).length > 0) && (
            <Button onClick={handleSaveItem} variant="contained">
              {editingItem ? 'تحديث' : 'إضافة'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DropdownCategoryManager;

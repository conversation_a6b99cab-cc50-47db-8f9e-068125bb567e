import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import {
  <PERSON>ert,
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Select,
  Snackbar,
  TextField,
  Typography,
} from '@mui/material';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import IconSelector from './IconSelector';

interface DropdownItem {
  id: string;
  name: string;
  nameEn?: string;
  icon?: string;
  active: boolean;
  [key: string]: any;
}

interface DropdownManagerProps {
  title: string;
  items: DropdownItem[];
  onSave: (items: DropdownItem[]) => void;
  fields?: Array<{
    key: string;
    label: string;
    type: 'text' | 'select' | 'icon';
    options?: Array<{ value: string; label: string }>;
    required?: boolean;
  }>;
  trigger?: React.ReactNode;
}

const DropdownManager: React.FC<DropdownManagerProps> = ({
  title,
  items,
  onSave,
  fields = [
    { key: 'name', label: 'الاسم بالعربية', type: 'text', required: true },
    { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
    { key: 'icon', label: 'الأيقونة', type: 'icon' },
  ],
  trigger,
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<DropdownItem | null>(null);
  const [formData, setFormData] = useState<any>({});
  const [localItems, setLocalItems] = useState<DropdownItem[]>(items);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  const handleOpen = () => {
    setLocalItems([...items]);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingItem(null);
    setFormData({});
  };

  const handleSave = () => {
    onSave(localItems);
    setSnackbar({
      open: true,
      message: 'تم حفظ التغييرات بنجاح',
      severity: 'success',
    });
    handleClose();
  };

  const handleAddItem = () => {
    if (!formData.name) {
      setSnackbar({
        open: true,
        message: 'يرجى إدخال الاسم',
        severity: 'error',
      });
      return;
    }

    const newItem: DropdownItem = {
      id: Date.now().toString(),
      name: formData.name,
      nameEn: formData.nameEn || '',
      icon: formData.icon || '📋',
      active: true,
      ...formData,
    };

    setLocalItems([...localItems, newItem]);
    setFormData({});
    setSnackbar({
      open: true,
      message: 'تم إضافة العنصر بنجاح',
      severity: 'success',
    });
  };

  const handleEditItem = (item: DropdownItem) => {
    setEditingItem(item);
    setFormData({ ...item });
  };

  const handleUpdateItem = () => {
    if (!formData.name) {
      setSnackbar({
        open: true,
        message: 'يرجى إدخال الاسم',
        severity: 'error',
      });
      return;
    }

    const updatedItems = localItems.map((item) =>
      item.id === editingItem?.id ? { ...item, ...formData } : item
    );
    setLocalItems(updatedItems);
    setEditingItem(null);
    setFormData({});
    setSnackbar({
      open: true,
      message: 'تم تحديث العنصر بنجاح',
      severity: 'success',
    });
  };

  const handleDeleteItem = (id: string) => {
    const updatedItems = localItems.filter((item) => item.id !== id);
    setLocalItems(updatedItems);
    setSnackbar({
      open: true,
      message: 'تم حذف العنصر بنجاح',
      severity: 'success',
    });
  };

  const handleToggleActive = (id: string) => {
    const updatedItems = localItems.map((item) =>
      item.id === id ? { ...item, active: !item.active } : item
    );
    setLocalItems(updatedItems);
  };

  const renderField = (field: any) => {
    switch (field.type) {
      case 'select':
        return (
          <FormControl fullWidth margin="normal" key={field.key}>
            <InputLabel>{field.label}</InputLabel>
            <Select
              value={formData[field.key] || ''}
              label={field.label}
              onChange={(e) =>
                setFormData({ ...formData, [field.key]: e.target.value })
              }
            >
              {field.options?.map((option: any) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );
      case 'icon':
        return (
          <Box key={field.key} sx={{ mt: 2, mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              {field.label}
            </Typography>
            <IconSelector
              selectedIcon={formData[field.key] || '📋'}
              onIconSelect={(icon) =>
                setFormData({ ...formData, [field.key]: icon })
              }
            />
          </Box>
        );
      default:
        return (
          <TextField
            key={field.key}
            fullWidth
            margin="normal"
            label={field.label}
            value={formData[field.key] || ''}
            onChange={(e) =>
              setFormData({ ...formData, [field.key]: e.target.value })
            }
            required={field.required}
          />
        );
    }
  };

  return (
    <>
      {trigger ? (
        <Box onClick={handleOpen} sx={{ cursor: 'pointer' }}>
          {trigger}
        </Box>
      ) : (
        <Button
          variant="outlined"
          startIcon={<SettingsIcon />}
          onClick={handleOpen}
          size="small"
        >
          إدارة {title}
        </Button>
      )}

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>إدارة {title}</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              إضافة عنصر جديد
            </Typography>
            {fields.map(renderField)}
            <Box sx={{ mt: 2 }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={editingItem ? handleUpdateItem : handleAddItem}
                sx={{ mr: 1 }}
              >
                {editingItem ? 'تحديث' : 'إضافة'}
              </Button>
              {editingItem && (
                <Button
                  variant="outlined"
                  onClick={() => {
                    setEditingItem(null);
                    setFormData({});
                  }}
                >
                  إلغاء
                </Button>
              )}
            </Box>
          </Box>

          <Typography variant="h6" gutterBottom>
            العناصر الحالية ({localItems.length})
          </Typography>
          <List>
            {localItems.map((item) => (
              <ListItem
                key={item.id}
                divider
                sx={{
                  pr: { xs: 10, md: 12 }, // إضافة مساحة للأزرار في RTL
                  direction: 'rtl', // تأكيد اتجاه RTL
                }}
              >
                <ListItemText
                  primary={
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: { xs: 0.5, md: 1 },
                        flexWrap: 'wrap',
                        pr: 1, // مساحة إضافية من اليمين
                      }}
                    >
                      <Typography
                        sx={{ fontSize: { xs: '1rem', md: '1.2rem' } }}
                      >
                        {item.icon}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: { xs: '0.875rem', md: '1rem' },
                          fontWeight: 500,
                        }}
                      >
                        {item.name}
                      </Typography>
                      {item.nameEn && (
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' } }}
                        >
                          ({item.nameEn})
                        </Typography>
                      )}
                      <Chip
                        label={item.active ? 'نشط' : 'غير نشط'}
                        color={item.active ? 'success' : 'default'}
                        size="small"
                        onClick={() => handleToggleActive(item.id)}
                        sx={{
                          cursor: 'pointer',
                          fontSize: { xs: '0.7rem', md: '0.75rem' },
                          height: { xs: 20, md: 24 },
                        }}
                      />
                    </Box>
                  }
                />
                <ListItemSecondaryAction
                  sx={{
                    left: { xs: 8, md: 16 }, // موضع الأزرار من اليسار في RTL
                    right: 'auto', // إلغاء الموضع الافتراضي
                    display: 'flex',
                    gap: 0.5,
                  }}
                >
                  <IconButton
                    size="small"
                    onClick={() => handleEditItem(item)}
                    sx={{
                      bgcolor: 'action.hover',
                      '&:hover': {
                        bgcolor: 'primary.light',
                        color: 'primary.contrastText',
                      },
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDeleteItem(item.id)}
                    color="error"
                    sx={{
                      bgcolor: 'action.hover',
                      '&:hover': {
                        bgcolor: 'error.light',
                        color: 'error.contrastText',
                      },
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>إلغاء</Button>
          <Button onClick={handleSave} variant="contained">
            حفظ التغييرات
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default DropdownManager;

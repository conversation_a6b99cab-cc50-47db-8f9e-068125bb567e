# Database Configuration
DATABASE_URL="file:./farm.db"

# Server Configuration
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# API Configuration
API_VERSION=v1
MAX_FILE_SIZE=10mb

# Farm Settings
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30

# Alert Configuration
ALERT_EMAIL_ENABLED=false
ALERT_EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>

# SMTP Configuration (if email alerts enabled)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_SECURE=false

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=5mb
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,xlsx,csv

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true

import { Save as SaveIcon } from '@mui/icons-material';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Paper,
  Select,
  Snackbar,
  Switch,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import BackupManagement from '../components/settings/BackupManagement';
import BackupTest from '../components/settings/BackupTest';
import SimpleBackup from '../components/settings/SimpleBackup';
import { useThemeStore } from '../store/themeStore';
import DropdownManagement from './settings/DropdownManagement';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface FarmSettings {
  farmName: string;
  ownerName: string;
  location: string;
  phone: string;
  email: string;
  currency: string;
  language: string;
  timezone: string;
}

const Settings: React.FC = () => {
  const { isDarkMode, toggleTheme } = useThemeStore();
  const [tabValue, setTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'warning' | 'info',
  });

  const [farmSettings, setFarmSettings] = useState<FarmSettings>({
    farmName: 'مزرعة الأمل',
    ownerName: 'أحمد محمد',
    location: 'الرياض، المملكة العربية السعودية',
    phone: '+966501234567',
    email: '<EMAIL>',
    currency: 'SAR',
    language: 'ar',
    timezone: 'Asia/Riyadh',
  });

  useEffect(() => {
    // Load settings from localStorage
    const savedSettings = localStorage.getItem('farmSettings');
    if (savedSettings) {
      setFarmSettings(JSON.parse(savedSettings));
    }
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSaveFarmSettings = () => {
    localStorage.setItem('farmSettings', JSON.stringify(farmSettings));
    setSnackbar({
      open: true,
      message: 'تم حفظ إعدادات المزرعة بنجاح',
      severity: 'success',
    });
  };

  const handleSaveSystemSettings = () => {
    setSnackbar({
      open: true,
      message: 'تم حفظ إعدادات النظام بنجاح',
      severity: 'success',
    });
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Page Header */}
      <Box
        sx={{
          mb: 4,
          p: 3,
          background: 'linear-gradient(135deg, #4a5568 0%, #2d3748 100%)',
          borderRadius: 3,
          color: 'white',
          boxShadow: '0 8px 32px rgba(74, 85, 104, 0.3)',
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          fontWeight="bold"
          sx={{ mb: 1 }}
        >
          ⚙️ إعدادات النظام
        </Typography>
        <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
          إدارة إعدادات المزرعة والنظام العامة
        </Typography>
      </Box>

      <Paper
        sx={{
          width: '100%',
          borderRadius: 3,
          overflow: 'hidden',
          boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
          border: '1px solid rgba(0,0,0,0.05)',
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="settings tabs"
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontWeight: 500,
              fontSize: '1rem',
              textTransform: 'none',
              minHeight: 64,
              '&.Mui-selected': {
                fontWeight: 600,
              },
            },
          }}
        >
          <Tab label="🏢 إعدادات المزرعة" />
          <Tab label="⚙️ إعدادات النظام" />
          <Tab label="📋 إدارة القوائم المنسدلة" />
          <Tab label="💾 النسخ الاحتياطية لقاعدة البيانات" />
        </Tabs>

        {/* Farm Settings Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveFarmSettings}
                  sx={{
                    background:
                      'linear-gradient(45deg, #4a5568 30%, #718096 90%)',
                    '&:hover': {
                      background:
                        'linear-gradient(45deg, #2d3748 30%, #4a5568 90%)',
                    },
                  }}
                >
                  حفظ إعدادات المزرعة
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="معلومات المزرعة الأساسية"
                  titleTypographyProps={{
                    variant: 'h6',
                    fontWeight: 'bold',
                    color: 'primary.main',
                  }}
                />
                <CardContent>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Typography
                        variant="subtitle1"
                        fontWeight="bold"
                        color="text.primary"
                        sx={{ mb: 1 }}
                      >
                        اسم المزرعة
                      </Typography>
                      <TextField
                        fullWidth
                        placeholder="أدخل اسم المزرعة"
                        value={farmSettings.farmName}
                        onChange={(e) =>
                          setFarmSettings({
                            ...farmSettings,
                            farmName: e.target.value,
                          })
                        }
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography
                        variant="subtitle1"
                        fontWeight="bold"
                        color="text.primary"
                        sx={{ mb: 1 }}
                      >
                        اسم المالك
                      </Typography>
                      <TextField
                        fullWidth
                        placeholder="أدخل اسم المالك"
                        value={farmSettings.ownerName}
                        onChange={(e) =>
                          setFarmSettings({
                            ...farmSettings,
                            ownerName: e.target.value,
                          })
                        }
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography
                        variant="subtitle1"
                        fontWeight="bold"
                        color="text.primary"
                        sx={{ mb: 1 }}
                      >
                        الموقع
                      </Typography>
                      <TextField
                        fullWidth
                        placeholder="أدخل موقع المزرعة"
                        value={farmSettings.location}
                        onChange={(e) =>
                          setFarmSettings({
                            ...farmSettings,
                            location: e.target.value,
                          })
                        }
                        variant="outlined"
                        multiline
                        rows={3}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography
                        variant="subtitle1"
                        fontWeight="bold"
                        color="text.primary"
                        sx={{ mb: 1 }}
                      >
                        رقم الهاتف
                      </Typography>
                      <TextField
                        fullWidth
                        placeholder="أدخل رقم الهاتف"
                        value={farmSettings.phone}
                        onChange={(e) =>
                          setFarmSettings({
                            ...farmSettings,
                            phone: e.target.value,
                          })
                        }
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography
                        variant="subtitle1"
                        fontWeight="bold"
                        color="text.primary"
                        sx={{ mb: 1 }}
                      >
                        البريد الإلكتروني
                      </Typography>
                      <TextField
                        fullWidth
                        placeholder="أدخل البريد الإلكتروني"
                        type="email"
                        value={farmSettings.email}
                        onChange={(e) =>
                          setFarmSettings({
                            ...farmSettings,
                            email: e.target.value,
                          })
                        }
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                          },
                        }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* System Settings Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveSystemSettings}
                  sx={{
                    background:
                      'linear-gradient(45deg, #4a5568 30%, #718096 90%)',
                    '&:hover': {
                      background:
                        'linear-gradient(45deg, #2d3748 30%, #4a5568 90%)',
                    },
                  }}
                >
                  حفظ إعدادات النظام
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="إعدادات النظام"
                  titleTypographyProps={{
                    variant: 'h6',
                    fontWeight: 'bold',
                    color: 'primary.main',
                  }}
                />
                <CardContent>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Typography
                        variant="subtitle1"
                        fontWeight="bold"
                        color="text.primary"
                        sx={{ mb: 1 }}
                      >
                        العملة
                      </Typography>
                      <FormControl fullWidth variant="outlined">
                        <Select
                          value={farmSettings.currency}
                          onChange={(e) =>
                            setFarmSettings({
                              ...farmSettings,
                              currency: e.target.value,
                            })
                          }
                          displayEmpty
                          sx={{
                            borderRadius: 2,
                          }}
                        >
                          <MenuItem value="SAR">💰 ريال سعودي (SAR)</MenuItem>
                          <MenuItem value="USD">💵 دولار أمريكي (USD)</MenuItem>
                          <MenuItem value="EUR">💶 يورو (EUR)</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography
                        variant="subtitle1"
                        fontWeight="bold"
                        color="text.primary"
                        sx={{ mb: 1 }}
                      >
                        اللغة
                      </Typography>
                      <FormControl fullWidth variant="outlined">
                        <Select
                          value={farmSettings.language}
                          onChange={(e) =>
                            setFarmSettings({
                              ...farmSettings,
                              language: e.target.value,
                            })
                          }
                          displayEmpty
                          sx={{
                            borderRadius: 2,
                          }}
                        >
                          <MenuItem value="ar">🇸🇦 العربية</MenuItem>
                          <MenuItem value="en">🇺🇸 English</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={isDarkMode}
                            onChange={toggleTheme}
                            color="primary"
                          />
                        }
                        label="الوضع الليلي"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Dropdown Management Tab */}
        <TabPanel value={tabValue} index={2}>
          <DropdownManagement />
        </TabPanel>

        {/* Database Backup Tab */}
        <TabPanel value={tabValue} index={3}>
          <SimpleBackup />
          <BackupTest />
          <BackupManagement />
        </TabPanel>
      </Paper>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Settings;

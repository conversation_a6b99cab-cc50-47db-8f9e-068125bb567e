import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Tooltip,
  IconButton,
  <PERSON>lapse,
  Alert,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
} from '@mui/icons-material';

interface DropdownStatisticsProps {
  categories: any;
}

interface StatisticData {
  totalCategories: number;
  totalItems: number;
  activeItems: number;
  inactiveItems: number;
  categoriesWithIssues: number;
  usageStats: Record<string, number>;
  moduleStats: Record<string, any>;
}

const DropdownStatistics: React.FC<DropdownStatisticsProps> = ({ categories }) => {
  const [statistics, setStatistics] = useState<StatisticData>({
    totalCategories: 0,
    totalItems: 0,
    activeItems: 0,
    inactiveItems: 0,
    categoriesWithIssues: 0,
    usageStats: {},
    moduleStats: {},
  });
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    calculateStatistics();
  }, [categories]);

  const calculateStatistics = () => {
    let totalCategories = 0;
    let totalItems = 0;
    let activeItems = 0;
    let inactiveItems = 0;
    let categoriesWithIssues = 0;
    const moduleStats: Record<string, any> = {};

    Object.values(categories).forEach((module: any) => {
      const moduleData = {
        name: module.name,
        icon: module.icon,
        categories: module.categories.length,
        totalItems: 0,
        activeItems: 0,
        issues: [],
      };

      module.categories.forEach((category: any) => {
        totalCategories++;
        
        try {
          const savedItems = localStorage.getItem(category.storageKey);
          if (savedItems) {
            const items = JSON.parse(savedItems);
            if (Array.isArray(items)) {
              const categoryActiveItems = items.filter(item => item.active).length;
              const categoryInactiveItems = items.length - categoryActiveItems;
              
              totalItems += items.length;
              activeItems += categoryActiveItems;
              inactiveItems += categoryInactiveItems;
              
              moduleData.totalItems += items.length;
              moduleData.activeItems += categoryActiveItems;

              // Check for issues
              if (items.length === 0) {
                moduleData.issues.push(`${category.name}: قائمة فارغة`);
                categoriesWithIssues++;
              } else if (categoryActiveItems === 0) {
                moduleData.issues.push(`${category.name}: لا توجد عناصر نشطة`);
                categoriesWithIssues++;
              } else if (items.some(item => !item.name || !item.name.trim())) {
                moduleData.issues.push(`${category.name}: عناصر بدون أسماء`);
                categoriesWithIssues++;
              }
            }
          } else {
            moduleData.issues.push(`${category.name}: لا توجد بيانات`);
            categoriesWithIssues++;
          }
        } catch (error) {
          moduleData.issues.push(`${category.name}: خطأ في البيانات`);
          categoriesWithIssues++;
        }
      });

      moduleStats[module.id] = moduleData;
    });

    setStatistics({
      totalCategories,
      totalItems,
      activeItems,
      inactiveItems,
      categoriesWithIssues,
      usageStats: {},
      moduleStats,
    });
  };

  const getHealthStatus = () => {
    const healthPercentage = statistics.totalItems > 0 
      ? (statistics.activeItems / statistics.totalItems) * 100 
      : 0;
    
    if (healthPercentage >= 80) return { status: 'excellent', color: 'success', text: 'ممتاز' };
    if (healthPercentage >= 60) return { status: 'good', color: 'info', text: 'جيد' };
    if (healthPercentage >= 40) return { status: 'warning', color: 'warning', text: 'يحتاج تحسين' };
    return { status: 'poor', color: 'error', text: 'يحتاج إصلاح' };
  };

  const health = getHealthStatus();
  const activePercentage = statistics.totalItems > 0 
    ? (statistics.activeItems / statistics.totalItems) * 100 
    : 0;

  return (
    <Box sx={{ mb: 4 }}>
      {/* Main Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              height: '100%',
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
                  {statistics.totalCategories}
                </Typography>
                <Typography sx={{ fontSize: '2rem' }}>📋</Typography>
              </Box>
              <Typography variant="body1">إجمالي القوائم</Typography>
              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                عبر {Object.keys(categories).length} وحدات
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              color: 'white',
              height: '100%',
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
                  {statistics.totalItems}
                </Typography>
                <Typography sx={{ fontSize: '2rem' }}>📝</Typography>
              </Box>
              <Typography variant="body1">إجمالي العناصر</Typography>
              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                {statistics.activeItems} نشط، {statistics.inactiveItems} معطل
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              background: `linear-gradient(135deg, ${
                health.color === 'success' ? '#4facfe 0%, #00f2fe' :
                health.color === 'info' ? '#43e97b 0%, #38f9d7' :
                health.color === 'warning' ? '#fa709a 0%, #fee140' :
                '#ff9a9e 0%, #fecfef'
              } 100%)`,
              color: 'white',
              height: '100%',
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
                  {activePercentage.toFixed(0)}%
                </Typography>
                <Typography sx={{ fontSize: '2rem' }}>
                  {health.color === 'success' ? '✅' : 
                   health.color === 'info' ? '📊' :
                   health.color === 'warning' ? '⚠️' : '🔴'}
                </Typography>
              </Box>
              <Typography variant="body1">صحة النظام</Typography>
              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                {health.text}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              background: statistics.categoriesWithIssues > 0 
                ? 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
                : 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
              color: 'white',
              height: '100%',
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
                  {statistics.categoriesWithIssues}
                </Typography>
                <Typography sx={{ fontSize: '2rem' }}>
                  {statistics.categoriesWithIssues > 0 ? '⚠️' : '✅'}
                </Typography>
              </Box>
              <Typography variant="body1">قوائم تحتاج إصلاح</Typography>
              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                {statistics.categoriesWithIssues === 0 ? 'كل شيء يعمل بشكل جيد' : 'تحتاج مراجعة'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Statistics */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              📊 إحصائيات تفصيلية
            </Typography>
            <IconButton onClick={() => setExpanded(!expanded)}>
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>

          <Collapse in={expanded}>
            <Grid container spacing={3}>
              {Object.entries(statistics.moduleStats).map(([moduleId, moduleData]: [string, any]) => (
                <Grid item xs={12} md={6} key={moduleId}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Typography sx={{ fontSize: '1.5rem', mr: 1 }}>
                          {moduleData.icon}
                        </Typography>
                        <Typography variant="h6" sx={{ flexGrow: 1 }}>
                          {moduleData.name}
                        </Typography>
                        {moduleData.issues.length > 0 && (
                          <Tooltip title={`${moduleData.issues.length} مشاكل`}>
                            <WarningIcon color="warning" />
                          </Tooltip>
                        )}
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2">العناصر النشطة</Typography>
                          <Typography variant="body2">
                            {moduleData.activeItems} / {moduleData.totalItems}
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={moduleData.totalItems > 0 ? (moduleData.activeItems / moduleData.totalItems) * 100 : 0}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </Box>

                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                        <Chip
                          label={`${moduleData.categories} قوائم`}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                        <Chip
                          label={`${moduleData.totalItems} عنصر`}
                          size="small"
                          color="info"
                          variant="outlined"
                        />
                        {moduleData.issues.length > 0 && (
                          <Chip
                            label={`${moduleData.issues.length} مشاكل`}
                            size="small"
                            color="warning"
                            variant="outlined"
                          />
                        )}
                      </Box>

                      {moduleData.issues.length > 0 && (
                        <Alert severity="warning" sx={{ mt: 1 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            المشاكل المكتشفة:
                          </Typography>
                          {moduleData.issues.map((issue: string, index: number) => (
                            <Typography key={index} variant="caption" display="block">
                              • {issue}
                            </Typography>
                          ))}
                        </Alert>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* Overall Health Summary */}
            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                📈 ملخص الصحة العامة
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      {((statistics.totalCategories - statistics.categoriesWithIssues) / statistics.totalCategories * 100).toFixed(0)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      القوائم السليمة
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="info.main">
                      {activePercentage.toFixed(0)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      العناصر النشطة
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">
                      {Object.keys(categories).length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      الوحدات المدعومة
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Collapse>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DropdownStatistics;

// 🔧 حل شامل لجميع القوائم المنسدلة الفارغة
console.log('🚀 بدء تحميل جميع البيانات الافتراضية...');

// البيانات الافتراضية الشاملة
const completeDefaultData = {
  // 1. أنواع الحيوانات
  animalTypes: [
    {id: '1', name: 'أغنام', nameEn: 'Sheep', icon: '🐑', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'ماعز', nameEn: 'Goats', icon: '🐐', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'أبقا<PERSON>', nameEn: 'Cattle', icon: '🐄', active: true, order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'جمال', nameEn: 'Camels', icon: '🐪', active: true, order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 2. السلالات
  breeds: [
    {id: '1', name: 'نجدي', nameEn: 'Najdi', icon: '🐑', active: true, animalTypeId: '1', order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'حري', nameEn: 'Harri', icon: '🐑', active: true, animalTypeId: '1', order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'عارضي', nameEn: 'Ardi', icon: '🐐', active: true, animalTypeId: '2', order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'شامي', nameEn: 'Damascus', icon: '🐐', active: true, animalTypeId: '2', order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '5', name: 'هولشتاين', nameEn: 'Holstein', icon: '🐄', active: true, animalTypeId: '3', order: 4, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '6', name: 'مجاهيم', nameEn: 'Magaheem', icon: '🐪', active: true, animalTypeId: '4', order: 5, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 3. فئات الحيوانات
  animalCategories: [
    {id: '1', name: 'أم', nameEn: 'Mother', icon: '👩', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'فحل', nameEn: 'Father', icon: '👨', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'مولود', nameEn: 'Newborn', icon: '🍼', active: true, order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'تسمين', nameEn: 'Fattening', icon: '📈', active: true, order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '5', name: 'للبيع', nameEn: 'For Sale', icon: '💰', active: true, order: 4, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 4. المواقع والحظائر
  animalLocations: [
    {id: '1', name: 'حظيرة أ', nameEn: 'Barn A', icon: '🏠', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'حظيرة ب', nameEn: 'Barn B', icon: '🏠', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'المرعى الشمالي', nameEn: 'North Pasture', icon: '🌿', active: true, order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'المرعى الجنوبي', nameEn: 'South Pasture', icon: '🌿', active: true, order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '5', name: 'حظيرة العزل', nameEn: 'Isolation Barn', icon: '🚫', active: true, order: 4, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 5. أنواع الولادة
  birthTypes: [
    {id: '1', name: 'ولادة طبيعية', nameEn: 'Natural Birth', icon: '🐣', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'ولادة قيصرية', nameEn: 'C-Section', icon: '🏥', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'ولادة مساعدة', nameEn: 'Assisted Birth', icon: '👨‍⚕️', active: true, order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'ولادة مبكرة', nameEn: 'Premature Birth', icon: '⏰', active: true, order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 6. حالات الولادة
  birthStatuses: [
    {id: '1', name: 'موجود', nameEn: 'Present', icon: '✅', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'مباع', nameEn: 'Sold', icon: '💰', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'نافق', nameEn: 'Dead', icon: '💔', active: true, order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'مفقود', nameEn: 'Missing', icon: '❓', active: true, order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 7. مواسم الولادة
  birthSeasons: [
    {id: '1', name: 'الربيع', nameEn: 'Spring', icon: '🌸', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'الصيف', nameEn: 'Summer', icon: '☀️', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'الخريف', nameEn: 'Autumn', icon: '🍂', active: true, order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'الشتاء', nameEn: 'Winter', icon: '❄️', active: true, order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 8. طرق البيع
  salesMethods: [
    {id: '1', name: 'جملة', nameEn: 'Wholesale', icon: '📦', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'مفرد/تجزئة', nameEn: 'Retail', icon: '🛒', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 9. تصنيفات المنتجات
  productCategories: [
    {id: '1', name: 'أغنام لباني', nameEn: 'Breeding Sheep', icon: '🐑', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'أغنام تسمين', nameEn: 'Fattening Sheep', icon: '🐏', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'أغنام إحلال', nameEn: 'Replacement Sheep', icon: '🐐', active: true, order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'منتجات مشتل', nameEn: 'Nursery Products', icon: '🌱', active: true, order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '5', name: 'منتجات فقاسة', nameEn: 'Hatchery Products', icon: '🐣', active: true, order: 4, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '6', name: 'بيض مائدة', nameEn: 'Table Eggs', icon: '🥚', active: true, order: 5, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 10. وحدات القياس
  measurementUnits: [
    {id: '1', name: 'رأس', nameEn: 'Head', icon: '🐑', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'كيلو', nameEn: 'Kilogram', icon: '⚖️', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'طبق', nameEn: 'Tray', icon: '🍽️', active: true, order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'صندوق', nameEn: 'Box', icon: '📦', active: true, order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '5', name: 'كيس', nameEn: 'Bag', icon: '🛍️', active: true, order: 4, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '6', name: 'لتر', nameEn: 'Liter', icon: '🥛', active: true, order: 5, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '7', name: 'متر', nameEn: 'Meter', icon: '📏', active: true, order: 6, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '8', name: 'قطعة', nameEn: 'Piece', icon: '🔧', active: true, order: 7, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '9', name: 'حبة', nameEn: 'Item', icon: '⚪', active: true, order: 8, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '10', name: 'علبة', nameEn: 'Can', icon: '🥫', active: true, order: 9, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ],

  // 11. أنواع العملاء
  customerTypes: [
    {id: '1', name: 'عميل فردي', nameEn: 'Individual Customer', icon: '👤', active: true, order: 0, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '2', name: 'تاجر جملة', nameEn: 'Wholesale Trader', icon: '🏪', active: true, order: 1, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '3', name: 'مطعم', nameEn: 'Restaurant', icon: '🍽️', active: true, order: 2, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '4', name: 'سوبر ماركت', nameEn: 'Supermarket', icon: '🏬', active: true, order: 3, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '5', name: 'مصنع', nameEn: 'Factory', icon: '🏭', active: true, order: 4, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '6', name: 'مزرعة أخرى', nameEn: 'Another Farm', icon: '🚜', active: true, order: 5, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()},
    {id: '7', name: 'مصدر', nameEn: 'Exporter', icon: '🚢', active: true, order: 6, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString()}
  ]
};

// حفظ جميع البيانات
console.log('💾 حفظ البيانات في localStorage...');
let totalItems = 0;

Object.entries(completeDefaultData).forEach(([key, data]) => {
  localStorage.setItem(key, JSON.stringify(data));
  totalItems += data.length;
  console.log(`✅ تم حفظ ${key}: ${data.length} عنصر`);
});

console.log(`🎉 تم تحميل ${totalItems} عنصر افتراضي بنجاح!`);
console.log('🔄 إعادة تحميل الصفحة...');

// إعادة تحميل الصفحة
setTimeout(() => {
  window.location.reload();
}, 1500);

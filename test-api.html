<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API الحيوانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2196F3;
            margin-top: 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #4CAF50;
            background-color: #f0fff0;
        }
        .error {
            border-color: #f44336;
            background-color: #fff0f0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐑 اختبار API نظام إدارة المزرعة</h1>
            <p>اختبار الاتصال بين الـ Frontend والـ Backend</p>
        </div>

        <div class="test-section">
            <h3>🔗 اختبار الاتصال بالخادم</h3>
            <button onclick="testConnection()">اختبار الاتصال</button>
            <div id="connectionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🐑 اختبار API الحيوانات</h3>
            <button onclick="testAnimalsAPI()">جلب بيانات الحيوانات</button>
            <button onclick="testAnimalsCount()">عدد الحيوانات</button>
            <div id="animalsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>💾 اختبار API النسخ الاحتياطية</h3>
            <button onclick="testBackupTables()">جلب قائمة الجداول</button>
            <button onclick="testExportAnimals()">تصدير الحيوانات (JSON)</button>
            <div id="backupResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 مقارنة البيانات</h3>
            <button onclick="compareData()">مقارنة البيانات</button>
            <div id="comparisonResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3001';

        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.textContent = 'جاري الاختبار...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ الاتصال ناجح!\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل الاتصال: ${error.message}`;
            }
        }

        async function testAnimalsAPI() {
            const resultDiv = document.getElementById('animalsResult');
            resultDiv.textContent = 'جاري جلب بيانات الحيوانات...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/animals`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                
                if (data.animals && data.animals.length > 0) {
                    let html = `✅ تم جلب ${data.animals.length} حيوان بنجاح!\n\n`;
                    html += createAnimalsTable(data.animals);
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.textContent = '⚠️ لا توجد حيوانات في قاعدة البيانات';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل في جلب البيانات: ${error.message}`;
            }
        }

        async function testAnimalsCount() {
            const resultDiv = document.getElementById('animalsResult');
            resultDiv.textContent = 'جاري حساب عدد الحيوانات...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/animals`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `📊 إجمالي الحيوانات: ${data.animals ? data.animals.length : 0}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل في حساب العدد: ${error.message}`;
            }
        }

        async function testBackupTables() {
            const resultDiv = document.getElementById('backupResult');
            resultDiv.textContent = 'جاري جلب قائمة الجداول...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/backup/tables`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                
                if (data.success && data.data) {
                    let html = '✅ قائمة الجداول:\n\n';
                    data.data.forEach(table => {
                        html += `📋 ${table.name} (${table.key}): ${table.recordCount} سجل\n`;
                    });
                    resultDiv.textContent = html;
                } else {
                    resultDiv.textContent = '⚠️ فشل في جلب قائمة الجداول';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل في جلب الجداول: ${error.message}`;
            }
        }

        async function testExportAnimals() {
            const resultDiv = document.getElementById('backupResult');
            resultDiv.textContent = 'جاري تصدير بيانات الحيوانات...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/backup/export/animals?format=json`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                
                if (data.animals && data.animals.length > 0) {
                    let html = `✅ تم تصدير ${data.animals.length} حيوان بنجاح!\n\n`;
                    html += createAnimalsTable(data.animals);
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.textContent = '⚠️ لا توجد حيوانات للتصدير';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل في التصدير: ${error.message}`;
            }
        }

        async function compareData() {
            const resultDiv = document.getElementById('comparisonResult');
            resultDiv.textContent = 'جاري مقارنة البيانات...';
            
            try {
                // جلب البيانات من API الحيوانات
                const animalsResponse = await fetch(`${API_BASE_URL}/api/animals`);
                const animalsData = await animalsResponse.json();
                
                // جلب البيانات من API النسخ الاحتياطية
                const backupResponse = await fetch(`${API_BASE_URL}/api/backup/export/animals?format=json`);
                const backupData = await backupResponse.json();
                
                const animalsCount = animalsData.animals ? animalsData.animals.length : 0;
                const backupCount = backupData.animals ? backupData.animals.length : 0;
                
                resultDiv.className = 'result success';
                
                let html = '📊 مقارنة البيانات:\n\n';
                html += `🐑 API الحيوانات: ${animalsCount} حيوان\n`;
                html += `💾 API النسخ الاحتياطية: ${backupCount} حيوان\n\n`;
                
                if (animalsCount === backupCount) {
                    html += '✅ البيانات متطابقة! العدد متساوي في كلا المصدرين.\n';
                } else {
                    html += '❌ البيانات غير متطابقة! يوجد اختلاف في العدد.\n';
                }
                
                resultDiv.textContent = html;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل في المقارنة: ${error.message}`;
            }
        }

        function createAnimalsTable(animals) {
            let html = '<table>';
            html += '<tr><th>رقم التسجيل</th><th>رقم التاغ</th><th>النوع</th><th>السلالة</th><th>الجنس</th><th>الفئة</th><th>الحالة</th></tr>';
            
            animals.slice(0, 10).forEach(animal => {
                html += '<tr>';
                html += `<td>${animal.internalId || 'غير محدد'}</td>`;
                html += `<td>${animal.tagNumber || 'غير محدد'}</td>`;
                html += `<td>${animal.animalType?.nameAr || animal.animalType?.name || 'غير محدد'}</td>`;
                html += `<td>${animal.breed?.nameAr || animal.breed?.name || 'غير محدد'}</td>`;
                html += `<td>${animal.gender === 'MALE' ? 'ذكر' : animal.gender === 'FEMALE' ? 'أنثى' : animal.gender}</td>`;
                html += `<td>${animal.category || 'غير محدد'}</td>`;
                html += `<td>${animal.status === 'ALIVE' ? 'حي' : animal.status}</td>`;
                html += '</tr>';
            });
            
            html += '</table>';
            
            if (animals.length > 10) {
                html += `<p>... وعرض أول 10 حيوانات من أصل ${animals.length}</p>`;
            }
            
            return html;
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>

import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import {
  <PERSON>ert,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeStore } from '../store/themeStore';
import type { Animal } from '../types';
import {
  getFilterSectionProps,
  getTableContainerProps,
} from '../utils/pageColors';

// Birth interface
interface Birth {
  id: string;
  registrationNumber: string;
  tagNumber?: string;
  motherRegistrationNumber: string;
  motherTagNumber: string;
  fatherTagNumber?: string;
  birthDate: string;
  gender: 'male' | 'female';
  birthWeight: number;
  type: 'sheep' | 'goat';
  breed: string;
  status: 'present' | 'sold' | 'dead';
  birthType: 'single' | 'twin';
  twinCount?: number;
  barnLocation?: string;
  notes?: string;
}

const Animals: React.FC = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [births, setBirths] = useState<Birth[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Tab state
  const [currentTab, setCurrentTab] = useState(0);

  // Filter states
  const [filterType, setFilterType] = useState('');
  const [filterBreed, setFilterBreed] = useState('');
  const [filterGender, setFilterGender] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterBarn, setFilterBarn] = useState('');

  // Dialog states
  const [selectedAnimal, setSelectedAnimal] = useState<Animal | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  // Bulk selection states
  const [selectedAnimals, setSelectedAnimals] = useState<string[]>([]);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  const [bulkStatusDialogOpen, setBulkStatusDialogOpen] = useState(false);
  const [bulkStatusValue, setBulkStatusValue] = useState<
    'present' | 'sold' | 'lost'
  >('present');
  const [birthsDialogOpen, setBirthsDialogOpen] = useState(false);

  // Form state for add/edit
  const [formData, setFormData] = useState<Partial<Animal>>({
    registrationNumber: '',
    tagNumber: '',
    type: 'sheep',
    breed: '',
    gender: 'male',
    birthDate: '',
    weight: 0,
    status: 'present',
    barnLocation: '',
    joinDate: '',
    notes: '',
  });

  // Load animal types and breeds from settings
  const [animalTypes, setAnimalTypes] = useState<any[]>([]);
  const [breeds, setBreeds] = useState<any[]>([]);
  const [barns, setBarns] = useState<any[]>([]);

  // Get breeds by type
  const getBreedsByType = (type: string) => {
    // Convert type to match the breeds data structure
    let targetType = type;
    if (type === 'sheep') targetType = 'sheep';
    if (type === 'goat') targetType = 'goat';

    return breeds.filter((breed) => breed.active && breed.type === targetType);
  };

  // Get animal type value for form
  const getAnimalTypeValue = (animalType: any) => {
    // Convert from settings format to form format
    const nameEn = animalType.nameEn.toLowerCase();
    if (nameEn === 'sheep') return 'sheep';
    if (nameEn === 'goats' || nameEn === 'goat') return 'goat';
    return nameEn;
  };

  // Get type label from type value
  const getTypeLabel = (type: string) => {
    const animalType = animalTypes.find((t) => getAnimalTypeValue(t) === type);
    if (animalType) return animalType.name;

    // Fallback labels
    if (type === 'sheep') return 'أغنام';
    if (type === 'goat') return 'ماعز';
    return type;
  };

  // Get active barns
  const getActiveBarns = () => {
    return barns.filter((barn) => barn.active);
  };

  // Get barn name by barn name (for display)
  const getBarnDisplayName = (barnName: string) => {
    const barn = barns.find((b) => b.name === barnName);
    return barn ? barn.name : barnName;
  };

  useEffect(() => {
    const loadAnimals = async () => {
      try {
        setLoading(true);

        // Load animal types and breeds from settings
        const savedAnimalTypes = localStorage.getItem('animalTypes');
        if (savedAnimalTypes) {
          const parsedAnimalTypes = JSON.parse(savedAnimalTypes);

          setAnimalTypes(parsedAnimalTypes);
        } else {
          // Set default animal types if none exist
          const defaultAnimalTypes = [
            { id: '1', name: 'أغنام', nameEn: 'Sheep', active: true },
            { id: '2', name: 'ماعز', nameEn: 'Goats', active: true },
          ];
          setAnimalTypes(defaultAnimalTypes);
          localStorage.setItem(
            'animalTypes',
            JSON.stringify(defaultAnimalTypes)
          );
        }

        const savedBreeds = localStorage.getItem('breeds');
        if (savedBreeds) {
          const parsedBreeds = JSON.parse(savedBreeds);

          setBreeds(parsedBreeds);
        } else {
          // Set default breeds if none exist
          const defaultBreeds = [
            {
              id: '1',
              name: 'حري',
              nameEn: 'Harri',
              type: 'sheep',
              active: true,
            },
            {
              id: '2',
              name: 'نجدي',
              nameEn: 'Najdi',
              type: 'sheep',
              active: true,
            },
            {
              id: '3',
              name: 'عواسي',
              nameEn: 'Awassi',
              type: 'sheep',
              active: true,
            },
            {
              id: '4',
              name: 'بلدي',
              nameEn: 'Baladi',
              type: 'goat',
              active: true,
            },
            {
              id: '5',
              name: 'شامي',
              nameEn: 'Shami',
              type: 'goat',
              active: true,
            },
            {
              id: '6',
              name: 'العارضي',
              nameEn: 'Aradi',
              type: 'goat',
              active: true,
            },
          ];
          setBreeds(defaultBreeds);
          localStorage.setItem('breeds', JSON.stringify(defaultBreeds));
        }

        // Load barns from settings
        const savedBarns = localStorage.getItem('barns');
        if (savedBarns) {
          const parsedBarns = JSON.parse(savedBarns);

          setBarns(parsedBarns);
        } else {
          // Set default barns if none exist
          const defaultBarns = [
            {
              id: '1',
              name: 'حظيرة أ',
              nameEn: 'Barn A',
              capacity: 50,
              type: 'general',
              location: 'الجانب الشرقي',
              active: true,
            },
            {
              id: '2',
              name: 'حظيرة ب',
              nameEn: 'Barn B',
              capacity: 30,
              type: 'maternity',
              location: 'الجانب الغربي',
              active: true,
            },
            {
              id: '3',
              name: 'حظيرة الحمل',
              nameEn: 'Pregnancy Barn',
              capacity: 25,
              type: 'pregnancy',
              location: 'الجانب الشمالي',
              active: true,
            },
            {
              id: '4',
              name: 'حظيرة العزل',
              nameEn: 'Isolation Barn',
              capacity: 10,
              type: 'isolation',
              location: 'منطقة منفصلة',
              active: true,
            },
          ];
          setBarns(defaultBarns);
          localStorage.setItem('barns', JSON.stringify(defaultBarns));
        }

        // Load animals from localStorage first
        const savedAnimals = localStorage.getItem('animals');
        if (savedAnimals) {
          const parsedAnimals = JSON.parse(savedAnimals);

          // Convert old format registration numbers to new format and ensure all animals have registration numbers
          const animalsWithUpdatedRegNumbers = parsedAnimals.map(
            (animal: Animal, index: number) => {
              let registrationNumber = animal.registrationNumber;

              // Check if it's old format (REG-YYYY-XXX) and convert to new format (YYYY-XXXX)
              if (registrationNumber && registrationNumber.startsWith('REG-')) {
                const parts = registrationNumber.split('-');
                if (parts.length === 3) {
                  const year = parts[1];
                  const number = parts[2].padStart(4, '0'); // Convert to 4 digits
                  registrationNumber = `${year}-${number}`;
                }
              }

              // If still no registration number, generate one
              if (!registrationNumber) {
                const year = new Date().getFullYear();
                registrationNumber = `${year}-${(index + 1)
                  .toString()
                  .padStart(4, '0')}`;
              }

              // Fix status values - ensure only valid status values are used
              let status = animal.status;
              if (!['present', 'sold', 'lost'].includes(status)) {
                status = 'present'; // Default to present for invalid status
              }

              return {
                ...animal,
                registrationNumber,
                status,
              };
            }
          );

          setAnimals(animalsWithUpdatedRegNumbers);
          // Update localStorage with corrected data
          localStorage.setItem(
            'animals',
            JSON.stringify(animalsWithUpdatedRegNumbers)
          );
        } else {
          // Add sample data if no animals exist
          const sampleAnimals: Animal[] = [
            {
              id: '1',
              registrationNumber: '2024-0001',
              tagNumber: 'SH001',
              type: 'sheep',
              breed: 'نجدي',
              gender: 'male',
              birthDate: '2023-03-15',
              weight: 45,
              status: 'present',
              barnLocation: 'حظيرة أ',
              joinDate: '2023-04-10',
              notes: 'خروف نجدي ممتاز للتربية',
            },
            {
              id: '2',
              registrationNumber: '2024-0002',
              tagNumber: 'SH002',
              type: 'sheep',
              breed: 'حري',
              gender: 'female',
              birthDate: '2022-12-10',
              weight: 38,
              status: 'present',
              barnLocation: 'حظيرة ب',
              joinDate: '2023-02-08',
              notes: 'نعجة منتجة، ولدت توأم العام الماضي',
            },
            {
              id: '3',
              registrationNumber: '2024-0003',
              tagNumber: 'GT001',
              type: 'goat',
              breed: 'شامي',
              gender: 'male',
              birthDate: '2023-01-20',
              weight: 42,
              status: 'present',
              barnLocation: 'حظيرة ج',
              joinDate: '2023-03-05',
              notes: 'تيس شامي أصيل',
            },
            {
              id: '4',
              registrationNumber: '2024-0004',
              tagNumber: 'GT002',
              type: 'goat',
              breed: 'بلدي',
              gender: 'female',
              birthDate: '2022-11-05',
              weight: 35,
              status: 'present',
              barnLocation: 'حظيرة أ',
              joinDate: '2022-11-08',
              notes: 'عنزة بلدي جيدة الإنتاج',
            },
            {
              id: '5',
              registrationNumber: '2024-0005',
              tagNumber: '000000',
              type: 'sheep',
              breed: 'اوروبي',
              gender: 'male',
              birthDate: '2020-06-04',
              weight: 90,
              status: 'present',
              barnLocation: 'حظيرة د',
              joinDate: '2020-06-04',
              notes: 'كبش كبير مخصص للأضاحي',
            },
          ];
          setAnimals(sampleAnimals);
          localStorage.setItem('animals', JSON.stringify(sampleAnimals));
        }

        // Load births from localStorage
        const savedBirths = localStorage.getItem('births');
        if (savedBirths) {
          setBirths(JSON.parse(savedBirths));
        }

        setError(null);
      } catch (err) {
        console.error('Error loading animals:', err);
        setError('فشل في تحميل بيانات الحيوانات');
      } finally {
        setLoading(false);
      }
    };

    loadAnimals();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'success';
      case 'sold':
        return 'warning';
      case 'lost':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'present':
        return 'موجود';
      case 'sold':
        return 'مباع';
      case 'lost':
        return 'فاقد';
      default:
        return status;
    }
  };

  // getTypeLabel moved to top of file

  const getGenderLabel = (gender: string) => {
    return gender === 'male' ? 'ذكر' : 'أنثى';
  };

  const filteredAnimals = animals.filter((animal) => {
    // Search filter
    const matchesSearch =
      searchTerm === '' ||
      animal.tagNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      animal.registrationNumber
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      animal.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (animal.barnLocation &&
        animal.barnLocation.toLowerCase().includes(searchTerm.toLowerCase()));

    // Type filter
    const matchesType = filterType === '' || animal.type === filterType;

    // Breed filter
    const matchesBreed = filterBreed === '' || animal.breed === filterBreed;

    // Gender filter
    const matchesGender = filterGender === '' || animal.gender === filterGender;

    // Status filter
    const matchesStatus = filterStatus === '' || animal.status === filterStatus;

    // Barn filter
    const matchesBarn = filterBarn === '' || animal.barnLocation === filterBarn;

    return (
      matchesSearch &&
      matchesType &&
      matchesBreed &&
      matchesGender &&
      matchesStatus &&
      matchesBarn
    );
  });

  const paginatedAnimals = filteredAnimals.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Animal action handlers
  const handleViewAnimal = (animal: Animal) => {
    setSelectedAnimal(animal);
    setViewDialogOpen(true);
  };

  const handleEditAnimal = (animal: Animal) => {
    setSelectedAnimal(animal);
    setFormData({
      registrationNumber: animal.registrationNumber,
      tagNumber: animal.tagNumber,
      type: animal.type,
      breed: animal.breed,
      gender: animal.gender,
      birthDate: animal.birthDate,
      weight: animal.weight,
      status: animal.status,
      barnLocation: animal.barnLocation,
      joinDate: animal.joinDate,
      notes: animal.notes,
    });
    setEditDialogOpen(true);
  };

  const handleDeleteAnimal = (animal: Animal) => {
    setSelectedAnimal(animal);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteAnimal = () => {
    if (selectedAnimal) {
      const updatedAnimals = animals.filter((a) => a.id !== selectedAnimal.id);
      setAnimals(updatedAnimals);
      localStorage.setItem('animals', JSON.stringify(updatedAnimals));
      setDeleteDialogOpen(false);
      setSelectedAnimal(null);
    }
  };

  const handleAddAnimal = () => {
    setFormData({
      registrationNumber: '',
      tagNumber: '',
      type: 'sheep',
      breed: '',
      gender: 'male',
      birthDate: '',
      weight: 0,
      status: 'present',
      barnLocation: '',
      joinDate: new Date().toISOString().split('T')[0], // تاريخ اليوم كافتراضي
      notes: '',
    });
    setSelectedAnimal(null);
    setAddDialogOpen(true);
  };

  // Filter reset function
  const handleResetFilters = () => {
    setFilterType('');
    setFilterBreed('');
    setFilterGender('');
    setFilterStatus('');
    setFilterBarn('');
    setSearchTerm('');
    setPage(0);
  };

  // Get unique values for filter options
  const getUniqueBreeds = () => {
    const breeds = [...new Set(animals.map((animal) => animal.breed))];
    return breeds.sort();
  };

  // Generate unique registration number
  const generateRegistrationNumber = () => {
    const year = new Date().getFullYear();
    const existingNumbers = animals
      .map((animal) => animal.registrationNumber)
      .filter((regNum) => regNum && regNum.startsWith(`${year}-`))
      .map((regNum) => parseInt(regNum.split('-')[1]))
      .filter((num) => !isNaN(num));

    const nextNumber =
      existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1;
    return `${year}-${nextNumber.toString().padStart(4, '0')}`;
  };

  // Get breeds for selected animal type
  const getAvailableBreeds = (animalType: string) => {
    return getBreedsByType(animalType);
  };

  // Form handlers
  const handleFormChange = (field: keyof Animal, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSaveAnimal = () => {
    if (!formData.tagNumber || !formData.breed) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    const newAnimal: Animal = {
      id: selectedAnimal?.id || Date.now().toString(),
      registrationNumber:
        selectedAnimal?.registrationNumber || generateRegistrationNumber(),
      tagNumber: formData.tagNumber!,
      type: formData.type as 'sheep' | 'goat',
      breed: formData.breed!,
      gender: formData.gender as 'male' | 'female',
      birthDate: formData.birthDate!,
      weight: formData.weight || 0,
      status: formData.status as 'present' | 'sold' | 'lost',
      barnLocation: formData.barnLocation,
      joinDate: formData.joinDate,
      notes: formData.notes,
    };

    let updatedAnimals;
    if (selectedAnimal) {
      // Edit existing animal
      updatedAnimals = animals.map((animal) =>
        animal.id === selectedAnimal.id ? newAnimal : animal
      );
    } else {
      // Add new animal
      updatedAnimals = [...animals, newAnimal];
    }

    setAnimals(updatedAnimals);
    localStorage.setItem('animals', JSON.stringify(updatedAnimals));

    // Close dialogs
    setAddDialogOpen(false);
    setEditDialogOpen(false);
    setSelectedAnimal(null);
  };

  const handleCancelForm = () => {
    setAddDialogOpen(false);
    setEditDialogOpen(false);
    setSelectedAnimal(null);
    setFormData({
      registrationNumber: '',
      tagNumber: '',
      type: 'sheep',
      breed: '',
      gender: 'male',
      birthDate: '',
      weight: 0,
      status: 'present',
      barnLocation: '',
      joinDate: '',
      notes: '',
    });
  };

  // Bulk selection functions
  const handleSelectAnimal = (animalId: string) => {
    setSelectedAnimals((prev) =>
      prev.includes(animalId)
        ? prev.filter((id) => id !== animalId)
        : [...prev, animalId]
    );
  };

  const handleSelectAll = () => {
    if (selectedAnimals.length === filteredAnimals.length) {
      setSelectedAnimals([]);
    } else {
      setSelectedAnimals(filteredAnimals.map((animal) => animal.id));
    }
  };

  const isAllSelected =
    selectedAnimals.length === filteredAnimals.length &&
    filteredAnimals.length > 0;
  const isIndeterminate =
    selectedAnimals.length > 0 &&
    selectedAnimals.length < filteredAnimals.length;

  // Get births for selected mothers
  const getSelectedMothersBirths = () => {
    const selectedMothersRegNumbers = animals
      .filter((animal) => selectedAnimals.includes(animal.id))
      .map((animal) => animal.registrationNumber);

    return births.filter((birth) =>
      selectedMothersRegNumbers.includes(birth.motherRegistrationNumber)
    );
  };

  const selectedMothersBirths = getSelectedMothersBirths();

  // Helper functions for births
  const getBirthStatusLabel = (status: string) => {
    switch (status) {
      case 'present':
        return 'موجود';
      case 'sold':
        return 'مباع';
      case 'dead':
        return 'نافق';
      default:
        return status;
    }
  };

  const getBirthStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'success';
      case 'sold':
        return 'warning';
      case 'dead':
        return 'error';
      default:
        return 'default';
    }
  };

  const getBirthTypeLabel = (birthType: string) => {
    return birthType === 'single' ? 'مفرد' : 'توأم';
  };

  // Calculate age for births
  const calculateAge = (birthDate: string) => {
    const birth = new Date(birthDate);
    const today = new Date();

    let years = today.getFullYear() - birth.getFullYear();
    let months = today.getMonth() - birth.getMonth();
    let days = today.getDate() - birth.getDate();

    if (days < 0) {
      months--;
      days += new Date(today.getFullYear(), today.getMonth(), 0).getDate();
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    return `${years.toString().padStart(2, '0')}-${months
      .toString()
      .padStart(2, '0')}-${days.toString().padStart(2, '0')}`;
  };

  // Bulk operations
  const handleBulkDelete = () => {
    setBulkDeleteDialogOpen(true);
  };

  const confirmBulkDelete = () => {
    const updatedAnimals = animals.filter(
      (animal) => !selectedAnimals.includes(animal.id)
    );
    setAnimals(updatedAnimals);
    localStorage.setItem('animals', JSON.stringify(updatedAnimals));
    setSelectedAnimals([]);
    setBulkDeleteDialogOpen(false);
  };

  const handleBulkStatusChange = () => {
    setBulkStatusDialogOpen(true);
  };

  const confirmBulkStatusChange = () => {
    const updatedAnimals = animals.map((animal) =>
      selectedAnimals.includes(animal.id)
        ? { ...animal, status: bulkStatusValue }
        : animal
    );
    setAnimals(updatedAnimals);
    localStorage.setItem('animals', JSON.stringify(updatedAnimals));
    setSelectedAnimals([]);
    setBulkStatusDialogOpen(false);
  };

  const handleViewBirths = () => {
    setBirthsDialogOpen(true);
  };

  const handleBulkExport = () => {
    const selectedAnimalsData = animals.filter((animal) =>
      selectedAnimals.includes(animal.id)
    );
    const csvContent = [
      [
        'رقم التسجيل',
        'رقم التاغ',
        'النوع',
        'السلالة',
        'الجنس',
        'تاريخ الميلاد',
        'الوزن',
        'الحالة',
        'الحظيرة',
        'تاريخ الدخول',
        'ملاحظات',
      ].join(','),
      ...selectedAnimalsData.map((animal) =>
        [
          animal.registrationNumber,
          animal.tagNumber,
          getTypeLabel(animal.type),
          animal.breed,
          getGenderLabel(animal.gender),
          animal.birthDate,
          animal.weight,
          getStatusLabel(animal.status),
          animal.barnLocation || '',
          animal.joinDate || '',
          animal.notes || '',
        ].join(',')
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute(
      'download',
      `animals_selected_${new Date().toISOString().split('T')[0]}.csv`
    );
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          {t('animals')}
        </Typography>
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 0, sm: 0 } }}>
      {/* Page Header */}
      {/* Page Title */}
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        🐑 إدارة الحيوانات
      </Typography>

      {/* Search and Filters */}
      <Box sx={{ mb: 3 }}>
        {/* Search Bar */}
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>
          <TextField
            placeholder="البحث في الحيوانات (رقم التاغ، رقم التسجيل، السلالة، الحظيرة)..."
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ flexGrow: 1 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddAnimal}
            sx={{
              backgroundColor: 'primary.main',
              '&:hover': { backgroundColor: 'primary.dark' },
            }}
          >
            إضافة حيوان
          </Button>
        </Box>

        {/* Filters */}
        <Box
          {...getFilterSectionProps(isDarkMode)}
          sx={{
            ...getFilterSectionProps(isDarkMode).sx,
            display: 'flex',
            gap: 2,
            alignItems: 'center',
            flexWrap: 'wrap',
          }}
        >
          {/* Type Filter */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>النوع</InputLabel>
            <Select
              value={filterType}
              label="النوع"
              onChange={(e) => setFilterType(e.target.value)}
            >
              <MenuItem value="">الكل</MenuItem>
              {animalTypes
                .filter((type) => type.active)
                .map((type) => (
                  <MenuItem key={type.id} value={getAnimalTypeValue(type)}>
                    {type.name}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          {/* Breed Filter */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>السلالة</InputLabel>
            <Select
              value={filterBreed}
              label="السلالة"
              onChange={(e) => setFilterBreed(e.target.value)}
            >
              <MenuItem value="">الكل</MenuItem>
              {(() => {
                const availableBreeds = filterType
                  ? getBreedsByType(filterType)
                  : breeds.filter((breed) => breed.active);
                return availableBreeds.map((breed) => (
                  <MenuItem key={breed.id} value={breed.name}>
                    {breed.name}
                  </MenuItem>
                ));
              })()}
            </Select>
          </FormControl>

          {/* Gender Filter */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>الجنس</InputLabel>
            <Select
              value={filterGender}
              label="الجنس"
              onChange={(e) => setFilterGender(e.target.value)}
            >
              <MenuItem value="">الكل</MenuItem>
              <MenuItem value="male">ذكر</MenuItem>
              <MenuItem value="female">أنثى</MenuItem>
            </Select>
          </FormControl>

          {/* Status Filter */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>الحالة</InputLabel>
            <Select
              value={filterStatus}
              label="الحالة"
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <MenuItem value="">الكل</MenuItem>
              <MenuItem value="present">موجود</MenuItem>
              <MenuItem value="sold">مباع</MenuItem>
              <MenuItem value="lost">فاقد</MenuItem>
            </Select>
          </FormControl>

          {/* Barn Filter */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>الحظيرة</InputLabel>
            <Select
              value={filterBarn}
              label="الحظيرة"
              onChange={(e) => setFilterBarn(e.target.value)}
            >
              <MenuItem value="">الكل</MenuItem>
              {(() => {
                const activeBarns = getActiveBarns();
                return activeBarns.map((barn) => (
                  <MenuItem key={barn.id} value={barn.name}>
                    {barn.name}
                  </MenuItem>
                ));
              })()}
            </Select>
          </FormControl>

          {/* Reset Filters Button */}
          <Button
            variant="outlined"
            onClick={handleResetFilters}
            size="small"
            sx={{
              color: 'primary.main',
              borderColor: 'primary.main',
              '&:hover': {
                backgroundColor: 'primary.light',
                borderColor: 'primary.dark',
              },
            }}
          >
            إعادة تعيين
          </Button>
        </Box>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'primary.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'primary.contrastText' }}
            >
              {filteredAnimals.length}
            </Typography>
            <Typography
              variant="caption"
              sx={{ color: 'primary.contrastText' }}
            >
              إجمالي الحيوانات
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'success.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'success.contrastText' }}
            >
              {filteredAnimals.filter((a) => a.type === 'sheep').length}
            </Typography>
            <Typography
              variant="caption"
              sx={{ color: 'success.contrastText' }}
            >
              🐑 الأغنام
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'info.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'info.contrastText' }}
            >
              {filteredAnimals.filter((a) => a.type === 'goat').length}
            </Typography>
            <Typography variant="caption" sx={{ color: 'info.contrastText' }}>
              🐐 الماعز
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            sx={{ p: 2, textAlign: 'center', backgroundColor: 'warning.light' }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 'bold', color: 'warning.contrastText' }}
            >
              {filteredAnimals.filter((a) => a.status === 'present').length}
            </Typography>
            <Typography
              variant="caption"
              sx={{ color: 'warning.contrastText' }}
            >
              ✅ موجود
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper
        sx={{
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
          border: '1px solid rgba(0,0,0,0.05)',
          overflow: 'hidden',
          mb: 3,
        }}
      >
        <Tabs
          value={currentTab}
          onChange={(_, newValue) => setCurrentTab(newValue)}
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontWeight: 'bold',
              fontSize: '1rem',
              minHeight: 60,
            },
          }}
        >
          <Tab
            label="📋 عرض عادي"
            sx={{
              color: 'primary.main',
              '&.Mui-selected': {
                color: 'primary.dark',
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
              },
            }}
          />
          <Tab
            label="👥 الأمهات والمواليد"
            sx={{
              color: 'warning.main',
              '&.Mui-selected': {
                color: 'warning.dark',
                backgroundColor: 'rgba(255, 152, 0, 0.1)',
              },
            }}
          />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {currentTab === 0 && (
        /* Animals Table */
        <Paper
          {...getTableContainerProps(isDarkMode)}
          sx={{
            ...getTableContainerProps(isDarkMode).sx,
            overflow: 'hidden',
          }}
        >
          <TableContainer sx={{ maxHeight: 500 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    رقم التسجيل
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    رقم التاغ
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    النوع
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    السلالة
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    الجنس
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    تاريخ الميلاد
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    العمر
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    تاريخ الدخول
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    الوزن (كغ)
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    الحظيرة
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    الحالة
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    ملاحظات
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      fontSize: '1rem',
                    }}
                  >
                    الإجراءات
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedAnimals.map((animal) => (
                  <TableRow
                    key={animal.id}
                    hover
                    sx={{
                      '&:nth-of-type(odd)': {
                        backgroundColor: 'action.hover',
                      },
                      '&:hover': {
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        transform: 'scale(1.01)',
                      },
                      transition: 'all 0.2s ease',
                    }}
                  >
                    <TableCell>
                      <Typography
                        variant="body2"
                        sx={{
                          color: 'text.secondary',
                          fontSize: '0.875rem',
                          fontFamily: 'monospace',
                        }}
                      >
                        {animal.registrationNumber || 'غير محدد'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body1"
                        fontWeight="bold"
                        sx={{
                          color: 'primary.main',
                          fontSize: '1rem',
                        }}
                      >
                        {animal.tagNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getTypeLabel(animal.type)}
                        color={
                          animal.type === 'sheep' ? 'primary' : 'secondary'
                        }
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">{animal.breed}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {getGenderLabel(animal.gender)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {new Date(animal.birthDate).toLocaleDateString('ar-SA')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        sx={{
                          fontFamily: 'monospace',
                          color: 'text.secondary',
                        }}
                      >
                        {calculateAge(animal.birthDate)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {animal.joinDate
                          ? new Date(animal.joinDate).toLocaleDateString(
                              'ar-SA'
                            )
                          : 'غير محدد'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1" fontWeight="bold">
                        {animal.weight} كغ
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {animal.barnLocation || 'غير محدد'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(animal.status)}
                        color={getStatusColor(animal.status) as any}
                        size="small"
                        sx={{ fontWeight: 500 }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        sx={{
                          maxWidth: 150,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {animal.notes || '-'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleViewAnimal(animal)}
                          sx={{
                            borderRadius: 2,
                            '&:hover': {
                              backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            },
                          }}
                          title="عرض التفاصيل"
                        >
                          <ViewIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="secondary"
                          onClick={() => handleEditAnimal(animal)}
                          sx={{
                            borderRadius: 2,
                            '&:hover': {
                              backgroundColor: 'rgba(156, 39, 176, 0.1)',
                            },
                          }}
                          title="تعديل"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteAnimal(animal)}
                          sx={{
                            borderRadius: 2,
                            '&:hover': {
                              backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            },
                          }}
                          title="حذف"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredAnimals.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="عدد الصفوف في الصفحة:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`
            }
          />
        </Paper>
      )}

      {/* Tab 2: Mothers and Births */}
      {currentTab === 1 && (
        <Box>
          {/* Mothers Section */}
          <Paper
            sx={{
              borderRadius: 3,
              boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
              border: '1px solid rgba(0,0,0,0.05)',
              overflow: 'hidden',
              mb: 3,
            }}
          >
            <Box
              sx={{
                p: 2,
                background: 'linear-gradient(135deg, #4caf50 0%, #81c784 100%)',
                color: 'white',
              }}
            >
              <Typography variant="h6" fontWeight="bold">
                👩‍👧‍👦 الأمهات (
                {filteredAnimals.filter((a) => a.gender === 'female').length})
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                حدد الأمهات لعرض مواليدهن في الأسفل
              </Typography>
            </Box>
            <TableContainer sx={{ maxHeight: 300 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        backgroundColor: 'primary.main',
                        color: 'white',
                        width: 60,
                      }}
                    >
                      <Checkbox
                        checked={isAllSelected}
                        indeterminate={isIndeterminate}
                        onChange={handleSelectAll}
                        sx={{
                          color: 'white',
                          '&.Mui-checked': { color: 'white' },
                          '&.MuiCheckbox-indeterminate': { color: 'white' },
                        }}
                      />
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        backgroundColor: 'primary.main',
                        color: 'white',
                      }}
                    >
                      رقم التسجيل
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        backgroundColor: 'primary.main',
                        color: 'white',
                      }}
                    >
                      رقم التاغ
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        backgroundColor: 'primary.main',
                        color: 'white',
                      }}
                    >
                      النوع
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        backgroundColor: 'primary.main',
                        color: 'white',
                      }}
                    >
                      السلالة
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        backgroundColor: 'primary.main',
                        color: 'white',
                      }}
                    >
                      الحالة
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        backgroundColor: 'primary.main',
                        color: 'white',
                      }}
                    >
                      الحظيرة
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredAnimals
                    .filter((animal) => animal.gender === 'female')
                    .map((animal) => (
                      <TableRow
                        key={animal.id}
                        hover
                        sx={{
                          '&:nth-of-type(odd)': {
                            backgroundColor: 'action.hover',
                          },
                          '&:hover': {
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                          },
                        }}
                      >
                        <TableCell>
                          <Checkbox
                            checked={selectedAnimals.includes(animal.id)}
                            onChange={() => handleSelectAnimal(animal.id)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography
                            variant="body2"
                            sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}
                          >
                            {animal.registrationNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography
                            variant="body1"
                            fontWeight="bold"
                            color="primary.main"
                          >
                            {animal.tagNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getTypeLabel(animal.type)}
                            color={
                              animal.type === 'sheep' ? 'primary' : 'secondary'
                            }
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>{animal.breed}</TableCell>
                        <TableCell>
                          <Chip
                            label={getStatusLabel(animal.status)}
                            color={getStatusColor(animal.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {animal.barnLocation || 'غير محدد'}
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* Births Section */}
          <Paper
            sx={{
              borderRadius: 3,
              boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
              border: '1px solid rgba(0,0,0,0.05)',
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                p: 2,
                background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
                color: 'white',
              }}
            >
              <Typography variant="h6" fontWeight="bold">
                🍼 المواليد ({selectedMothersBirths.length})
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                {selectedAnimals.length > 0
                  ? `مواليد الأمهات المحددة (${selectedAnimals.length} أم محددة)`
                  : 'حدد الأمهات أعلاه لعرض مواليدهن'}
              </Typography>
            </Box>
            {selectedMothersBirths.length > 0 ? (
              <TableContainer sx={{ maxHeight: 400 }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          backgroundColor: 'warning.main',
                          color: 'white',
                        }}
                      >
                        رقم التسجيل
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          backgroundColor: 'warning.main',
                          color: 'white',
                        }}
                      >
                        رقم التاغ
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          backgroundColor: 'warning.main',
                          color: 'white',
                        }}
                      >
                        الأم
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          backgroundColor: 'warning.main',
                          color: 'white',
                        }}
                      >
                        النوع
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          backgroundColor: 'warning.main',
                          color: 'white',
                        }}
                      >
                        الجنس
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          backgroundColor: 'warning.main',
                          color: 'white',
                        }}
                      >
                        تاريخ الميلاد
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          backgroundColor: 'warning.main',
                          color: 'white',
                        }}
                      >
                        العمر
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          backgroundColor: 'warning.main',
                          color: 'white',
                        }}
                      >
                        نوع الولادة
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          backgroundColor: 'warning.main',
                          color: 'white',
                        }}
                      >
                        الحالة
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedMothersBirths.map((birth) => (
                      <TableRow
                        key={birth.id}
                        hover
                        sx={{
                          '&:nth-of-type(odd)': {
                            backgroundColor: 'action.hover',
                          },
                          '&:hover': {
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                          },
                        }}
                      >
                        <TableCell>
                          <Typography
                            variant="body2"
                            sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}
                          >
                            {birth.registrationNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography
                            variant="body1"
                            fontWeight="bold"
                            color="warning.main"
                          >
                            {birth.tagNumber || 'غير محدد'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {birth.motherTagNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getTypeLabel(birth.type)}
                            color={
                              birth.type === 'sheep' ? 'primary' : 'secondary'
                            }
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>{getGenderLabel(birth.gender)}</TableCell>
                        <TableCell>
                          {new Date(birth.birthDate).toLocaleDateString(
                            'ar-SA'
                          )}
                        </TableCell>
                        <TableCell>
                          <Typography
                            variant="body2"
                            sx={{ fontFamily: 'monospace' }}
                          >
                            {calculateAge(birth.birthDate)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getBirthTypeLabel(birth.birthType)}
                            color={
                              birth.birthType === 'single' ? 'success' : 'info'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getBirthStatusLabel(birth.status)}
                            color={getBirthStatusColor(birth.status) as any}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Box sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                  🔍 لا توجد مواليد للعرض
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedAnimals.length === 0
                    ? 'حدد الأمهات من الجدول أعلاه لعرض مواليدهن'
                    : 'لا توجد مواليد مسجلة للأمهات المحددة'}
                </Typography>
              </Box>
            )}
          </Paper>
        </Box>
      )}

      {/* View Animal Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(45deg, #4caf50 30%, #81c784 90%)',
            color: 'white',
            fontWeight: 'bold',
          }}
        >
          🐑 تفاصيل الحيوان
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          {selectedAnimal && (
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                gap: 3,
              }}
            >
              <Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  fontWeight="bold"
                >
                  المعلومات الأساسية
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Typography>
                    <strong>رقم التسجيل:</strong>{' '}
                    {selectedAnimal.registrationNumber}
                  </Typography>
                  <Typography>
                    <strong>رقم التاغ:</strong> {selectedAnimal.tagNumber}
                  </Typography>
                  <Typography>
                    <strong>النوع:</strong> {getTypeLabel(selectedAnimal.type)}
                  </Typography>
                  <Typography>
                    <strong>السلالة:</strong> {selectedAnimal.breed}
                  </Typography>
                  <Typography>
                    <strong>الجنس:</strong>{' '}
                    {getGenderLabel(selectedAnimal.gender)}
                  </Typography>
                </Box>
              </Box>
              <Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  fontWeight="bold"
                >
                  المعلومات الصحية
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Typography>
                    <strong>تاريخ الميلاد:</strong>{' '}
                    {new Date(selectedAnimal.birthDate).toLocaleDateString(
                      'ar-SA'
                    )}
                  </Typography>
                  <Typography>
                    <strong>تاريخ الدخول:</strong>{' '}
                    {selectedAnimal.joinDate
                      ? new Date(selectedAnimal.joinDate).toLocaleDateString(
                          'ar-SA'
                        )
                      : 'غير محدد'}
                  </Typography>
                  <Typography>
                    <strong>الوزن:</strong> {selectedAnimal.weight} كغ
                  </Typography>
                  <Typography>
                    <strong>الحظيرة:</strong>{' '}
                    {selectedAnimal.barnLocation || 'غير محدد'}
                  </Typography>
                  <Typography>
                    <strong>الحالة:</strong>
                    <Chip
                      label={getStatusLabel(selectedAnimal.status)}
                      color={getStatusColor(selectedAnimal.status) as any}
                      size="small"
                      sx={{ ml: 1, fontWeight: 500 }}
                    />
                  </Typography>
                  <Typography>
                    <strong>ملاحظات:</strong>{' '}
                    {selectedAnimal.notes || 'لا توجد ملاحظات'}
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)} color="primary">
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ color: 'error.main', fontWeight: 'bold' }}>
          ⚠️ تأكيد الحذف
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ fontSize: '1.1rem', color: 'text.primary' }}>
            هل أنت متأكد من حذف الحيوان برقم التاغ{' '}
            <strong>{selectedAnimal?.tagNumber}</strong>؟
            <br />
            <Typography
              component="span"
              color="error"
              sx={{ mt: 1, display: 'block' }}
            >
              لا يمكن التراجع عن هذا الإجراء.
            </Typography>
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            color="primary"
            variant="outlined"
          >
            إلغاء
          </Button>
          <Button
            onClick={confirmDeleteAnimal}
            color="error"
            variant="contained"
            sx={{ fontWeight: 'bold' }}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add/Edit Animal Dialog */}
      <Dialog
        open={addDialogOpen || editDialogOpen}
        onClose={handleCancelForm}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(45deg, #4caf50 30%, #81c784 90%)',
            color: 'white',
            fontWeight: 'bold',
          }}
        >
          {selectedAnimal ? '✏️ تعديل الحيوان' : '➕ إضافة حيوان جديد'}
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
              gap: 3,
            }}
          >
            {/* Basic Information */}
            <Box>
              <Typography
                variant="h6"
                gutterBottom
                color="primary"
                fontWeight="bold"
              >
                المعلومات الأساسية
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {/* Registration Number - Auto-generated for all animals */}
                <TextField
                  label="رقم التسجيل"
                  value={
                    selectedAnimal
                      ? formData.registrationNumber ||
                        selectedAnimal.registrationNumber
                      : 'سيتم إنشاؤه تلقائياً'
                  }
                  fullWidth
                  disabled
                  helperText={
                    selectedAnimal
                      ? 'رقم التسجيل في النظام (لا يمكن تغييره)'
                      : 'سيتم إنشاء رقم تسجيل فريد تلقائياً'
                  }
                  sx={{
                    '& .MuiInputBase-input.Mui-disabled': {
                      WebkitTextFillColor: 'rgba(0, 0, 0, 0.6)',
                    },
                  }}
                />

                <TextField
                  label="رقم التاغ *"
                  value={formData.tagNumber || ''}
                  onChange={(e) =>
                    handleFormChange('tagNumber', e.target.value)
                  }
                  fullWidth
                  required
                  helperText="رقم التاغ الموضوع على الحيوان (يمكن إعادة استخدامه)"
                />

                <FormControl fullWidth>
                  <InputLabel>النوع *</InputLabel>
                  <Select
                    value={formData.type || 'sheep'}
                    label="النوع *"
                    onChange={(e) => {
                      handleFormChange('type', e.target.value);
                      // Reset breed when type changes
                      handleFormChange('breed', '');
                    }}
                  >
                    {(() => {
                      const activeTypes = animalTypes.filter(
                        (type) => type.active
                      );

                      return activeTypes.map((type) => (
                        <MenuItem
                          key={type.id}
                          value={getAnimalTypeValue(type)}
                        >
                          {type.nameEn.toLowerCase() === 'sheep' ? '🐑' : '🐐'}{' '}
                          {type.name}
                        </MenuItem>
                      ));
                    })()}
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>السلالة *</InputLabel>
                  <Select
                    value={formData.breed || ''}
                    label="السلالة *"
                    onChange={(e) => handleFormChange('breed', e.target.value)}
                    disabled={!formData.type}
                  >
                    <MenuItem value="">اختر السلالة</MenuItem>
                    {(() => {
                      const availableBreeds = getAvailableBreeds(
                        formData.type || 'sheep'
                      );

                      return availableBreeds.map((breed: any) => (
                        <MenuItem key={breed.id} value={breed.name}>
                          {breed.name}
                        </MenuItem>
                      ));
                    })()}
                  </Select>
                  {formData.type && (
                    <Typography
                      variant="caption"
                      sx={{ mt: 0.5, color: 'text.secondary' }}
                    >
                      السلالات المتاحة لـ{' '}
                      {formData.type === 'sheep' ? 'الأغنام' : 'الماعز'}
                    </Typography>
                  )}
                </FormControl>
              </Box>
            </Box>

            {/* Additional Information */}
            <Box>
              <Typography
                variant="h6"
                gutterBottom
                color="primary"
                fontWeight="bold"
              >
                معلومات إضافية
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <FormControl fullWidth>
                  <InputLabel>الجنس</InputLabel>
                  <Select
                    value={formData.gender || 'male'}
                    label="الجنس"
                    onChange={(e) => handleFormChange('gender', e.target.value)}
                  >
                    <MenuItem value="male">ذكر</MenuItem>
                    <MenuItem value="female">أنثى</MenuItem>
                  </Select>
                </FormControl>
                <TextField
                  label="تاريخ الميلاد"
                  type="date"
                  value={formData.birthDate || ''}
                  onChange={(e) =>
                    handleFormChange('birthDate', e.target.value)
                  }
                  fullWidth
                  placeholder="yyyy-mm-dd"
                  helperText="تنسيق التاريخ: yyyy-mm-dd"
                  slotProps={{
                    inputLabel: { shrink: true },
                    htmlInput: {
                      pattern: '\\d{4}-\\d{2}-\\d{2}',
                      placeholder: 'yyyy-mm-dd',
                    },
                  }}
                />
                <TextField
                  label="تاريخ الدخول"
                  type="date"
                  value={formData.joinDate || ''}
                  onChange={(e) => handleFormChange('joinDate', e.target.value)}
                  fullWidth
                  placeholder="yyyy-mm-dd"
                  helperText="تاريخ انضمام الحيوان إلى المزرعة (yyyy-mm-dd)"
                  slotProps={{
                    inputLabel: { shrink: true },
                    htmlInput: {
                      pattern: '\\d{4}-\\d{2}-\\d{2}',
                      placeholder: 'yyyy-mm-dd',
                    },
                  }}
                />
                <TextField
                  label="الوزن (كغ)"
                  type="number"
                  value={formData.weight || ''}
                  onChange={(e) =>
                    handleFormChange('weight', parseFloat(e.target.value) || 0)
                  }
                  fullWidth
                  slotProps={{
                    htmlInput: { min: 0, step: 0.1 },
                  }}
                />
                <FormControl fullWidth>
                  <InputLabel>الحظيرة</InputLabel>
                  <Select
                    value={formData.barnLocation || ''}
                    label="الحظيرة"
                    onChange={(e) =>
                      handleFormChange('barnLocation', e.target.value)
                    }
                  >
                    <MenuItem value="">
                      <em>لا توجد حظيرة محددة</em>
                    </MenuItem>
                    {(() => {
                      const activeBarns = getActiveBarns();

                      return activeBarns.map((barn: any) => (
                        <MenuItem key={barn.id} value={barn.name}>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                            }}
                          >
                            <Typography>
                              {barn.type === 'general'
                                ? '🏠'
                                : barn.type === 'maternity'
                                ? '🍼'
                                : barn.type === 'pregnancy'
                                ? '🤱'
                                : barn.type === 'isolation'
                                ? '🚫'
                                : '🏠'}
                            </Typography>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {barn.name}
                              </Typography>
                              <Typography
                                variant="caption"
                                color="text.secondary"
                              >
                                السعة: {barn.capacity} رأس - {barn.location}
                              </Typography>
                            </Box>
                          </Box>
                        </MenuItem>
                      ));
                    })()}
                  </Select>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ mt: 0.5 }}
                  >
                    اختياري - الحظيرة المتواجد فيها الحيوان حالياً
                  </Typography>
                </FormControl>
                <FormControl fullWidth>
                  <InputLabel>الحالة</InputLabel>
                  <Select
                    value={formData.status || 'present'}
                    label="الحالة"
                    onChange={(e) => handleFormChange('status', e.target.value)}
                  >
                    <MenuItem value="present">موجود</MenuItem>
                    <MenuItem value="sold">مباع</MenuItem>
                    <MenuItem value="lost">فاقد</MenuItem>
                  </Select>
                </FormControl>
                <TextField
                  label="ملاحظات"
                  value={formData.notes || ''}
                  onChange={(e) => handleFormChange('notes', e.target.value)}
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="أي ملاحظات إضافية عن الحيوان..."
                  helperText="اختياري - ملاحظات عامة أو معلومات إضافية"
                />
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button onClick={handleCancelForm} color="primary" variant="outlined">
            إلغاء
          </Button>
          <Button
            onClick={handleSaveAnimal}
            color="primary"
            variant="contained"
            sx={{
              fontWeight: 'bold',
              background: 'linear-gradient(45deg, #4caf50 30%, #81c784 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #388e3c 30%, #66bb6a 90%)',
              },
            }}
          >
            {selectedAnimal ? 'حفظ التعديلات' : 'إضافة الحيوان'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <Dialog
        open={bulkDeleteDialogOpen}
        onClose={() => setBulkDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, #f44336 0%, #e57373 100%)',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '1.25rem',
          }}
        >
          ⚠️ تأكيد الحذف الجماعي
        </DialogTitle>
        <DialogContent sx={{ p: 3, mt: 2 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            هل أنت متأكد من حذف <strong>{selectedAnimals.length}</strong> من
            الحيوانات المحددة؟
          </Typography>
          <Typography variant="body2" color="text.secondary">
            هذا الإجراء لا يمكن التراجع عنه.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 2 }}>
          <Button
            onClick={() => setBulkDeleteDialogOpen(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            إلغاء
          </Button>
          <Button
            onClick={confirmBulkDelete}
            variant="contained"
            color="error"
            sx={{ minWidth: 100 }}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bulk Status Change Dialog */}
      <Dialog
        open={bulkStatusDialogOpen}
        onClose={() => setBulkStatusDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '1.25rem',
          }}
        >
          🔄 تغيير الحالة الجماعي
        </DialogTitle>
        <DialogContent sx={{ p: 3, mt: 2 }}>
          <Typography variant="body1" sx={{ mb: 3 }}>
            تغيير حالة <strong>{selectedAnimals.length}</strong> من الحيوانات
            المحددة إلى:
          </Typography>
          <FormControl fullWidth>
            <InputLabel>الحالة الجديدة</InputLabel>
            <Select
              value={bulkStatusValue}
              label="الحالة الجديدة"
              onChange={(e) =>
                setBulkStatusValue(
                  e.target.value as 'present' | 'sold' | 'lost'
                )
              }
            >
              <MenuItem value="present">موجود</MenuItem>
              <MenuItem value="sold">مباع</MenuItem>
              <MenuItem value="lost">فاقد</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 2 }}>
          <Button
            onClick={() => setBulkStatusDialogOpen(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            إلغاء
          </Button>
          <Button
            onClick={confirmBulkStatusChange}
            variant="contained"
            color="warning"
            sx={{ minWidth: 100 }}
          >
            تغيير
          </Button>
        </DialogActions>
      </Dialog>

      {/* Births Dialog */}
      <Dialog
        open={birthsDialogOpen}
        onClose={() => setBirthsDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, #2196f3 0%, #64b5f6 100%)',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '1.25rem',
          }}
        >
          👶 مواليد الحيوانات المحددة
        </DialogTitle>
        <DialogContent sx={{ p: 3, mt: 2 }}>
          <Typography variant="body1" sx={{ mb: 3 }}>
            عرض مواليد <strong>{selectedAnimals.length}</strong> من الحيوانات
            المحددة:
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            سيتم تطوير هذه الميزة لاحقاً لعرض المواليد المرتبطة بالأمهات
            المحددة.
          </Typography>
          <Box
            sx={{
              p: 3,
              backgroundColor: 'info.light',
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'info.main',
            }}
          >
            <Typography variant="h6" sx={{ mb: 2, color: 'info.dark' }}>
              📋 الحيوانات المحددة:
            </Typography>
            {animals
              .filter((animal) => selectedAnimals.includes(animal.id))
              .map((animal) => (
                <Typography key={animal.id} variant="body2" sx={{ mb: 1 }}>
                  • {animal.tagNumber} - {getTypeLabel(animal.type)} -{' '}
                  {animal.breed}
                </Typography>
              ))}
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={() => setBirthsDialogOpen(false)}
            variant="contained"
            color="primary"
          >
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Animals;

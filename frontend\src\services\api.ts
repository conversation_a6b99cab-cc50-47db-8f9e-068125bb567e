import axios from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Import types
import type {
    Animal,
    ApiResponse,
    Breed,
    DashboardStats
} from '../types';

// API Functions
export const apiService = {
  // Health check
  health: () => api.get('/health'),

  // Test endpoint
  test: () => api.get('/api/test'),

  // Animals
  getAnimals: (): Promise<ApiResponse<Animal[]>> =>
    api.get('/api/animals').then(res => res.data),

  getAnimal: (id: string): Promise<ApiResponse<Animal>> =>
    api.get(`/api/animals/${id}`).then(res => res.data),

  createAnimal: (animal: Omit<Animal, 'id'>): Promise<ApiResponse<Animal>> =>
    api.post('/api/animals', animal).then(res => res.data),

  updateAnimal: (id: string, animal: Partial<Animal>): Promise<ApiResponse<Animal>> =>
    api.put(`/api/animals/${id}`, animal).then(res => res.data),

  deleteAnimal: (id: string): Promise<ApiResponse<void>> =>
    api.delete(`/api/animals/${id}`).then(res => res.data),

  // Breeds
  getBreeds: (): Promise<ApiResponse<Breed[]>> =>
    api.get('/api/breeds').then(res => res.data),

  getBreedsForType: (type: 'sheep' | 'goat'): Promise<ApiResponse<Breed[]>> =>
    api.get(`/api/breeds?type=${type}`).then(res => res.data),

  // Dashboard
  getDashboardStats: (): Promise<ApiResponse<DashboardStats>> =>
    api.get('/api/dashboard/stats').then(res => res.data),

  // Reports
  getFinancialReport: (startDate: string, endDate: string) =>
    api.get(`/api/reports/financial?start=${startDate}&end=${endDate}`).then(res => res.data),

  getProductionReport: (startDate: string, endDate: string) =>
    api.get(`/api/reports/production?start=${startDate}&end=${endDate}`).then(res => res.data),

  // Export data
  exportAnimals: (format: 'excel' | 'csv' = 'excel') =>
    api.get(`/api/export/animals?format=${format}`, {
      responseType: 'blob'
    }),

  exportReports: (type: string, format: 'excel' | 'pdf' = 'excel') =>
    api.get(`/api/export/reports/${type}?format=${format}`, {
      responseType: 'blob'
    }),

  // Settings
  getFarmSettings: () =>
    api.get('/api/settings/farm').then(res => res.data),

  updateFarmSettings: (settings: any) =>
    api.put('/api/settings/farm', settings).then(res => res.data),

  getAnimalTypes: () =>
    api.get('/api/settings/animal-types').then(res => res.data),

  getBreedsSettings: () =>
    api.get('/api/settings/breeds').then(res => res.data),

  getFeedTypes: () =>
    api.get('/api/settings/feed-types').then(res => res.data),

  getTreatmentTypes: () =>
    api.get('/api/settings/treatment-types').then(res => res.data),
};

export default api;

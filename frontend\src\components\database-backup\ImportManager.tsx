import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Typography,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Upload as UploadIcon,
  Preview as PreviewIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { DatabaseBackupService } from '../../services/DatabaseBackupService';

interface ImportManagerProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

interface ImportFile {
  file: File;
  tableName: string;
  status: 'pending' | 'validating' | 'valid' | 'invalid' | 'importing' | 'imported' | 'error';
  recordCount: number;
  errors: string[];
  warnings: string[];
  preview: any[];
}

const ImportManager: React.FC<ImportManagerProps> = ({ onSuccess, onError }) => {
  const [importFiles, setImportFiles] = useState<ImportFile[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [previewDialog, setPreviewDialog] = useState<{
    open: boolean;
    file: ImportFile | null;
  }>({ open: false, file: null });
  const [replaceExisting, setReplaceExisting] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    for (const file of files) {
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.json')) {
        onError(`الملف ${file.name} غير مدعوم. يرجى رفع ملفات Excel (.xlsx) أو JSON (.json) فقط.`);
        continue;
      }

      const tableName = file.name.replace(/\.(xlsx|json)$/, '');
      
      const importFile: ImportFile = {
        file,
        tableName,
        status: 'pending',
        recordCount: 0,
        errors: [],
        warnings: [],
        preview: [],
      };

      setImportFiles(prev => [...prev, importFile]);
      
      // بدء التحقق من صحة الملف
      validateFile(importFile);
    }
    
    // إعادة تعيين input
    event.target.value = '';
  };

  const validateFile = async (importFile: ImportFile) => {
    setImportFiles(prev => prev.map(f => 
      f.file === importFile.file 
        ? { ...f, status: 'validating' }
        : f
    ));

    try {
      const backupService = new DatabaseBackupService();
      const validation = await backupService.validateImportFile(importFile.file);

      setImportFiles(prev => prev.map(f => 
        f.file === importFile.file 
          ? {
              ...f,
              status: validation.isValid ? 'valid' : 'invalid',
              recordCount: validation.recordCount,
              errors: validation.errors,
              warnings: validation.warnings,
              preview: validation.preview,
            }
          : f
      ));
    } catch (error) {
      setImportFiles(prev => prev.map(f => 
        f.file === importFile.file 
          ? {
              ...f,
              status: 'error',
              errors: ['فشل في قراءة الملف'],
            }
          : f
      ));
    }
  };

  const handleRemoveFile = (fileToRemove: File) => {
    setImportFiles(prev => prev.filter(f => f.file !== fileToRemove));
  };

  const handlePreview = (importFile: ImportFile) => {
    setPreviewDialog({ open: true, file: importFile });
  };

  const handleImport = async () => {
    const validFiles = importFiles.filter(f => f.status === 'valid');
    
    if (validFiles.length === 0) {
      onError('لا توجد ملفات صالحة للاستيراد');
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      const backupService = new DatabaseBackupService();
      let completedFiles = 0;

      for (const importFile of validFiles) {
        setImportFiles(prev => prev.map(f => 
          f.file === importFile.file 
            ? { ...f, status: 'importing' }
            : f
        ));

        const result = await backupService.importFile(importFile.file, {
          replaceExisting,
          tableName: importFile.tableName,
        });

        if (result.success) {
          setImportFiles(prev => prev.map(f => 
            f.file === importFile.file 
              ? { ...f, status: 'imported' }
              : f
          ));
        } else {
          setImportFiles(prev => prev.map(f => 
            f.file === importFile.file 
              ? { 
                  ...f, 
                  status: 'error',
                  errors: [result.error || 'فشل في الاستيراد']
                }
              : f
          ));
        }

        completedFiles++;
        setImportProgress((completedFiles / validFiles.length) * 100);
      }

      const successCount = importFiles.filter(f => f.status === 'imported').length;
      onSuccess(`تم استيراد ${successCount} ملف بنجاح`);
      
    } catch (error) {
      console.error('Import error:', error);
      onError('حدث خطأ أثناء استيراد البيانات');
    } finally {
      setIsImporting(false);
      setTimeout(() => {
        setImportProgress(0);
      }, 2000);
    }
  };

  const getStatusIcon = (status: ImportFile['status']) => {
    switch (status) {
      case 'valid':
      case 'imported':
        return <CheckIcon color="success" />;
      case 'invalid':
      case 'error':
        return <ErrorIcon color="error" />;
      case 'validating':
      case 'importing':
        return <LinearProgress sx={{ width: 20 }} />;
      default:
        return <WarningIcon color="warning" />;
    }
  };

  const getStatusText = (status: ImportFile['status']) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'validating': return 'جاري التحقق';
      case 'valid': return 'صالح';
      case 'invalid': return 'غير صالح';
      case 'importing': return 'جاري الاستيراد';
      case 'imported': return 'تم الاستيراد';
      case 'error': return 'خطأ';
      default: return 'غير معروف';
    }
  };

  const validFilesCount = importFiles.filter(f => f.status === 'valid').length;
  const totalRecords = importFiles
    .filter(f => f.status === 'valid')
    .reduce((sum, f) => sum + f.recordCount, 0);

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <Grid container spacing={3}>
        {/* Upload Section */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: 'fit-content' }}>
            <CardHeader
              title="رفع الملفات"
              titleTypographyProps={{
                variant: 'h6',
                fontWeight: 'bold',
                color: 'primary.main',
              }}
            />
            <CardContent>
              <Box
                sx={{
                  border: '2px dashed',
                  borderColor: 'divider',
                  borderRadius: 2,
                  p: 3,
                  textAlign: 'center',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    borderColor: 'primary.main',
                    bgcolor: 'action.hover',
                  },
                }}
                component="label"
              >
                <input
                  type="file"
                  multiple
                  accept=".xlsx,.json"
                  onChange={handleFileUpload}
                  style={{ display: 'none' }}
                />
                <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  اختر الملفات للرفع
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  ملفات Excel (.xlsx) أو JSON (.json)
                </Typography>
              </Box>

              <Box sx={{ mt: 3 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={replaceExisting}
                      onChange={(e) => setReplaceExisting(e.target.checked)}
                    />
                  }
                  label="استبدال البيانات الموجودة"
                />
                <Typography variant="caption" color="text.secondary" display="block">
                  إذا تم تفعيل هذا الخيار، سيتم حذف البيانات الموجودة واستبدالها
                </Typography>
              </Box>

              {validFilesCount > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    {validFilesCount} ملف جاهز للاستيراد ({totalRecords} سجل)
                  </Alert>
                  
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<CheckIcon />}
                    onClick={handleImport}
                    disabled={isImporting}
                    sx={{
                      py: 1.5,
                      background: 'linear-gradient(45deg, #4caf50 30%, #66bb6a 90%)',
                      '&:hover': {
                        background: 'linear-gradient(45deg, #388e3c 30%, #4caf50 90%)',
                      },
                    }}
                  >
                    {isImporting ? 'جاري الاستيراد...' : 'بدء الاستيراد'}
                  </Button>

                  {isImporting && (
                    <Box sx={{ mt: 2 }}>
                      <LinearProgress 
                        variant="determinate" 
                        value={importProgress}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        {Math.round(importProgress)}% مكتمل
                      </Typography>
                    </Box>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Files List */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader
              title="الملفات المرفوعة"
              titleTypographyProps={{
                variant: 'h6',
                fontWeight: 'bold',
                color: 'primary.main',
              }}
            />
            <CardContent>
              {importFiles.length === 0 ? (
                <Alert severity="info">
                  لم يتم رفع أي ملفات بعد. يرجى اختيار الملفات للبدء.
                </Alert>
              ) : (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>الملف</TableCell>
                        <TableCell>الحالة</TableCell>
                        <TableCell>السجلات</TableCell>
                        <TableCell>الإجراءات</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {importFiles.map((importFile, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography variant="subtitle2">
                              {importFile.file.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {(importFile.file.size / 1024).toFixed(1)} KB
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getStatusIcon(importFile.status)}
                              <Typography variant="body2">
                                {getStatusText(importFile.status)}
                              </Typography>
                            </Box>
                            {importFile.errors.length > 0 && (
                              <Chip
                                label={`${importFile.errors.length} خطأ`}
                                color="error"
                                size="small"
                                sx={{ mt: 0.5 }}
                              />
                            )}
                            {importFile.warnings.length > 0 && (
                              <Chip
                                label={`${importFile.warnings.length} تحذير`}
                                color="warning"
                                size="small"
                                sx={{ mt: 0.5, ml: 0.5 }}
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            {importFile.recordCount > 0 && (
                              <Chip
                                label={`${importFile.recordCount} سجل`}
                                color="primary"
                                size="small"
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              {importFile.preview.length > 0 && (
                                <Button
                                  size="small"
                                  startIcon={<PreviewIcon />}
                                  onClick={() => handlePreview(importFile)}
                                >
                                  معاينة
                                </Button>
                              )}
                              <Button
                                size="small"
                                color="error"
                                onClick={() => handleRemoveFile(importFile.file)}
                              >
                                حذف
                              </Button>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Preview Dialog */}
      <Dialog
        open={previewDialog.open}
        onClose={() => setPreviewDialog({ open: false, file: null })}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          معاينة البيانات - {previewDialog.file?.file.name}
        </DialogTitle>
        <DialogContent>
          {previewDialog.file && previewDialog.file.preview.length > 0 && (
            <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    {Object.keys(previewDialog.file.preview[0]).map((key) => (
                      <TableCell key={key}>{key}</TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {previewDialog.file.preview.slice(0, 10).map((row, index) => (
                    <TableRow key={index}>
                      {Object.values(row).map((value: any, cellIndex) => (
                        <TableCell key={cellIndex}>
                          {String(value)}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          
          {previewDialog.file && previewDialog.file.errors.length > 0 && (
            <Alert severity="error" sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                الأخطاء المكتشفة:
              </Typography>
              <ul>
                {previewDialog.file.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </Alert>
          )}
          
          {previewDialog.file && previewDialog.file.warnings.length > 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                التحذيرات:
              </Typography>
              <ul>
                {previewDialog.file.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog({ open: false, file: null })}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ImportManager;

// Unified color scheme for all pages
export const pageColors = {
  // Primary page colors - consistent across all pages
  animals: {
    primary: '#4caf50',    // Green
    secondary: '#81c784',
    gradient: 'linear-gradient(135deg, #4caf50 0%, #81c784 100%)',
    shadow: 'rgba(76, 175, 80, 0.3)',
    icon: '🐑',
    title: 'الحيوانات'
  },
  births: {
    primary: '#2196f3',    // Blue
    secondary: '#64b5f6',
    gradient: 'linear-gradient(135deg, #2196f3 0%, #64b5f6 100%)',
    shadow: 'rgba(33, 150, 243, 0.3)',
    icon: '🍼',
    title: 'المواليد'
  },
  sales: {
    primary: '#ff9800',    // Orange
    secondary: '#ffb74d',
    gradient: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
    shadow: 'rgba(255, 152, 0, 0.3)',
    icon: '💰',
    title: 'المبيعات'
  },
  purchases: {
    primary: '#9c27b0',    // Purple
    secondary: '#ba68c8',
    gradient: 'linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%)',
    shadow: 'rgba(156, 39, 176, 0.3)',
    icon: '🛒',
    title: 'المشتريات والمصروفات'
  },
  treatments: {
    primary: '#f44336',    // Red
    secondary: '#ef5350',
    gradient: 'linear-gradient(135deg, #f44336 0%, #ef5350 100%)',
    shadow: 'rgba(244, 67, 54, 0.3)',
    icon: '💊',
    title: 'العلاجات'
  },
  settings: {
    primary: '#607d8b',    // Blue Grey
    secondary: '#90a4ae',
    gradient: 'linear-gradient(135deg, #607d8b 0%, #90a4ae 100%)',
    shadow: 'rgba(96, 125, 139, 0.3)',
    icon: '⚙️',
    title: 'الإعدادات'
  }
};

// Summary card colors for consistent styling
export const summaryCardColors = {
  total: {
    background: 'linear-gradient(135deg, #2196f3 0%, #64b5f6 100%)',
    color: '#ffffff'
  },
  success: {
    background: 'linear-gradient(135deg, #4caf50 0%, #81c784 100%)',
    color: '#ffffff'
  },
  warning: {
    background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
    color: '#ffffff'
  },
  error: {
    background: 'linear-gradient(135deg, #f44336 0%, #ef5350 100%)',
    color: '#ffffff'
  },
  info: {
    background: 'linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%)',
    color: '#ffffff'
  }
};

// Common header component props
export const getPageHeaderProps = (pageKey: keyof typeof pageColors, isDarkMode: boolean = false) => {
  const colors = pageColors[pageKey];
  return {
    sx: {
      mb: 4,
      p: 3,
      background: colors.gradient,
      borderRadius: 3,
      color: 'white',
      boxShadow: `0 8px 32px ${colors.shadow}`,
    },
    icon: colors.icon,
    title: colors.title
  };
};

// Common button styling
export const getActionButtonProps = (pageKey: keyof typeof pageColors) => {
  const colors = pageColors[pageKey];
  return {
    sx: {
      background: colors.gradient,
      '&:hover': {
        background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primary} 100%)`,
        transform: 'translateY(-2px)',
        boxShadow: `0 8px 25px ${colors.shadow}`,
      },
      transition: 'all 0.3s ease',
    }
  };
};

// Filter section styling
export const getFilterSectionProps = (isDarkMode: boolean = false) => {
  return {
    sx: {
      p: 3,
      mb: 3,
      borderRadius: 3,
      backgroundColor: isDarkMode ? 'grey.900' : '#f8f9fa',
      border: isDarkMode ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.05)',
      boxShadow: isDarkMode 
        ? '0 4px 20px rgba(0,0,0,0.3)' 
        : '0 4px 20px rgba(0,0,0,0.08)',
    }
  };
};

// Table container styling
export const getTableContainerProps = (isDarkMode: boolean = false) => {
  return {
    sx: {
      borderRadius: 3,
      backgroundColor: isDarkMode ? 'grey.900' : '#ffffff',
      border: isDarkMode ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.05)',
      boxShadow: isDarkMode 
        ? '0 4px 20px rgba(0,0,0,0.3)' 
        : '0 4px 20px rgba(0,0,0,0.08)',
    }
  };
};

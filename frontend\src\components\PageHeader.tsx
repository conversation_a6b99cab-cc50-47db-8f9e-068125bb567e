import { Box, Typography } from '@mui/material';
import React from 'react';
import { getPageHeaderProps, pageColors } from '../utils/pageColors';

interface PageHeaderProps {
  pageKey: keyof typeof pageColors;
  subtitle?: string;
  isDarkMode?: boolean;
}

const PageHeader: React.FC<PageHeaderProps> = ({ 
  pageKey, 
  subtitle, 
  isDarkMode = false 
}) => {
  const headerProps = getPageHeaderProps(pageKey, isDarkMode);
  const colors = pageColors[pageKey];

  return (
    <Box {...headerProps}>
      <Typography
        variant="h3"
        component="h1"
        fontWeight="bold"
        sx={{ mb: 1 }}
      >
        {colors.icon} {colors.title}
      </Typography>
      {subtitle && (
        <Typography variant="h6" sx={{ opacity: 0.9 }}>
          {subtitle}
        </Typography>
      )}
    </Box>
  );
};

export default PageHeader;

{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.1.2", "typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/x-data-grid": "^8.5.0", "@mui/x-date-pickers": "^8.5.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "axios": "^1.9.0", "i18next": "^25.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.2", "react-router-dom": "^7.6.1", "recharts": "^2.15.3", "zustand": "^5.0.5"}}
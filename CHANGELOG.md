# Changelog

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### المخطط لها
- نظام المصادقة والتفويض
- تطبيق الجوال (React Native)
- تقارير متقدمة مع PDF
- نظام الإشعارات
- تكامل مع أنظمة المحاسبة

## [1.0.0] - 2024-12-03

### أضيف
- **النظام الأساسي**
  - إعداد مشروع React مع TypeScript
  - إعداد خادم Node.js مع Express
  - قاعدة بيانات SQLite مع Prisma ORM
  - دعم ثنائي اللغة (عربي/إنجليزي)
  - نظام الثيمات (فاتح/معتم)

- **إدارة الحيوانات**
  - نموذج بيانات شامل للحيوانات
  - دعم أنواع مختلفة (أغنام، ماعز)
  - إدارة السلالات
  - تتبع الأوزان والحالة الصحية
  - تصنيف الحيوانات (أم، فحل، مولود، إلخ)

- **الدورة الإنتاجية**
  - نموذج دورات التكاثر
  - تتبع مراحل الحمل
  - إدارة المواليد
  - حساب الفترات الزمنية

- **إدارة الأعلاف**
  - أنواع الأعلاف المختلفة
  - تتبع المشتريات والاستهلاك
  - خطط التغذية للمراحل المختلفة

- **النظام المالي**
  - تتبع المبيعات والمشتريات
  - إدارة المصروفات
  - نظام الشراكة وتوزيع الأرباح
  - تقارير مالية أساسية

- **إدارة الموظفين**
  - بيانات الموظفين
  - نظام الرواتب
  - تتبع الحضور

- **العلاجات والتحصينات**
  - سجل العلاجات
  - جدولة التحصينات
  - تتبع الأدوية

- **الواجهة والتجربة**
  - تصميم Material-UI عصري
  - واجهة متجاوبة للجوال
  - لوحة تحكم تفاعلية
  - رسوم بيانية ومؤشرات أداء
  - نظام البحث والتصفية

- **التقنيات والأدوات**
  - TypeScript للأمان في الكود
  - Zustand لإدارة الحالة
  - React Router للتنقل
  - Recharts للرسوم البيانية
  - i18next للترجمة
  - Docker للنشر
  - ESLint و Prettier لجودة الكود

- **الأمان والأداء**
  - Helmet للأمان
  - Rate limiting
  - CORS configuration
  - Gzip compression
  - Error handling شامل

- **التطوير والنشر**
  - إعداد Docker Compose
  - تكوين VS Code
  - Scripts للتطوير والبناء
  - بيانات أولية للاختبار
  - توثيق شامل

### الأمان
- تشفير كلمات المرور
- JWT للمصادقة
- حماية من XSS و CSRF
- تحديد معدل الطلبات

### الأداء
- ضغط Gzip
- تحسين الصور
- Lazy loading للمكونات
- تخزين مؤقت للبيانات

## [0.1.0] - 2024-12-01

### أضيف
- إعداد المشروع الأولي
- هيكل المجلدات الأساسي
- تكوين البيئة التطويرية

---

## أنواع التغييرات
- `أضيف` للميزات الجديدة
- `غُيّر` للتغييرات في الميزات الموجودة
- `مُهمل` للميزات التي ستُحذف قريباً
- `حُذف` للميزات المحذوفة
- `أُصلح` لإصلاح الأخطاء
- `الأمان` لإصلاحات الأمان

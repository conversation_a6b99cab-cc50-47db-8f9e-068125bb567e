const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function simpleSeed() {
  try {
    console.log('🌱 إضافة بيانات تجريبية بسيطة...');

    // إضافة موظفين
    const employee1 = await prisma.employee.create({
      data: {
        name: 'أحمد محمد',
        idNumber: '1234567890',
        position: 'مدير المزرعة',
        monthlySalary: 8000,
        hireDate: new Date('2023-01-01'),
        status: 'ACTIVE',
        phone: '0501234567',
      },
    });

    const employee2 = await prisma.employee.create({
      data: {
        name: 'سالم أحمد',
        idNumber: '0987654321',
        position: 'راعي',
        monthlySalary: 4000,
        hireDate: new Date('2023-06-01'),
        status: 'ACTIVE',
        phone: '0507654321',
      },
    });

    // إضافة مبيعات
    const sale1 = await prisma.sale.create({
      data: {
        saleDate: new Date('2024-11-15'),
        saleType: 'MARKET',
        totalPrice: 15000,
        customerName: 'شركة التجارة المتقدمة',
        customerPhone: '0501111111',
        notes: 'بيع 10 رؤوس أغنام',
      },
    });

    const sale2 = await prisma.sale.create({
      data: {
        saleDate: new Date('2024-10-20'),
        saleType: 'INDIVIDUAL',
        totalPrice: 3000,
        customerName: 'محمد علي',
        customerPhone: '0502222222',
        notes: 'بيع خروف واحد',
      },
    });

    // إضافة مشتريات
    const purchase1 = await prisma.purchase.create({
      data: {
        type: 'OTHER',
        description: 'علف مركز للأغنام',
        quantity: 100,
        unitPrice: 45,
        totalCost: 4500,
        purchaseDate: new Date('2024-11-01'),
        supplier: 'شركة الأعلاف المتقدمة',
        notes: 'علف عالي الجودة',
      },
    });

    const purchase2 = await prisma.purchase.create({
      data: {
        type: 'MEDICINE',
        description: 'أدوية بيطرية',
        quantity: 10,
        unitPrice: 50,
        totalCost: 500,
        purchaseDate: new Date('2024-10-15'),
        supplier: 'الصيدلية البيطرية',
        notes: 'أدوية للتطعيم',
      },
    });

    // إضافة مصروفات
    const expense1 = await prisma.expense.create({
      data: {
        description: 'فاتورة كهرباء',
        category: 'كهرباء',
        amount: 1200,
        date: new Date('2024-11-01'),
        notes: 'فاتورة شهر أكتوبر',
      },
    });

    const expense2 = await prisma.expense.create({
      data: {
        description: 'صيانة المعدات',
        category: 'صيانة',
        amount: 800,
        date: new Date('2024-10-25'),
        notes: 'صيانة دورية',
      },
    });

    console.log('✅ تم إضافة البيانات التجريبية بنجاح!');
    console.log(`📊 الإحصائيات:`);
    console.log(`   - الموظفين: 2`);
    console.log(`   - المبيعات: 2`);
    console.log(`   - المشتريات: 2`);
    console.log(`   - المصروفات: 2`);

  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات التجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

simpleSeed();

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        cd backend && npm ci
        cd ../frontend && npm ci
    
    - name: Lint backend
      run: cd backend && npm run lint
      continue-on-error: true
    
    - name: Lint frontend
      run: cd frontend && npm run lint
      continue-on-error: true
    
    - name: Type check backend
      run: cd backend && npm run type-check
      continue-on-error: true
    
    - name: Type check frontend
      run: cd frontend && npm run type-check
      continue-on-error: true
    
    - name: Test backend
      run: cd backend && npm test
      continue-on-error: true
    
    - name: Test frontend
      run: cd frontend && npm test
      continue-on-error: true
    
    - name: Build backend
      run: cd backend && npm run build
    
    - name: Build frontend
      run: cd frontend && npm run build

  security:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        cd backend && npm ci
        cd ../frontend && npm ci
    
    - name: Run security audit
      run: |
        cd backend && npm audit --audit-level moderate
        cd ../frontend && npm audit --audit-level moderate
      continue-on-error: true

  docker:
    runs-on: ubuntu-latest
    needs: [test]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push backend
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/farm-backend:latest
          ${{ secrets.DOCKER_USERNAME }}/farm-backend:${{ github.sha }}
    
    - name: Build and push frontend
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/farm-frontend:latest
          ${{ secrets.DOCKER_USERNAME }}/farm-frontend:${{ github.sha }}

  deploy:
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to production
      run: |
        echo "Deployment step would go here"
        # Add your deployment commands here

import { PrismaClient } from '@prisma/client';
import { Router } from 'express';

const router = Router();
const prisma = new PrismaClient();

// GET /api/breeds - Get all breeds
router.get('/', async (req, res) => {
  try {
    const { animalTypeId } = req.query;

    const where: any = {};
    if (animalTypeId) {
      where.animalTypeId = animalTypeId as string;
    }

    const breeds = await prisma.breed.findMany({
      where,
      include: {
        animalType: true,
        _count: {
          select: { animals: true },
        },
      },
      orderBy: { name: 'asc' },
    });

    res.json(breeds);
  } catch (error) {
    console.error('Error fetching breeds:', error);
    res.status(500).json({ error: 'Failed to fetch breeds' });
  }
});

// GET /api/breeds/:id - Get single breed
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const breed = await prisma.breed.findUnique({
      where: { id },
      include: {
        animalType: true,
        animals: {
          select: {
            id: true,
            internalId: true,
            tagNumber: true,
            gender: true,
            status: true,
          },
        },
      },
    });

    if (!breed) {
      return res.status(404).json({ error: 'Breed not found' });
    }

    return res.json(breed);
  } catch (error) {
    console.error('Error fetching breed:', error);
    return res.status(500).json({ error: 'Failed to fetch breed' });
  }
});

// POST /api/breeds - Create new breed
router.post('/', async (req, res) => {
  try {
    const breedData = req.body;

    const breed = await prisma.breed.create({
      data: breedData,
      include: {
        animalType: true,
      },
    });

    res.status(201).json(breed);
  } catch (error) {
    console.error('Error creating breed:', error);
    res.status(500).json({ error: 'Failed to create breed' });
  }
});

// PUT /api/breeds/:id - Update breed
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const breed = await prisma.breed.update({
      where: { id },
      data: updateData,
      include: {
        animalType: true,
      },
    });

    res.json(breed);
  } catch (error) {
    console.error('Error updating breed:', error);
    res.status(500).json({ error: 'Failed to update breed' });
  }
});

// DELETE /api/breeds/:id - Delete breed
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if breed has animals
    const animalCount = await prisma.animal.count({
      where: { breedId: id },
    });

    if (animalCount > 0) {
      return res.status(400).json({
        error: 'Cannot delete breed with existing animals',
      });
    }

    await prisma.breed.delete({
      where: { id },
    });

    return res.status(204).send();
  } catch (error) {
    console.error('Error deleting breed:', error);
    return res.status(500).json({ error: 'Failed to delete breed' });
  }
});

// GET /api/animal-types - Get all animal types
router.get('/animal-types', async (req, res) => {
  try {
    const animalTypes = await prisma.animalType.findMany({
      include: {
        _count: {
          select: {
            breeds: true,
            animals: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    res.json(animalTypes);
  } catch (error) {
    console.error('Error fetching animal types:', error);
    res.status(500).json({ error: 'Failed to fetch animal types' });
  }
});

// POST /api/animal-types - Create new animal type
router.post('/animal-types', async (req, res) => {
  try {
    const animalTypeData = req.body;

    const animalType = await prisma.animalType.create({
      data: animalTypeData,
    });

    res.status(201).json(animalType);
  } catch (error) {
    console.error('Error creating animal type:', error);
    res.status(500).json({ error: 'Failed to create animal type' });
  }
});

export default router;

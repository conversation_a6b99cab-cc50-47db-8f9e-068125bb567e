import { Box, CssBaseline } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import React from 'react';
import { I18nextProvider } from 'react-i18next';
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import Navbar from './components/Layout/Navbar';
import Sidebar from './components/Layout/Sidebar';
import i18n from './i18n/config';
import Animals from './pages/AnimalsSimple';
import Births from './pages/Births';
import Dashboard from './pages/Dashboard';
import Employees from './pages/Employees';
import Feeds from './pages/Feeds';
import PurchasesExpenses from './pages/PurchasesExpenses';
import Reports from './pages/Reports';
import Reproduction from './pages/Reproduction';
import Sales from './pages/Sales';
import Settings from './pages/Settings';
import Treatments from './pages/Treatments';
import { useLanguageStore } from './store/languageStore';
import { useThemeStore } from './store/themeStore';

function App() {
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();
  const [sidebarOpen, setSidebarOpen] = React.useState(true);

  // Create theme based on dark mode and language
  const theme = createTheme({
    palette: {
      mode: isDarkMode ? 'dark' : 'light',
      primary: {
        main: '#1976d2', // Modern blue
        light: '#42a5f5',
        dark: '#1565c0',
      },
      secondary: {
        main: '#ff9800', // Modern orange
        light: '#ffb74d',
        dark: '#f57c00',
      },
      success: {
        main: '#4caf50',
        light: '#81c784',
        dark: '#388e3c',
      },
      background: {
        default: isDarkMode ? '#121212' : '#ffffff',
        paper: isDarkMode ? '#1e1e1e' : '#ffffff',
      },
      text: {
        primary: isDarkMode ? '#ffffff' : '#000000',
        secondary: isDarkMode ? '#b3b3b3' : '#666666',
      },
    },
    direction: language === 'ar' ? 'rtl' : 'ltr',
    typography: {
      fontFamily:
        language === 'ar'
          ? '"Tajawal", "Cairo", "Almarai", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
          : '"Inter", "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
      h1: {
        fontWeight: 700,
        fontSize: '2.5rem',
        lineHeight: 3,
      },
      h2: {
        fontWeight: 600,
        fontSize: '2rem',
        lineHeight: 1.3,
      },
      h3: {
        fontWeight: 600,
        fontSize: '1.75rem',
        lineHeight: 1.3,
      },
      h4: {
        fontWeight: 600,
        fontSize: '1.5rem',
        lineHeight: 1.4,
      },
      h5: {
        fontWeight: 500,
        fontSize: '1.25rem',
        lineHeight: 1.4,
      },
      h6: {
        fontWeight: 500,
        fontSize: '1.125rem',
        lineHeight: 1.4,
      },
      body1: {
        fontSize: language === 'ar' ? '1.1rem' : '1rem',
        lineHeight: 1.6,
      },
      body2: {
        fontSize: language === 'ar' ? '0.95rem' : '0.875rem',
        lineHeight: 1.5,
      },
    },
    shape: {
      borderRadius: 12,
    },
    shadows: [
      'none',
      '0px 2px 4px rgba(0,0,0,0.05)',
      '0px 4px 8px rgba(0,0,0,0.08)',
      '0px 8px 16px rgba(0,0,0,0.1)',
      '0px 12px 24px rgba(0,0,0,0.12)',
      '0px 16px 32px rgba(0,0,0,0.15)',
      '0px 20px 40px rgba(0,0,0,0.18)',
      '0px 24px 48px rgba(0,0,0,0.2)',
      '0px 28px 56px rgba(0,0,0,0.22)',
      '0px 32px 64px rgba(0,0,0,0.25)',
      '0px 36px 72px rgba(0,0,0,0.28)',
      '0px 40px 80px rgba(0,0,0,0.3)',
      '0px 44px 88px rgba(0,0,0,0.33)',
      '0px 48px 96px rgba(0,0,0,0.35)',
      '0px 52px 104px rgba(0,0,0,0.38)',
      '0px 56px 112px rgba(0,0,0,0.4)',
      '0px 60px 120px rgba(0,0,0,0.42)',
      '0px 64px 128px rgba(0,0,0,0.45)',
      '0px 68px 136px rgba(0,0,0,0.47)',
      '0px 72px 144px rgba(0,0,0,0.5)',
      '0px 76px 152px rgba(0,0,0,0.52)',
      '0px 80px 160px rgba(0,0,0,0.55)',
      '0px 84px 168px rgba(0,0,0,0.57)',
      '0px 88px 176px rgba(0,0,0,0.6)',
      '0px 92px 184px rgba(0,0,0,0.62)',
    ],
    components: {
      MuiCard: {
        styleOverrides: {
          root: {
            boxShadow: isDarkMode
              ? '0px 4px 20px rgba(0,0,0,0.3)'
              : '0px 4px 20px rgba(0,0,0,0.08)',
            borderRadius: 16,
            border: isDarkMode
              ? '1px solid rgba(255,255,255,0.1)'
              : '1px solid rgba(0,0,0,0.05)',
            backgroundColor: isDarkMode ? '#1e1e1e' : '#ffffff',
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 10,
            textTransform: 'none',
            fontWeight: 500,
            padding: '10px 20px',
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            border: isDarkMode
              ? '1px solid rgba(255,255,255,0.1)'
              : '1px solid rgba(0,0,0,0.05)',
            backgroundColor: isDarkMode ? '#1e1e1e' : '#ffffff',
            boxShadow: isDarkMode
              ? '0px 2px 8px rgba(0,0,0,0.3)'
              : '0px 2px 8px rgba(0,0,0,0.1)',
          },
        },
      },
      MuiTableContainer: {
        styleOverrides: {
          root: {
            backgroundColor: isDarkMode ? '#1e1e1e' : '#ffffff',
          },
        },
      },
      MuiTableHead: {
        styleOverrides: {
          root: {
            backgroundColor: isDarkMode ? '#2a2a2a' : '#f5f5f5',
          },
        },
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            borderColor: isDarkMode
              ? 'rgba(255,255,255,0.1)'
              : 'rgba(0,0,0,0.12)',
          },
          head: {
            backgroundColor: isDarkMode ? '#2a2a2a' : '#f5f5f5',
            color: isDarkMode ? '#ffffff' : '#000000',
            fontWeight: 600,
          },
        },
      },
    },
  });

  React.useEffect(() => {
    // Set document direction based on language
    document.dir = language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
  }, [language]);

  React.useEffect(() => {
    // Set theme attribute on document body
    document.body.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
  }, [isDarkMode]);

  return (
    <I18nextProvider i18n={i18n}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Box sx={{ display: 'flex', minHeight: '100vh' }}>
            <Navbar
              onMenuClick={() => setSidebarOpen(!sidebarOpen)}
              sidebarOpen={sidebarOpen}
            />
            <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
            <Box
              component="main"
              sx={{
                flexGrow: 1,
                p: 0,
                mt: 8, // Account for navbar height
                mr: sidebarOpen && language === 'ar' ? { sm: '240px' } : 0,
                ml: sidebarOpen && language === 'en' ? { sm: '240px' } : 0,
                transition: theme.transitions.create(['margin'], {
                  easing: theme.transitions.easing.sharp,
                  duration: theme.transitions.duration.leavingScreen,
                }),
                backgroundColor: isDarkMode ? '#121212' : '#ffffff',
                minHeight: 'calc(100vh - 64px)',
              }}
            >
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/animals" element={<Animals />} />
                <Route path="/births" element={<Births />} />
                <Route path="/reproduction" element={<Reproduction />} />
                <Route path="/feeds" element={<Feeds />} />
                <Route path="/treatments" element={<Treatments />} />
                <Route path="/employees" element={<Employees />} />
                <Route
                  path="/purchases-expenses"
                  element={<PurchasesExpenses />}
                />
                <Route path="/sales" element={<Sales />} />
                <Route path="/reports" element={<Reports />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </Box>
          </Box>
        </Router>
      </ThemeProvider>
    </I18nextProvider>
  );
}

export default App;

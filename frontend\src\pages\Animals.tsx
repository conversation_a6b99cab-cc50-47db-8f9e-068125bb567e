import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import {
  Alert,
  Box,
  Button,
  Chip,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Snackbar,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { DataGrid, GridActionsCellItem, GridColDef } from '@mui/x-data-grid';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import DropdownManager from '../components/DropdownManager';
import { useThemeStore } from '../store/themeStore';

const Animals: React.FC = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');

  // States for real data
  const [animals, setAnimals] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // States for dropdown management
  const [animalTypes, setAnimalTypes] = useState([
    { id: '1', name: 'أغنام', nameEn: 'Sheep', icon: '🐑', active: true },
    { id: '2', name: 'ماعز', nameEn: 'Goats', icon: '🐐', active: true },
  ]);

  const [breeds, setBreeds] = useState([
    {
      id: '1',
      name: 'نجدي',
      nameEn: 'Najdi',
      icon: '🐑',
      active: true,
      animalTypeId: '1',
    },
    {
      id: '2',
      name: 'حري',
      nameEn: 'Harri',
      icon: '🐑',
      active: true,
      animalTypeId: '1',
    },
    {
      id: '3',
      name: 'عارضي',
      nameEn: 'Ardi',
      icon: '🐐',
      active: true,
      animalTypeId: '2',
    },
    {
      id: '4',
      name: 'شامي',
      nameEn: 'Damascus',
      icon: '🐐',
      active: true,
      animalTypeId: '2',
    },
  ]);

  const [categories, setCategories] = useState([
    { id: '1', name: 'أم', nameEn: 'Mother', icon: '👩', active: true },
    { id: '2', name: 'فحل', nameEn: 'Father', icon: '👨', active: true },
    { id: '3', name: 'مولود', nameEn: 'Newborn', icon: '🍼', active: true },
    { id: '4', name: 'تسمين', nameEn: 'Fattening', icon: '📈', active: true },
    { id: '5', name: 'للبيع', nameEn: 'For Sale', icon: '💰', active: true },
  ]);

  const [locations, setLocations] = useState([
    { id: '1', name: 'حظيرة 1', nameEn: 'Barn 1', icon: '🏠', active: true },
    { id: '2', name: 'حظيرة 2', nameEn: 'Barn 2', icon: '🏠', active: true },
    {
      id: '3',
      name: 'المرعى الشمالي',
      nameEn: 'North Pasture',
      icon: '🌿',
      active: true,
    },
    {
      id: '4',
      name: 'المرعى الجنوبي',
      nameEn: 'South Pasture',
      icon: '🌿',
      active: true,
    },
  ]);

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  // Load data from localStorage on component mount
  useEffect(() => {
    const savedAnimalTypes = localStorage.getItem('animalTypes');
    if (savedAnimalTypes) {
      setAnimalTypes(JSON.parse(savedAnimalTypes));
    } else {
      // إضافة بيانات افتراضية لأنواع الحيوانات
      const defaultAnimalTypes = [
        {
          id: '1',
          name: 'أغنام',
          nameEn: 'Sheep',
          icon: '🐑',
          active: true,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'ماعز',
          nameEn: 'Goats',
          icon: '🐐',
          active: true,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'أبقار',
          nameEn: 'Cattle',
          icon: '🐄',
          active: true,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'جمال',
          nameEn: 'Camels',
          icon: '🐪',
          active: true,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setAnimalTypes(defaultAnimalTypes);
      localStorage.setItem('animalTypes', JSON.stringify(defaultAnimalTypes));
    }

    const savedBreeds = localStorage.getItem('breeds');
    if (savedBreeds) {
      setBreeds(JSON.parse(savedBreeds));
    } else {
      // إضافة بيانات افتراضية للسلالات
      const defaultBreeds = [
        {
          id: '1',
          name: 'نجدي',
          nameEn: 'Najdi',
          icon: '🐑',
          active: true,
          animalTypeId: '1',
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'حري',
          nameEn: 'Harri',
          icon: '🐑',
          active: true,
          animalTypeId: '1',
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'عارضي',
          nameEn: 'Ardi',
          icon: '🐐',
          active: true,
          animalTypeId: '2',
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'شامي',
          nameEn: 'Damascus',
          icon: '🐐',
          active: true,
          animalTypeId: '2',
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '5',
          name: 'هولشتاين',
          nameEn: 'Holstein',
          icon: '🐄',
          active: true,
          animalTypeId: '3',
          order: 4,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '6',
          name: 'مجاهيم',
          nameEn: 'Magaheem',
          icon: '🐪',
          active: true,
          animalTypeId: '4',
          order: 5,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setBreeds(defaultBreeds);
      localStorage.setItem('breeds', JSON.stringify(defaultBreeds));
    }

    const savedCategories = localStorage.getItem('animalCategories');
    if (savedCategories) {
      setCategories(JSON.parse(savedCategories));
    } else {
      // إضافة بيانات افتراضية لفئات الحيوانات
      const defaultCategories = [
        {
          id: '1',
          name: 'أم',
          nameEn: 'Mother',
          icon: '👩',
          active: true,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'فحل',
          nameEn: 'Father',
          icon: '👨',
          active: true,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'مولود',
          nameEn: 'Newborn',
          icon: '🍼',
          active: true,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'تسمين',
          nameEn: 'Fattening',
          icon: '📈',
          active: true,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '5',
          name: 'للبيع',
          nameEn: 'For Sale',
          icon: '💰',
          active: true,
          order: 4,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setCategories(defaultCategories);
      localStorage.setItem(
        'animalCategories',
        JSON.stringify(defaultCategories)
      );
    }

    const savedLocations = localStorage.getItem('animalLocations');
    if (savedLocations) {
      setLocations(JSON.parse(savedLocations));
    } else {
      // إضافة بيانات افتراضية للمواقع
      const defaultLocations = [
        {
          id: '1',
          name: 'حظيرة أ',
          nameEn: 'Barn A',
          icon: '🏠',
          active: true,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'حظيرة ب',
          nameEn: 'Barn B',
          icon: '🏠',
          active: true,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'المرعى الشمالي',
          nameEn: 'North Pasture',
          icon: '🌿',
          active: true,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'المرعى الجنوبي',
          nameEn: 'South Pasture',
          icon: '🌿',
          active: true,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '5',
          name: 'حظيرة العزل',
          nameEn: 'Isolation Barn',
          icon: '🚫',
          active: true,
          order: 4,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setLocations(defaultLocations);
      localStorage.setItem('animalLocations', JSON.stringify(defaultLocations));
    }
  }, []);

  // Save functions
  const saveAnimalTypes = (newTypes: any[]) => {
    setAnimalTypes(newTypes);
    localStorage.setItem('animalTypes', JSON.stringify(newTypes));
    setSnackbar({
      open: true,
      message: 'تم حفظ أنواع الحيوانات بنجاح',
      severity: 'success',
    });
  };

  const saveBreeds = (newBreeds: any[]) => {
    setBreeds(newBreeds);
    localStorage.setItem('breeds', JSON.stringify(newBreeds));
    setSnackbar({
      open: true,
      message: 'تم حفظ السلالات بنجاح',
      severity: 'success',
    });
  };

  const saveCategories = (newCategories: any[]) => {
    setCategories(newCategories);
    localStorage.setItem('animalCategories', JSON.stringify(newCategories));
    setSnackbar({
      open: true,
      message: 'تم حفظ فئات الحيوانات بنجاح',
      severity: 'success',
    });
  };

  const saveLocations = (newLocations: any[]) => {
    setLocations(newLocations);
    localStorage.setItem('animalLocations', JSON.stringify(newLocations));
    setSnackbar({
      open: true,
      message: 'تم حفظ المواقع بنجاح',
      severity: 'success',
    });
  };

  // Load animals from API
  const loadAnimals = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getAnimals();
      console.log('Animals API response:', response);

      if (response && response.animals) {
        setAnimals(response.animals);
      } else {
        setAnimals([]);
      }
    } catch (err) {
      console.error('Error loading animals:', err);
      setError('فشل في تحميل بيانات الحيوانات');
      setAnimals([]);
    } finally {
      setLoading(false);
    }
  };

  // Load animals on component mount
  useEffect(() => {
    loadAnimals();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'alive':
        return 'success';
      case 'dead':
        return 'error';
      case 'sold':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'mother':
        return 'primary';
      case 'father':
        return 'secondary';
      case 'newborn':
        return 'info';
      case 'fattening':
        return 'warning';
      case 'forSale':
        return 'success';
      default:
        return 'default';
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'internalId',
      headerName: 'رقم التسجيل',
      width: 120,
      fontWeight: 'bold',
    },
    {
      field: 'tagNumber',
      headerName: 'رقم التاغ',
      width: 100,
    },
    {
      field: 'animalType',
      headerName: 'نوع الحيوان',
      width: 120,
      renderCell: (params) => {
        return (
          params.row.animalType?.nameAr ||
          params.row.animalType?.name ||
          'غير محدد'
        );
      },
    },
    {
      field: 'breed',
      headerName: 'السلالة',
      width: 120,
      renderCell: (params) => {
        return params.row.breed?.nameAr || params.row.breed?.name || 'غير محدد';
      },
    },
    {
      field: 'gender',
      headerName: 'الجنس',
      width: 100,
      renderCell: (params) => {
        const genderMap: { [key: string]: string } = {
          MALE: 'ذكر',
          FEMALE: 'أنثى',
          male: 'ذكر',
          female: 'أنثى',
        };
        return genderMap[params.value] || params.value;
      },
    },
    {
      field: 'category',
      headerName: 'الفئة',
      width: 120,
      renderCell: (params) => {
        const categoryMap: { [key: string]: string } = {
          MOTHER: 'أم',
          FATHER: 'فحل',
          NEWBORN: 'مولود',
          WEANED: 'مفطوم',
          FATTENING: 'تسمين',
          FOR_SALE: 'للبيع',
        };
        const categoryText = categoryMap[params.value] || params.value;
        return (
          <Chip
            label={categoryText}
            color={getCategoryColor(params.value) as any}
            size="small"
          />
        );
      },
    },
    {
      field: 'currentWeight',
      headerName: 'الوزن',
      width: 100,
      renderCell: (params) =>
        params.value ? `${params.value} كغ` : 'غير محدد',
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => {
        const statusMap: { [key: string]: string } = {
          ALIVE: 'حي',
          DEAD: 'نافق',
          SOLD: 'مباع',
          alive: 'حي',
          dead: 'نافق',
          sold: 'مباع',
        };
        const statusText = statusMap[params.value] || params.value;
        return (
          <Chip
            label={statusText}
            color={getStatusColor(params.value) as any}
            size="small"
          />
        );
      },
    },
    {
      field: 'barnLocation',
      headerName: 'الموقع',
      width: 120,
      renderCell: (params) => params.value || 'غير محدد',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="عرض">
              <ViewIcon />
            </Tooltip>
          }
          label="عرض"
          onClick={() => handleView(params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => handleEdit(params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="حذف">
              <DeleteIcon />
            </Tooltip>
          }
          label="حذف"
          onClick={() => handleDelete(params.id)}
        />,
      ],
    },
  ];

  const handleView = (id: any) => {
    console.log('View animal:', id);
  };

  const handleEdit = (id: any) => {
    console.log('Edit animal:', id);
  };

  const handleDelete = (id: any) => {
    console.log('Delete animal:', id);
  };

  const handleAddAnimal = () => {
    console.log('Add new animal');
  };

  const filteredAnimals = animals.filter((animal) => {
    const matchesSearch =
      animal.internalId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      animal.tagNumber?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType =
      !filterType ||
      animal.animalType?.name?.toLowerCase() === filterType.toLowerCase() ||
      animal.animalType?.nameEn?.toLowerCase() === filterType.toLowerCase();

    const matchesStatus =
      !filterStatus ||
      animal.status?.toLowerCase() === filterStatus.toLowerCase();

    return matchesSearch && matchesType && matchesStatus;
  });

  return (
    <Box
      sx={{
        backgroundColor: isDarkMode ? 'grey.900' : 'background.default',
        minHeight: '100vh',
        p: 2,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
        }}
      >
        <Typography variant="h4" component="h1" fontWeight="bold">
          {t('animals')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddAnimal}
        >
          {t('add')} {t('animals')}
        </Button>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              placeholder={`${t('search')}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <InputLabel>{t('animalType')}</InputLabel>
              <Select
                value={filterType}
                label={t('animalType')}
                onChange={(e) => setFilterType(e.target.value)}
              >
                <MenuItem value="">{t('all')}</MenuItem>
                <MenuItem value="sheep">{t('sheep')}</MenuItem>
                <MenuItem value="goat">{t('goats')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <InputLabel>{t('status')}</InputLabel>
              <Select
                value={filterStatus}
                label={t('status')}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="">{t('all')}</MenuItem>
                <MenuItem value="alive">{t('alive')}</MenuItem>
                <MenuItem value="dead">{t('dead')}</MenuItem>
                <MenuItem value="sold">{t('sold')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button fullWidth variant="outlined" startIcon={<FilterIcon />}>
              {t('filter')}
            </Button>
          </Grid>
        </Grid>

        {/* Dropdown Management Section */}
        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              إدارة القوائم المنسدلة
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="أنواع الحيوانات"
              items={animalTypes}
              onSave={saveAnimalTypes}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="السلالات"
              items={breeds}
              onSave={saveBreeds}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                {
                  key: 'animalTypeId',
                  label: 'نوع الحيوان',
                  type: 'select',
                  options: animalTypes.map((type) => ({
                    value: type.id,
                    label: type.name,
                  })),
                },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="فئات الحيوانات"
              items={categories}
              onSave={saveCategories}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="المواقع"
              items={locations}
              onSave={saveLocations}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Data Grid */}
      <Paper sx={{ height: 600, width: '100%', position: 'relative' }}>
        {loading ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              flexDirection: 'column',
              gap: 2,
            }}
          >
            <CircularProgress />
            <Typography>جاري تحميل بيانات الحيوانات...</Typography>
          </Box>
        ) : error ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              flexDirection: 'column',
              gap: 2,
            }}
          >
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
            <Button variant="contained" onClick={loadAnimals}>
              إعادة المحاولة
            </Button>
          </Box>
        ) : (
          <DataGrid
            rows={filteredAnimals}
            columns={columns}
            initialState={{
              pagination: {
                paginationModel: { page: 0, pageSize: 10 },
              },
            }}
            pageSizeOptions={[5, 10, 25]}
            checkboxSelection
            disableRowSelectionOnClick
            sx={{
              '& .MuiDataGrid-root': {
                border: 'none',
              },
              '& .MuiDataGrid-cell': {
                borderBottom: 'none',
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: 'background.paper',
                borderBottom: 'none',
              },
            }}
          />
        )}
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Animals;

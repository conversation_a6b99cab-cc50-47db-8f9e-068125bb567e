import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { DataGrid, GridActionsCellItem, GridColDef } from '@mui/x-data-grid';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeStore } from '../store/themeStore';

const Animals: React.FC = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');

  // Mock data - في التطبيق الحقيقي، ستأتي هذه البيانات من API
  const animals = [
    {
      id: '1',
      internalId: 'A000001',
      tagNumber: 'T001',
      animalType: 'sheep',
      breed: 'najdi',
      gender: 'female',
      category: 'mother',
      birthDate: '2022-03-15',
      currentWeight: 45.5,
      status: 'alive',
      location: 'حظيرة 1',
    },
    {
      id: '2',
      internalId: 'A000002',
      tagNumber: 'T002',
      animalType: 'goat',
      breed: 'ardi',
      gender: 'male',
      category: 'father',
      birthDate: '2021-08-20',
      currentWeight: 55.2,
      status: 'alive',
      location: 'حظيرة 2',
    },
    {
      id: '3',
      internalId: 'A000003',
      tagNumber: 'T003',
      animalType: 'sheep',
      breed: 'harri',
      gender: 'female',
      category: 'fattening',
      birthDate: '2023-01-10',
      currentWeight: 32.8,
      status: 'alive',
      location: 'حظيرة 1',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'alive':
        return 'success';
      case 'dead':
        return 'error';
      case 'sold':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'mother':
        return 'primary';
      case 'father':
        return 'secondary';
      case 'newborn':
        return 'info';
      case 'fattening':
        return 'warning';
      case 'forSale':
        return 'success';
      default:
        return 'default';
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'internalId',
      headerName: t('animalId'),
      width: 120,
      fontWeight: 'bold',
    },
    {
      field: 'tagNumber',
      headerName: t('tagNumber'),
      width: 100,
    },
    {
      field: 'animalType',
      headerName: t('animalType'),
      width: 120,
      renderCell: (params) => t(params.value),
    },
    {
      field: 'breed',
      headerName: t('breed'),
      width: 120,
      renderCell: (params) => {
        const breedNames: { [key: string]: string } = {
          najdi: 'نجدي',
          harri: 'حري',
          ardi: 'عارضي',
          damascus: 'شامي',
        };
        return breedNames[params.value] || params.value;
      },
    },
    {
      field: 'gender',
      headerName: t('gender'),
      width: 100,
      renderCell: (params) => t(params.value),
    },
    {
      field: 'category',
      headerName: t('category'),
      width: 120,
      renderCell: (params) => (
        <Chip
          label={t(params.value)}
          color={getCategoryColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'currentWeight',
      headerName: t('weight'),
      width: 100,
      renderCell: (params) => `${params.value} كغ`,
    },
    {
      field: 'status',
      headerName: t('status'),
      width: 100,
      renderCell: (params) => (
        <Chip
          label={t(params.value)}
          color={getStatusColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'location',
      headerName: t('location'),
      width: 120,
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="عرض">
              <ViewIcon />
            </Tooltip>
          }
          label="عرض"
          onClick={() => handleView(params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => handleEdit(params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="حذف">
              <DeleteIcon />
            </Tooltip>
          }
          label="حذف"
          onClick={() => handleDelete(params.id)}
        />,
      ],
    },
  ];

  const handleView = (id: any) => {
    console.log('View animal:', id);
  };

  const handleEdit = (id: any) => {
    console.log('Edit animal:', id);
  };

  const handleDelete = (id: any) => {
    console.log('Delete animal:', id);
  };

  const handleAddAnimal = () => {
    console.log('Add new animal');
  };

  const filteredAnimals = animals.filter((animal) => {
    const matchesSearch =
      animal.internalId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      animal.tagNumber?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = !filterType || animal.animalType === filterType;
    const matchesStatus = !filterStatus || animal.status === filterStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  return (
    <Box
      sx={{
        backgroundColor: isDarkMode ? 'grey.900' : 'background.default',
        minHeight: '100vh',
        p: 2,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
        }}
      >
        <Typography variant="h4" component="h1" fontWeight="bold">
          {t('animals')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddAnimal}
        >
          {t('add')} {t('animals')}
        </Button>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              placeholder={`${t('search')}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <InputLabel>{t('animalType')}</InputLabel>
              <Select
                value={filterType}
                label={t('animalType')}
                onChange={(e) => setFilterType(e.target.value)}
              >
                <MenuItem value="">{t('all')}</MenuItem>
                <MenuItem value="sheep">{t('sheep')}</MenuItem>
                <MenuItem value="goat">{t('goats')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <InputLabel>{t('status')}</InputLabel>
              <Select
                value={filterStatus}
                label={t('status')}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="">{t('all')}</MenuItem>
                <MenuItem value="alive">{t('alive')}</MenuItem>
                <MenuItem value="dead">{t('dead')}</MenuItem>
                <MenuItem value="sold">{t('sold')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button fullWidth variant="outlined" startIcon={<FilterIcon />}>
              {t('filter')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredAnimals}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          pageSizeOptions={[5, 10, 25]}
          checkboxSelection
          disableRowSelectionOnClick
          sx={{
            '& .MuiDataGrid-root': {
              border: 'none',
            },
            '& .MuiDataGrid-cell': {
              borderBottom: 'none',
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: 'background.paper',
              borderBottom: 'none',
            },
          }}
        />
      </Paper>
    </Box>
  );
};

export default Animals;

import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { DataGrid, GridActionsCellItem, GridColDef } from '@mui/x-data-grid';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import DropdownManager from '../components/DropdownManager';
import { useThemeStore } from '../store/themeStore';

const Animals: React.FC = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');

  // States for dropdown management
  const [animalTypes, setAnimalTypes] = useState([
    { id: '1', name: 'أغنام', nameEn: 'Sheep', icon: '🐑', active: true },
    { id: '2', name: 'ماعز', nameEn: 'Goats', icon: '🐐', active: true },
  ]);

  const [breeds, setBreeds] = useState([
    {
      id: '1',
      name: 'نجدي',
      nameEn: 'Najdi',
      icon: '🐑',
      active: true,
      animalTypeId: '1',
    },
    {
      id: '2',
      name: 'حري',
      nameEn: 'Harri',
      icon: '🐑',
      active: true,
      animalTypeId: '1',
    },
    {
      id: '3',
      name: 'عارضي',
      nameEn: 'Ardi',
      icon: '🐐',
      active: true,
      animalTypeId: '2',
    },
    {
      id: '4',
      name: 'شامي',
      nameEn: 'Damascus',
      icon: '🐐',
      active: true,
      animalTypeId: '2',
    },
  ]);

  const [categories, setCategories] = useState([
    { id: '1', name: 'أم', nameEn: 'Mother', icon: '👩', active: true },
    { id: '2', name: 'فحل', nameEn: 'Father', icon: '👨', active: true },
    { id: '3', name: 'مولود', nameEn: 'Newborn', icon: '🍼', active: true },
    { id: '4', name: 'تسمين', nameEn: 'Fattening', icon: '📈', active: true },
    { id: '5', name: 'للبيع', nameEn: 'For Sale', icon: '💰', active: true },
  ]);

  const [locations, setLocations] = useState([
    { id: '1', name: 'حظيرة 1', nameEn: 'Barn 1', icon: '🏠', active: true },
    { id: '2', name: 'حظيرة 2', nameEn: 'Barn 2', icon: '🏠', active: true },
    {
      id: '3',
      name: 'المرعى الشمالي',
      nameEn: 'North Pasture',
      icon: '🌿',
      active: true,
    },
    {
      id: '4',
      name: 'المرعى الجنوبي',
      nameEn: 'South Pasture',
      icon: '🌿',
      active: true,
    },
  ]);

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  // Load data from localStorage on component mount
  useEffect(() => {
    const savedAnimalTypes = localStorage.getItem('animalTypes');
    if (savedAnimalTypes) {
      setAnimalTypes(JSON.parse(savedAnimalTypes));
    }

    const savedBreeds = localStorage.getItem('breeds');
    if (savedBreeds) {
      setBreeds(JSON.parse(savedBreeds));
    }

    const savedCategories = localStorage.getItem('animalCategories');
    if (savedCategories) {
      setCategories(JSON.parse(savedCategories));
    }

    const savedLocations = localStorage.getItem('animalLocations');
    if (savedLocations) {
      setLocations(JSON.parse(savedLocations));
    }
  }, []);

  // Save functions
  const saveAnimalTypes = (newTypes: any[]) => {
    setAnimalTypes(newTypes);
    localStorage.setItem('animalTypes', JSON.stringify(newTypes));
    setSnackbar({
      open: true,
      message: 'تم حفظ أنواع الحيوانات بنجاح',
      severity: 'success',
    });
  };

  const saveBreeds = (newBreeds: any[]) => {
    setBreeds(newBreeds);
    localStorage.setItem('breeds', JSON.stringify(newBreeds));
    setSnackbar({
      open: true,
      message: 'تم حفظ السلالات بنجاح',
      severity: 'success',
    });
  };

  const saveCategories = (newCategories: any[]) => {
    setCategories(newCategories);
    localStorage.setItem('animalCategories', JSON.stringify(newCategories));
    setSnackbar({
      open: true,
      message: 'تم حفظ فئات الحيوانات بنجاح',
      severity: 'success',
    });
  };

  const saveLocations = (newLocations: any[]) => {
    setLocations(newLocations);
    localStorage.setItem('animalLocations', JSON.stringify(newLocations));
    setSnackbar({
      open: true,
      message: 'تم حفظ المواقع بنجاح',
      severity: 'success',
    });
  };

  // Mock data - في التطبيق الحقيقي، ستأتي هذه البيانات من API
  const animals = [
    {
      id: '1',
      internalId: 'A000001',
      tagNumber: 'T001',
      animalType: 'sheep',
      breed: 'najdi',
      gender: 'female',
      category: 'mother',
      birthDate: '2022-03-15',
      currentWeight: 45.5,
      status: 'alive',
      location: 'حظيرة 1',
    },
    {
      id: '2',
      internalId: 'A000002',
      tagNumber: 'T002',
      animalType: 'goat',
      breed: 'ardi',
      gender: 'male',
      category: 'father',
      birthDate: '2021-08-20',
      currentWeight: 55.2,
      status: 'alive',
      location: 'حظيرة 2',
    },
    {
      id: '3',
      internalId: 'A000003',
      tagNumber: 'T003',
      animalType: 'sheep',
      breed: 'harri',
      gender: 'female',
      category: 'fattening',
      birthDate: '2023-01-10',
      currentWeight: 32.8,
      status: 'alive',
      location: 'حظيرة 1',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'alive':
        return 'success';
      case 'dead':
        return 'error';
      case 'sold':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'mother':
        return 'primary';
      case 'father':
        return 'secondary';
      case 'newborn':
        return 'info';
      case 'fattening':
        return 'warning';
      case 'forSale':
        return 'success';
      default:
        return 'default';
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'internalId',
      headerName: t('animalId'),
      width: 120,
      fontWeight: 'bold',
    },
    {
      field: 'tagNumber',
      headerName: t('tagNumber'),
      width: 100,
    },
    {
      field: 'animalType',
      headerName: t('animalType'),
      width: 120,
      renderCell: (params) => t(params.value),
    },
    {
      field: 'breed',
      headerName: t('breed'),
      width: 120,
      renderCell: (params) => {
        const breedNames: { [key: string]: string } = {
          najdi: 'نجدي',
          harri: 'حري',
          ardi: 'عارضي',
          damascus: 'شامي',
        };
        return breedNames[params.value] || params.value;
      },
    },
    {
      field: 'gender',
      headerName: t('gender'),
      width: 100,
      renderCell: (params) => t(params.value),
    },
    {
      field: 'category',
      headerName: t('category'),
      width: 120,
      renderCell: (params) => (
        <Chip
          label={t(params.value)}
          color={getCategoryColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'currentWeight',
      headerName: t('weight'),
      width: 100,
      renderCell: (params) => `${params.value} كغ`,
    },
    {
      field: 'status',
      headerName: t('status'),
      width: 100,
      renderCell: (params) => (
        <Chip
          label={t(params.value)}
          color={getStatusColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'location',
      headerName: t('location'),
      width: 120,
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="عرض">
              <ViewIcon />
            </Tooltip>
          }
          label="عرض"
          onClick={() => handleView(params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="تعديل">
              <EditIcon />
            </Tooltip>
          }
          label="تعديل"
          onClick={() => handleEdit(params.id)}
        />,
        <GridActionsCellItem
          icon={
            <Tooltip title="حذف">
              <DeleteIcon />
            </Tooltip>
          }
          label="حذف"
          onClick={() => handleDelete(params.id)}
        />,
      ],
    },
  ];

  const handleView = (id: any) => {
    console.log('View animal:', id);
  };

  const handleEdit = (id: any) => {
    console.log('Edit animal:', id);
  };

  const handleDelete = (id: any) => {
    console.log('Delete animal:', id);
  };

  const handleAddAnimal = () => {
    console.log('Add new animal');
  };

  const filteredAnimals = animals.filter((animal) => {
    const matchesSearch =
      animal.internalId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      animal.tagNumber?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = !filterType || animal.animalType === filterType;
    const matchesStatus = !filterStatus || animal.status === filterStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  return (
    <Box
      sx={{
        backgroundColor: isDarkMode ? 'grey.900' : 'background.default',
        minHeight: '100vh',
        p: 2,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
        }}
      >
        <Typography variant="h4" component="h1" fontWeight="bold">
          {t('animals')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddAnimal}
        >
          {t('add')} {t('animals')}
        </Button>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              placeholder={`${t('search')}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <InputLabel>{t('animalType')}</InputLabel>
              <Select
                value={filterType}
                label={t('animalType')}
                onChange={(e) => setFilterType(e.target.value)}
              >
                <MenuItem value="">{t('all')}</MenuItem>
                <MenuItem value="sheep">{t('sheep')}</MenuItem>
                <MenuItem value="goat">{t('goats')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth>
              <InputLabel>{t('status')}</InputLabel>
              <Select
                value={filterStatus}
                label={t('status')}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="">{t('all')}</MenuItem>
                <MenuItem value="alive">{t('alive')}</MenuItem>
                <MenuItem value="dead">{t('dead')}</MenuItem>
                <MenuItem value="sold">{t('sold')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button fullWidth variant="outlined" startIcon={<FilterIcon />}>
              {t('filter')}
            </Button>
          </Grid>
        </Grid>

        {/* Dropdown Management Section */}
        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              إدارة القوائم المنسدلة
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="أنواع الحيوانات"
              items={animalTypes}
              onSave={saveAnimalTypes}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="السلالات"
              items={breeds}
              onSave={saveBreeds}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                {
                  key: 'animalTypeId',
                  label: 'نوع الحيوان',
                  type: 'select',
                  options: animalTypes.map((type) => ({
                    value: type.id,
                    label: type.name,
                  })),
                },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="فئات الحيوانات"
              items={categories}
              onSave={saveCategories}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DropdownManager
              title="المواقع"
              items={locations}
              onSave={saveLocations}
              fields={[
                {
                  key: 'name',
                  label: 'الاسم بالعربية',
                  type: 'text',
                  required: true,
                },
                { key: 'nameEn', label: 'الاسم بالإنجليزية', type: 'text' },
                { key: 'icon', label: 'الأيقونة', type: 'icon' },
              ]}
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredAnimals}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          pageSizeOptions={[5, 10, 25]}
          checkboxSelection
          disableRowSelectionOnClick
          sx={{
            '& .MuiDataGrid-root': {
              border: 'none',
            },
            '& .MuiDataGrid-cell': {
              borderBottom: 'none',
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: 'background.paper',
              borderBottom: 'none',
            },
          }}
        />
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Animals;

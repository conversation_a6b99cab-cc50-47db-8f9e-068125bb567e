<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة الحيوانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            background-color: #fff0f0;
            border: 1px solid #f44336;
            color: #d32f2f;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #f0fff0;
            border: 1px solid #4CAF50;
            color: #2e7d32;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.alive {
            background-color: #e8f5e8;
            color: #2e7d32;
        }
        .status.dead {
            background-color: #ffebee;
            color: #d32f2f;
        }
        .status.sold {
            background-color: #fff3e0;
            color: #f57c00;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .debug {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐑 اختبار صفحة الحيوانات</h1>
            <p>محاكاة سلوك صفحة الحيوانات في التطبيق</p>
        </div>

        <div>
            <button onclick="loadAnimals()">تحميل الحيوانات</button>
            <button onclick="clearResults()">مسح النتائج</button>
        </div>

        <div id="status" class="loading">جاري التحميل...</div>
        <div id="debug" class="debug"></div>
        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3001';
        
        function log(message) {
            const debugDiv = document.getElementById('debug');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.textContent += `[${timestamp}] ${message}\n`;
            console.log(message);
        }

        function setStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = type;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('debug').textContent = '';
            setStatus('جاهز للتحميل');
        }

        async function loadAnimals() {
            log('🔄 بدء تحميل الحيوانات...');
            setStatus('جاري تحميل الحيوانات...', 'loading');
            
            try {
                log(`📡 إرسال طلب إلى: ${API_BASE_URL}/api/animals`);
                
                const response = await fetch(`${API_BASE_URL}/api/animals`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                log(`📨 استلام رد: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                log(`📊 البيانات المستلمة: ${JSON.stringify(data, null, 2)}`);

                if (data && data.animals) {
                    displayAnimals(data.animals);
                    setStatus(`✅ تم تحميل ${data.animals.length} حيوان بنجاح`, 'success');
                    log(`✅ تم عرض ${data.animals.length} حيوان`);
                } else {
                    setStatus('⚠️ لا توجد حيوانات في قاعدة البيانات', 'error');
                    log('⚠️ البيانات المستلمة لا تحتوي على حيوانات');
                }

            } catch (error) {
                log(`❌ خطأ في تحميل الحيوانات: ${error.message}`);
                setStatus(`❌ فشل في تحميل الحيوانات: ${error.message}`, 'error');
                
                // محاولة تشخيص المشكلة
                try {
                    log('🔍 محاولة اختبار الاتصال بالخادم...');
                    const healthResponse = await fetch(`${API_BASE_URL}/health`);
                    if (healthResponse.ok) {
                        log('✅ الخادم يعمل بشكل طبيعي');
                    } else {
                        log(`❌ مشكلة في الخادم: ${healthResponse.status}`);
                    }
                } catch (healthError) {
                    log(`❌ لا يمكن الوصول للخادم: ${healthError.message}`);
                }
            }
        }

        function displayAnimals(animals) {
            const resultsDiv = document.getElementById('results');
            
            if (!animals || animals.length === 0) {
                resultsDiv.innerHTML = '<p>لا توجد حيوانات للعرض</p>';
                return;
            }

            let html = '<table>';
            html += '<tr>';
            html += '<th>رقم التسجيل</th>';
            html += '<th>رقم التاغ</th>';
            html += '<th>النوع</th>';
            html += '<th>السلالة</th>';
            html += '<th>الجنس</th>';
            html += '<th>الفئة</th>';
            html += '<th>الحالة</th>';
            html += '<th>تاريخ الإنشاء</th>';
            html += '</tr>';

            animals.forEach(animal => {
                html += '<tr>';
                html += `<td>${animal.internalId || 'غير محدد'}</td>`;
                html += `<td>${animal.tagNumber || 'غير محدد'}</td>`;
                html += `<td>${getAnimalTypeName(animal)}</td>`;
                html += `<td>${getBreedName(animal)}</td>`;
                html += `<td>${getGenderName(animal.gender)}</td>`;
                html += `<td>${getCategoryName(animal.category)}</td>`;
                html += `<td><span class="status ${getStatusClass(animal.status)}">${getStatusName(animal.status)}</span></td>`;
                html += `<td>${formatDate(animal.createdAt)}</td>`;
                html += '</tr>';
            });

            html += '</table>';
            resultsDiv.innerHTML = html;
        }

        function getAnimalTypeName(animal) {
            if (animal.animalType) {
                return animal.animalType.nameAr || animal.animalType.name || 'غير محدد';
            }
            return 'غير محدد';
        }

        function getBreedName(animal) {
            if (animal.breed) {
                return animal.breed.nameAr || animal.breed.name || 'غير محدد';
            }
            return 'غير محدد';
        }

        function getGenderName(gender) {
            const genderMap = {
                'MALE': 'ذكر',
                'FEMALE': 'أنثى',
                'male': 'ذكر',
                'female': 'أنثى'
            };
            return genderMap[gender] || gender || 'غير محدد';
        }

        function getCategoryName(category) {
            const categoryMap = {
                'MOTHER': 'أم',
                'FATHER': 'فحل',
                'NEWBORN': 'مولود',
                'WEANED': 'مفطوم',
                'FATTENING': 'تسمين',
                'FOR_SALE': 'للبيع'
            };
            return categoryMap[category] || category || 'غير محدد';
        }

        function getStatusName(status) {
            const statusMap = {
                'ALIVE': 'حي',
                'DEAD': 'نافق',
                'SOLD': 'مباع'
            };
            return statusMap[status] || status || 'غير محدد';
        }

        function getStatusClass(status) {
            const statusClassMap = {
                'ALIVE': 'alive',
                'DEAD': 'dead',
                'SOLD': 'sold'
            };
            return statusClassMap[status] || '';
        }

        function formatDate(dateString) {
            if (!dateString) return 'غير محدد';
            try {
                return new Date(dateString).toLocaleDateString('ar-SA');
            } catch {
                return dateString;
            }
        }

        // تحميل تلقائي عند فتح الصفحة
        window.onload = function() {
            log('🚀 تم تحميل الصفحة');
            loadAnimals();
        };
    </script>
</body>
</html>

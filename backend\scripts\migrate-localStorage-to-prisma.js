const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Sample data from localStorage (based on AnimalsSimple.tsx)
const sampleAnimals = [
  {
    id: '1',
    registrationNumber: '2024-0001',
    tagNumber: 'SH001',
    type: 'sheep',
    breed: 'نجدي',
    gender: 'male',
    birthDate: '2023-03-15',
    weight: 45,
    status: 'present',
    barnLocation: 'حظيرة أ',
    joinDate: '2023-04-10',
    notes: 'خروف نجدي ممتاز للتربية',
  },
  {
    id: '2',
    registrationNumber: '2024-0002',
    tagNumber: 'SH002',
    type: 'sheep',
    breed: 'حري',
    gender: 'female',
    birthDate: '2022-12-10',
    weight: 38,
    status: 'present',
    barnLocation: 'حظيرة ب',
    joinDate: '2023-02-08',
    notes: 'نعجة منتجة، ولدت توأم العام الماضي',
  },
  {
    id: '3',
    registrationNumber: '2024-0003',
    tagNumber: 'GT001',
    type: 'goat',
    breed: 'شامي',
    gender: 'female',
    birthDate: '2023-01-20',
    weight: 42,
    status: 'present',
    barnLocation: 'حظيرة أ',
    joinDate: '2023-03-15',
    notes: 'عنزة شامية أصيلة',
  },
  {
    id: '4',
    registrationNumber: '2024-0004',
    tagNumber: 'GT002',
    type: 'goat',
    breed: 'عارضي',
    gender: 'male',
    birthDate: '2023-02-28',
    weight: 35,
    status: 'sold',
    barnLocation: 'حظيرة ب',
    joinDate: '2023-04-01',
    notes: 'تيس عارضي قوي البنية',
  },
  {
    id: '5',
    registrationNumber: '2024-0005',
    tagNumber: 'QR0000',
    type: 'sheep',
    breed: 'نجدي',
    gender: 'male',
    birthDate: '2023-05-10',
    weight: 50,
    status: 'present',
    barnLocation: 'المرعى الشمالي',
    joinDate: '2023-06-15',
    notes: 'خروف نجدي كبير الحجم',
  },
  {
    id: '6',
    registrationNumber: '2024-0006',
    tagNumber: 'PM01',
    type: 'sheep',
    breed: 'حري',
    gender: 'female',
    birthDate: '2023-04-05',
    weight: 40,
    status: 'present',
    barnLocation: 'حظيرة أ',
    joinDate: '2023-05-20',
    notes: 'نعجة حري جيدة الإنتاج',
  },
];

// Animal types data
const animalTypes = [
  { id: '1', name: 'أغنام', nameEn: 'Sheep', nameAr: 'أغنام' },
  { id: '2', name: 'ماعز', nameEn: 'Goats', nameAr: 'ماعز' },
  { id: '3', name: 'أبقار', nameEn: 'Cattle', nameAr: 'أبقار' },
  { id: '4', name: 'جمال', nameEn: 'Camels', nameAr: 'جمال' },
];

// Breeds data
const breeds = [
  { id: '1', name: 'نجدي', nameEn: 'Najdi', nameAr: 'نجدي', animalTypeId: '1' },
  { id: '2', name: 'حري', nameEn: 'Harri', nameAr: 'حري', animalTypeId: '1' },
  {
    id: '3',
    name: 'عارضي',
    nameEn: 'Ardi',
    nameAr: 'عارضي',
    animalTypeId: '2',
  },
  {
    id: '4',
    name: 'شامي',
    nameEn: 'Damascus',
    nameAr: 'شامي',
    animalTypeId: '2',
  },
  {
    id: '5',
    name: 'هولشتاين',
    nameEn: 'Holstein',
    nameAr: 'هولشتاين',
    animalTypeId: '3',
  },
  {
    id: '6',
    name: 'مجاهيم',
    nameEn: 'Magaheem',
    nameAr: 'مجاهيم',
    animalTypeId: '4',
  },
];

async function migrateData() {
  try {
    console.log('🚀 بدء نقل البيانات من localStorage إلى Prisma...');

    // Clear existing data in correct order (respecting foreign keys)
    console.log('🗑️ حذف البيانات الموجودة...');

    // Delete all related records first
    await prisma.stageTransition.deleteMany();
    await prisma.birth.deleteMany();
    await prisma.treatment.deleteMany();
    await prisma.animalMovement.deleteMany();
    await prisma.saleAnimal.deleteMany();
    await prisma.reproductionCycle.deleteMany();
    await prisma.weightRecord.deleteMany();
    await prisma.fatteningAnimal.deleteMany();
    await prisma.reproductionStage.deleteMany();

    // Clear self-referencing relationships in animals
    await prisma.animal.updateMany({
      data: {
        motherId: null,
        fatherId: null,
      },
    });

    // Now delete animals
    await prisma.animal.deleteMany();
    await prisma.breed.deleteMany();
    await prisma.animalType.deleteMany();

    // Create animal types
    console.log('📝 إنشاء أنواع الحيوانات...');
    for (const animalType of animalTypes) {
      await prisma.animalType.create({
        data: {
          id: animalType.id,
          name: animalType.name,
          nameEn: animalType.nameEn,
          nameAr: animalType.nameAr,
        },
      });
    }

    // Create breeds
    console.log('📝 إنشاء السلالات...');
    for (const breed of breeds) {
      await prisma.breed.create({
        data: {
          id: breed.id,
          name: breed.name,
          nameEn: breed.nameEn,
          nameAr: breed.nameAr,
          animalTypeId: breed.animalTypeId,
        },
      });
    }

    // Create animals
    console.log('📝 إنشاء الحيوانات...');
    for (const animal of sampleAnimals) {
      // Find animal type and breed
      const animalType = animalTypes.find(
        (at) => at.name === (animal.type === 'sheep' ? 'أغنام' : 'ماعز')
      );
      const breed = breeds.find((b) => b.name === animal.breed);

      // Map status values
      let status = 'ALIVE';
      if (animal.status === 'sold') status = 'SOLD';
      else if (animal.status === 'lost') status = 'DEAD';

      // Map gender values
      const gender = animal.gender === 'male' ? 'MALE' : 'FEMALE';

      await prisma.animal.create({
        data: {
          id: animal.id,
          internalId: animal.registrationNumber,
          tagNumber: animal.tagNumber,
          animalTypeId: animalType?.id || '1',
          breedId: breed?.id || '1',
          gender: gender,
          birthDate: new Date(animal.birthDate),
          currentWeight: animal.weight,
          status: status,
          barnLocation: animal.barnLocation,
          notes: animal.notes,
          category: 'MOTHER', // Default category
          source: 'INTERNAL',
        },
      });
    }

    console.log('✅ تم نقل البيانات بنجاح!');
    console.log(`📊 تم إنشاء ${animalTypes.length} نوع حيوان`);
    console.log(`📊 تم إنشاء ${breeds.length} سلالة`);
    console.log(`📊 تم إنشاء ${sampleAnimals.length} حيوان`);
  } catch (error) {
    console.error('❌ خطأ في نقل البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateData();

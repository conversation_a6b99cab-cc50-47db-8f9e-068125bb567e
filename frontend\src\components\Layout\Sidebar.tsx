import {
  ChildCare as BirthsIcon,
  Dashboard as DashboardIcon,
  People as EmployeesIcon,
  Grass as FeedsIcon,
  Pets as PetsIcon,
  AttachMoney as PurchasesExpensesIcon,
  Assessment as ReportsIcon,
  FavoriteOutlined as ReproductionIcon,
  ShoppingCart as SalesIcon,
  Settings as SettingsIcon,
  LocalHospital as TreatmentsIcon,
} from '@mui/icons-material';
import {
  Avatar,
  Box,
  Divider,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../store/themeStore';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ open, onClose }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { isDarkMode } = useThemeStore();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const menuItems = [
    { text: t('dashboard'), icon: <DashboardIcon />, path: '/dashboard' },
    { text: t('animals'), icon: <PetsIcon />, path: '/animals' },
    { text: 'المواليد', icon: <BirthsIcon />, path: '/births' },
    {
      text: t('reproduction'),
      icon: <ReproductionIcon />,
      path: '/reproduction',
    },
    { text: t('feeds'), icon: <FeedsIcon />, path: '/feeds' },
    { text: t('treatments'), icon: <TreatmentsIcon />, path: '/treatments' },
    { text: t('employees'), icon: <EmployeesIcon />, path: '/employees' },
    {
      text: 'المشتريات والمصروفات',
      icon: <PurchasesExpensesIcon />,
      path: '/purchases-expenses',
    },
    { text: t('sales'), icon: <SalesIcon />, path: '/sales' },
    { text: t('reports'), icon: <ReportsIcon />, path: '/reports' },
    { text: t('settings'), icon: <SettingsIcon />, path: '/settings' },
  ];

  const handleItemClick = (path: string) => {
    navigate(path);
    if (isMobile) {
      onClose();
    }
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box
        sx={{
          height: 64,
          display: 'flex',
          alignItems: 'center',
          px: 2,
          background: isDarkMode
            ? 'linear-gradient(135deg, #1565c0 0%, #1976d2 100%)'
            : 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
          color: 'white',
        }}
      >
        <Avatar
          sx={{
            bgcolor: 'rgba(255,255,255,0.2)',
            mr: theme.direction === 'rtl' ? 0 : 1,
            ml: theme.direction === 'rtl' ? 1 : 0,
            width: 40,
            height: 40,
          }}
        >
          🐑
        </Avatar>
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1rem' }}>
            مزرعة الأمل
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.8 }}>
            نظام الإدارة
          </Typography>
        </Box>
      </Box>

      <Divider />

      {/* Menu Items */}
      <List sx={{ flexGrow: 1, py: 1 }}>
        {menuItems.map((item) => (
          <ListItem key={item.path} disablePadding sx={{ mb: 0.5, px: 1 }}>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => handleItemClick(item.path)}
              sx={{
                borderRadius: 2,
                mx: 1,
                '&.Mui-selected': {
                  backgroundColor: theme.palette.primary.main,
                  color: 'white',
                  '&:hover': {
                    backgroundColor: theme.palette.primary.dark,
                  },
                  '& .MuiListItemIcon-root': {
                    color: 'white',
                  },
                },
                '&:hover': {
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              <ListItemIcon
                sx={{
                  color:
                    location.pathname === item.path
                      ? 'white'
                      : theme.palette.text.secondary,
                  minWidth: 40,
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.text}
                primaryTypographyProps={{
                  fontWeight: location.pathname === item.path ? 600 : 400,
                  fontSize: '0.95rem',
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Typography
          variant="caption"
          color="text.secondary"
          align="center"
          display="block"
        >
          الإصدار 1.0.0
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Drawer
      variant={isMobile ? 'temporary' : 'persistent'}
      anchor={theme.direction === 'rtl' ? 'right' : 'left'}
      open={open}
      onClose={onClose}
      sx={{
        width: 240,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 240,
          boxSizing: 'border-box',
          borderRight: theme.direction === 'rtl' ? '1px solid' : 'none',
          borderLeft: theme.direction === 'ltr' ? '1px solid' : 'none',
          borderColor: 'divider',
          background: theme.palette.background.paper,
          boxShadow:
            theme.direction === 'rtl'
              ? '-4px 0 8px rgba(0,0,0,0.1)'
              : '4px 0 8px rgba(0,0,0,0.1)',
        },
      }}
    >
      {drawerContent}
    </Drawer>
  );
};

export default Sidebar;

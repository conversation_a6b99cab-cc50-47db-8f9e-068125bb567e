import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import i18n from '../i18n/config';

interface LanguageState {
  language: 'ar' | 'en';
  setLanguage: (lang: 'ar' | 'en') => void;
  toggleLanguage: () => void;
}

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set, get) => ({
      language: 'ar',
      setLanguage: (lang: 'ar' | 'en') => {
        set({ language: lang });
        i18n.changeLanguage(lang);
      },
      toggleLanguage: () => {
        const currentLang = get().language;
        const newLang = currentLang === 'ar' ? 'en' : 'ar';
        get().setLanguage(newLang);
      },
    }),
    {
      name: 'farm-language-storage',
    }
  )
);

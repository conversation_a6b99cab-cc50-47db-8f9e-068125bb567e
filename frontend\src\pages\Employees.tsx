import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  History as HistoryIcon,
  Assignment as PayrollIcon,
  Person as PersonIcon,
  AttachMoney as SalaryIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import {
  Alert,
  Avatar,
  Box,
  Button,
  Card,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Snackbar,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useThemeStore } from '../store/themeStore';

// Types
interface Employee {
  id: string;
  nameAr: string;
  nameEn?: string;
  idNumber: string;
  phone: string;
  address: string;
  hireDate: string;
  position: string;
  status: 'active' | 'inactive' | 'resigned';
  photo?: string;
  salary: {
    basicSalary: number;
    salaryType: 'monthly' | 'weekly' | 'daily';
    startDate: string;
    allowances: Allowance[];
    deductions: Deduction[];
  };
  notes?: string;
}

interface Allowance {
  id: string;
  name: string;
  amount: number;
  type: 'fixed' | 'percentage';
}

interface Deduction {
  id: string;
  name: string;
  amount: number;
  type: 'fixed' | 'percentage';
}

interface PayrollRecord {
  id: string;
  employeeId: string;
  month: string;
  year: number;
  basicSalary: number;
  totalAllowances: number;
  totalDeductions: number;
  netSalary: number;
  paymentDate?: string;
  status: 'pending' | 'paid';
  notes?: string;
}

interface Position {
  id: string;
  name: string;
  nameEn: string;
  icon: string;
  active: boolean;
}

// Sample data
const samplePositions: Position[] = [
  { id: '1', name: 'راعي', nameEn: 'Shepherd', icon: '🐑', active: true },
  { id: '2', name: 'بيطري', nameEn: 'Veterinarian', icon: '🩺', active: true },
  { id: '3', name: 'عامل تنظيف', nameEn: 'Cleaner', icon: '🧹', active: true },
  { id: '4', name: 'مشرف', nameEn: 'Supervisor', icon: '👨‍💼', active: true },
  { id: '5', name: 'سائق', nameEn: 'Driver', icon: '🚚', active: true },
  { id: '6', name: 'حارس', nameEn: 'Guard', icon: '🛡️', active: true },
];

const sampleEmployees: Employee[] = [
  {
    id: '1',
    nameAr: 'أحمد محمد علي',
    nameEn: 'Ahmed Mohammed Ali',
    idNumber: '1234567890',
    phone: '0501234567',
    address: 'الرياض، المملكة العربية السعودية',
    hireDate: '2023-01-15',
    position: 'راعي',
    status: 'active',
    salary: {
      basicSalary: 3000,
      salaryType: 'monthly',
      startDate: '2023-01-15',
      allowances: [
        { id: '1', name: 'بدل سكن', amount: 500, type: 'fixed' },
        { id: '2', name: 'بدل مواصلات', amount: 300, type: 'fixed' },
      ],
      deductions: [
        { id: '1', name: 'تأمينات اجتماعية', amount: 10, type: 'percentage' },
      ],
    },
    notes: 'موظف ممتاز ومتفاني في العمل',
  },
  {
    id: '2',
    nameAr: 'فاطمة عبدالله',
    nameEn: 'Fatima Abdullah',
    idNumber: '0987654321',
    phone: '0507654321',
    address: 'جدة، المملكة العربية السعودية',
    hireDate: '2023-03-01',
    position: 'بيطري',
    status: 'active',
    salary: {
      basicSalary: 5000,
      salaryType: 'monthly',
      startDate: '2023-03-01',
      allowances: [
        { id: '1', name: 'بدل خبرة', amount: 1000, type: 'fixed' },
        { id: '2', name: 'بدل مواصلات', amount: 400, type: 'fixed' },
      ],
      deductions: [
        { id: '1', name: 'تأمينات اجتماعية', amount: 10, type: 'percentage' },
      ],
    },
    notes: 'طبيبة بيطرية متخصصة',
  },
];

const Employees: React.FC = () => {
  const { isDarkMode } = useThemeStore();

  // States
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>([]);
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPosition, setFilterPosition] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [openPayrollDialog, setOpenPayrollDialog] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null);
  const [viewingEmployee, setViewingEmployee] = useState<Employee | null>(null);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  // States for allowances and deductions management
  const [allowanceTypes, setAllowanceTypes] = useState([
    {
      id: '1',
      name: 'بدل سكن',
      nameEn: 'Housing Allowance',
      icon: '🏠',
      active: true,
      type: 'fixed',
      defaultAmount: 500,
      order: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'بدل مواصلات',
      nameEn: 'Transportation Allowance',
      icon: '🚗',
      active: true,
      type: 'fixed',
      defaultAmount: 300,
      order: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ]);

  const [deductionTypes, setDeductionTypes] = useState([
    {
      id: '1',
      name: 'تأمينات اجتماعية',
      nameEn: 'Social Insurance',
      icon: '🛡️',
      active: true,
      type: 'percentage',
      defaultAmount: 10,
      order: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'ضريبة دخل',
      nameEn: 'Income Tax',
      icon: '💸',
      active: true,
      type: 'percentage',
      defaultAmount: 5,
      order: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ]);

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const savedEmployees = localStorage.getItem('employees');
    const savedPositions = localStorage.getItem('employeePositions'); // تغيير المفتاح ليتطابق مع الإعدادات
    const savedPayroll = localStorage.getItem('payrollRecords');

    if (savedEmployees) {
      setEmployees(JSON.parse(savedEmployees));
    } else {
      setEmployees(sampleEmployees);
      localStorage.setItem('employees', JSON.stringify(sampleEmployees));
    }

    if (savedPositions) {
      setPositions(JSON.parse(savedPositions));
    } else {
      // إضافة بيانات افتراضية للمناصب الوظيفية
      const defaultPositions = [
        {
          id: '1',
          name: 'مدير المزرعة',
          nameEn: 'Farm Manager',
          icon: '👨‍💼',
          active: true,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'راعي',
          nameEn: 'Shepherd',
          icon: '🐑',
          active: true,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'طبيب بيطري',
          nameEn: 'Veterinarian',
          icon: '⚕️',
          active: true,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'عامل تنظيف',
          nameEn: 'Cleaner',
          icon: '🧹',
          active: true,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '5',
          name: 'حارس أمن',
          nameEn: 'Security Guard',
          icon: '🛡️',
          active: true,
          order: 4,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '6',
          name: 'محاسب',
          nameEn: 'Accountant',
          icon: '💰',
          active: true,
          order: 5,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setPositions(defaultPositions);
      localStorage.setItem(
        'employeePositions',
        JSON.stringify(defaultPositions)
      );
    }

    if (savedPayroll) {
      setPayrollRecords(JSON.parse(savedPayroll));
    }

    // Load allowance types
    const savedAllowanceTypes = localStorage.getItem('employeeAllowances');
    if (savedAllowanceTypes) {
      setAllowanceTypes(JSON.parse(savedAllowanceTypes));
    } else {
      // إضافة بيانات افتراضية لأنواع البدلات
      const defaultAllowanceTypes = [
        {
          id: '1',
          name: 'بدل سكن',
          nameEn: 'Housing Allowance',
          icon: '🏠',
          active: true,
          type: 'fixed',
          defaultAmount: 500,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'بدل مواصلات',
          nameEn: 'Transportation Allowance',
          icon: '🚗',
          active: true,
          type: 'fixed',
          defaultAmount: 300,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'بدل خبرة',
          nameEn: 'Experience Allowance',
          icon: '🎓',
          active: true,
          type: 'fixed',
          defaultAmount: 1000,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'بدل طبيعة عمل',
          nameEn: 'Nature of Work Allowance',
          icon: '⚡',
          active: true,
          type: 'percentage',
          defaultAmount: 15,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '5',
          name: 'بدل إشراف',
          nameEn: 'Supervision Allowance',
          icon: '👨‍💼',
          active: true,
          type: 'fixed',
          defaultAmount: 800,
          order: 4,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '6',
          name: 'بدل هاتف',
          nameEn: 'Phone Allowance',
          icon: '📱',
          active: true,
          type: 'fixed',
          defaultAmount: 200,
          order: 5,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setAllowanceTypes(defaultAllowanceTypes);
      localStorage.setItem(
        'employeeAllowances',
        JSON.stringify(defaultAllowanceTypes)
      );
    }

    // Load deduction types
    const savedDeductionTypes = localStorage.getItem('employeeDeductions');
    if (savedDeductionTypes) {
      setDeductionTypes(JSON.parse(savedDeductionTypes));
    } else {
      // إضافة بيانات افتراضية لأنواع الخصومات
      const defaultDeductionTypes = [
        {
          id: '1',
          name: 'تأمينات اجتماعية',
          nameEn: 'Social Insurance',
          icon: '🛡️',
          active: true,
          type: 'percentage',
          defaultAmount: 10,
          order: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'ضريبة دخل',
          nameEn: 'Income Tax',
          icon: '💸',
          active: true,
          type: 'percentage',
          defaultAmount: 5,
          order: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'قرض شخصي',
          nameEn: 'Personal Loan',
          icon: '💰',
          active: true,
          type: 'fixed',
          defaultAmount: 500,
          order: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'غياب بدون إذن',
          nameEn: 'Unauthorized Absence',
          icon: '❌',
          active: true,
          type: 'fixed',
          defaultAmount: 100,
          order: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '5',
          name: 'تأخير',
          nameEn: 'Late Arrival',
          icon: '⏰',
          active: true,
          type: 'fixed',
          defaultAmount: 50,
          order: 4,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      setDeductionTypes(defaultDeductionTypes);
      localStorage.setItem(
        'employeeDeductions',
        JSON.stringify(defaultDeductionTypes)
      );
    }
  };

  // Helper functions
  const calculateNetSalary = (employee: Employee): number => {
    const { basicSalary, allowances, deductions } = employee.salary;

    const totalAllowances = allowances.reduce((sum, allowance) => {
      return (
        sum +
        (allowance.type === 'fixed'
          ? allowance.amount
          : (basicSalary * allowance.amount) / 100)
      );
    }, 0);

    const totalDeductions = deductions.reduce((sum, deduction) => {
      return (
        sum +
        (deduction.type === 'fixed'
          ? deduction.amount
          : (basicSalary * deduction.amount) / 100)
      );
    }, 0);

    return basicSalary + totalAllowances - totalDeductions;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'resigned':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'معطل';
      case 'resigned':
        return 'مستقيل';
      default:
        return status;
    }
  };

  // Event handlers
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleAddEmployee = () => {
    setEditingEmployee(null);
    setOpenDialog(true);
  };

  const handleEditEmployee = (employee: Employee) => {
    setEditingEmployee(employee);
    setOpenDialog(true);
  };

  const handleViewEmployee = (employee: Employee) => {
    setViewingEmployee(employee);
    setOpenViewDialog(true);
  };

  const handleDeleteEmployee = (id: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
      const updatedEmployees = employees.filter((emp) => emp.id !== id);
      setEmployees(updatedEmployees);
      localStorage.setItem('employees', JSON.stringify(updatedEmployees));

      setSnackbar({
        open: true,
        message: 'تم حذف الموظف بنجاح',
        severity: 'success',
      });
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingEmployee(null);
  };

  const handleCloseViewDialog = () => {
    setOpenViewDialog(false);
    setViewingEmployee(null);
  };

  const handleGeneratePayroll = () => {
    setOpenPayrollDialog(true);
  };

  // Filter employees
  const filteredEmployees = employees.filter((employee) => {
    const matchesSearch =
      employee.nameAr.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.idNumber.includes(searchTerm) ||
      employee.phone.includes(searchTerm);
    const matchesPosition =
      !filterPosition || employee.position === filterPosition;
    const matchesStatus = !filterStatus || employee.status === filterStatus;

    return matchesSearch && matchesPosition && matchesStatus;
  });

  // Calculate summary statistics
  const totalEmployees = employees.length;
  const activeEmployees = employees.filter(
    (emp) => emp.status === 'active'
  ).length;
  const totalMonthlySalaries = employees
    .filter((emp) => emp.status === 'active')
    .reduce((sum, emp) => sum + calculateNetSalary(emp), 0);

  // Tab panel component
  const TabPanel = ({ children, value, index }: any) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Page Header */}
      <Box
        sx={{
          mb: 4,
          p: 3,
          background: 'linear-gradient(135deg, #4a5568 0%, #2d3748 100%)',
          borderRadius: 3,
          color: 'white',
          boxShadow: '0 8px 32px rgba(74, 85, 104, 0.3)',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Box>
            <Typography
              variant="h4"
              component="h1"
              fontWeight="bold"
              sx={{ mb: 1 }}
            >
              👥 إدارة الموظفين
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
              إدارة شاملة للموظفين والرواتب ونظام كشف الرواتب
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddEmployee}
            sx={{
              background: 'rgba(255, 255, 255, 0.2)',
              backdropFilter: 'blur(10px)',
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.3)',
              },
            }}
          >
            إضافة موظف جديد
          </Button>
        </Box>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              p: 2,
              textAlign: 'center',
              background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
              color: 'white',
            }}
          >
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {totalEmployees}
            </Typography>
            <Typography variant="body1">إجمالي الموظفين</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              p: 2,
              textAlign: 'center',
              background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
              color: 'white',
            }}
          >
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {activeEmployees}
            </Typography>
            <Typography variant="body1">الموظفين النشطين</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              p: 2,
              textAlign: 'center',
              background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
              color: 'white',
            }}
          >
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {totalMonthlySalaries.toLocaleString()}
            </Typography>
            <Typography variant="body1">إجمالي الرواتب الشهرية</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              p: 2,
              textAlign: 'center',
              background: 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
              color: 'white',
            }}
          >
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {positions.filter((p) => p.active).length}
            </Typography>
            <Typography variant="body1">المناصب المتاحة</Typography>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Paper
        sx={{
          width: '100%',
          borderRadius: 3,
          overflow: 'hidden',
          boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
          border: '1px solid rgba(0,0,0,0.05)',
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="employees tabs"
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontWeight: 500,
              fontSize: '1rem',
              textTransform: 'none',
              minHeight: 64,
              '&.Mui-selected': {
                fontWeight: 600,
              },
            },
          }}
        >
          <Tab label="👥 قائمة الموظفين" />
          <Tab label="💰 كشف الرواتب" />
          <Tab label="📊 التقارير" />
          <Tab label="⚙️ إعدادات المناصب" />
        </Tabs>

        {/* Employees List Tab */}
        <TabPanel value={tabValue} index={0}>
          {/* Filters */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              🔍 البحث والفلترة
            </Typography>
            <Grid container spacing={2} alignItems="end">
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
                  البحث العام
                </Typography>
                <TextField
                  fullWidth
                  placeholder="البحث بالاسم أو رقم الهوية أو الهاتف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
                  فلترة حسب المنصب
                </Typography>
                <FormControl fullWidth>
                  <InputLabel>المنصب</InputLabel>
                  <Select
                    value={filterPosition}
                    onChange={(e) => setFilterPosition(e.target.value)}
                    label="المنصب"
                    sx={{ borderRadius: 2 }}
                  >
                    <MenuItem value="">جميع المناصب</MenuItem>
                    {positions
                      .filter((p) => p.active)
                      .map((position) => (
                        <MenuItem key={position.id} value={position.name}>
                          {position.icon} {position.name}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
                  فلترة حسب الحالة
                </Typography>
                <FormControl fullWidth>
                  <InputLabel>الحالة</InputLabel>
                  <Select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    label="الحالة"
                    sx={{ borderRadius: 2 }}
                  >
                    <MenuItem value="">جميع الحالات</MenuItem>
                    <MenuItem value="active">✅ نشط</MenuItem>
                    <MenuItem value="inactive">⏸️ معطل</MenuItem>
                    <MenuItem value="resigned">❌ مستقيل</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={2}>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
                  إجراءات سريعة
                </Typography>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={handleGeneratePayroll}
                  startIcon={<PayrollIcon />}
                  sx={{ height: 56, borderRadius: 2 }}
                >
                  كشف الرواتب
                </Button>
              </Grid>
            </Grid>
          </Box>

          {/* Employees Table */}
          <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
            <Table>
              <TableHead
                sx={{ backgroundColor: isDarkMode ? '#2d3748' : '#4a5568' }}
              >
                <TableRow>
                  <TableCell sx={{ fontWeight: 600, color: 'white' }}>
                    الصورة
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'white' }}>
                    الاسم
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'white' }}>
                    رقم الهوية
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'white' }}>
                    المنصب
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'white' }}>
                    الهاتف
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'white' }}>
                    تاريخ التوظيف
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'white' }}>
                    الراتب الصافي
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'white' }}>
                    الحالة
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      color: 'white',
                      textAlign: 'center',
                    }}
                  >
                    الإجراءات
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredEmployees
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((employee) => (
                    <TableRow key={employee.id} hover>
                      <TableCell>
                        <Avatar
                          src={employee.photo}
                          sx={{ width: 40, height: 40 }}
                        >
                          {employee.nameAr.charAt(0)}
                        </Avatar>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body1" fontWeight="bold">
                            {employee.nameAr}
                          </Typography>
                          {employee.nameEn && (
                            <Typography variant="body2" color="text.secondary">
                              {employee.nameEn}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>{employee.idNumber}</TableCell>
                      <TableCell>
                        <Chip
                          label={employee.position}
                          icon={
                            <span>
                              {
                                positions.find(
                                  (p) => p.name === employee.position
                                )?.icon
                              }
                            </span>
                          }
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>{employee.phone}</TableCell>
                      <TableCell>
                        {new Date(employee.hireDate).toLocaleDateString(
                          'ar-SA'
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography
                          variant="body1"
                          fontWeight="bold"
                          color="primary"
                        >
                          {calculateNetSalary(employee).toLocaleString()} ر.س
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusText(employee.status)}
                          color={getStatusColor(employee.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <IconButton
                            size="small"
                            onClick={() => handleViewEmployee(employee)}
                            sx={{ borderRadius: 2 }}
                          >
                            <ViewIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleEditEmployee(employee)}
                            sx={{ borderRadius: 2 }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteEmployee(employee.id)}
                            sx={{
                              borderRadius: 2,
                              '&:hover': {
                                backgroundColor: isDarkMode
                                  ? 'error.dark'
                                  : 'error.light',
                              },
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component="div"
            count={filteredEmployees.length}
            page={page}
            onPageChange={(event, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(event) => {
              setRowsPerPage(parseInt(event.target.value, 10));
              setPage(0);
            }}
            labelRowsPerPage="عدد الصفوف في الصفحة:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`
            }
          />
        </TabPanel>

        {/* Payroll Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <PayrollIcon
              sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }}
            />
            <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
              نظام كشف الرواتب
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
              سيتم تطوير هذا القسم لإدارة كشوف الرواتب الشهرية والتقارير المالية
            </Typography>
            <Button
              variant="contained"
              startIcon={<PayrollIcon />}
              onClick={handleGeneratePayroll}
              sx={{
                background: 'linear-gradient(45deg, #4a5568 30%, #718096 90%)',
                '&:hover': {
                  background:
                    'linear-gradient(45deg, #2d3748 30%, #4a5568 90%)',
                },
              }}
            >
              إنشاء كشف رواتب جديد
            </Button>
          </Box>
        </TabPanel>

        {/* Reports Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <HistoryIcon
              sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }}
            />
            <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
              التقارير والإحصائيات
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
              تقارير شاملة عن الموظفين والرواتب والتكاليف التشغيلية
            </Typography>
            <Grid container spacing={2} justifyContent="center">
              <Grid item>
                <Button variant="outlined" startIcon={<PersonIcon />}>
                  تقرير الموظفين
                </Button>
              </Grid>
              <Grid item>
                <Button variant="outlined" startIcon={<SalaryIcon />}>
                  تقرير الرواتب
                </Button>
              </Grid>
              <Grid item>
                <Button variant="outlined" startIcon={<HistoryIcon />}>
                  التقرير السنوي
                </Button>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        {/* Positions Settings Tab */}
        <TabPanel value={tabValue} index={3}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              ⚙️ إدارة المناصب والوظائف
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                const newPosition: Position = {
                  id: Date.now().toString(),
                  name: 'منصب جديد',
                  nameEn: 'New Position',
                  icon: '👤',
                  active: true,
                };
                const updatedPositions = [...positions, newPosition];
                setPositions(updatedPositions);
                localStorage.setItem(
                  'employeePositions',
                  JSON.stringify(updatedPositions)
                );
                setSnackbar({
                  open: true,
                  message: 'تم إضافة منصب جديد بنجاح',
                  severity: 'success',
                });
              }}
              sx={{
                background: 'linear-gradient(45deg, #4a5568 30%, #718096 90%)',
                '&:hover': {
                  background:
                    'linear-gradient(45deg, #2d3748 30%, #4a5568 90%)',
                },
                mb: 3,
              }}
            >
              إضافة منصب جديد
            </Button>
          </Box>

          {/* Positions List */}
          <Grid container spacing={2}>
            {positions.map((position) => (
              <Grid item xs={12} sm={6} md={4} key={position.id}>
                <Card sx={{ p: 2, borderRadius: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      mb: 2,
                    }}
                  >
                    <Typography sx={{ fontSize: '2rem' }}>
                      {position.icon}
                    </Typography>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {position.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {position.nameEn}
                      </Typography>
                    </Box>
                    <Chip
                      label={position.active ? 'نشط' : 'معطل'}
                      color={position.active ? 'success' : 'default'}
                      size="small"
                    />
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<EditIcon />}
                      onClick={() => {
                        const newName = prompt(
                          'أدخل الاسم الجديد بالعربية:',
                          position.name
                        );
                        if (newName && newName.trim()) {
                          const newNameEn = prompt(
                            'أدخل الاسم الجديد بالإنجليزية:',
                            position.nameEn
                          );
                          const newIcon = prompt(
                            'أدخل الأيقونة الجديدة:',
                            position.icon
                          );

                          const updatedPositions = positions.map((p) =>
                            p.id === position.id
                              ? {
                                  ...p,
                                  name: newName.trim(),
                                  nameEn: newNameEn?.trim() || position.nameEn,
                                  icon: newIcon?.trim() || position.icon,
                                }
                              : p
                          );
                          setPositions(updatedPositions);
                          localStorage.setItem(
                            'employeePositions',
                            JSON.stringify(updatedPositions)
                          );
                          setSnackbar({
                            open: true,
                            message: 'تم تحديث المنصب بنجاح',
                            severity: 'success',
                          });
                        }
                      }}
                    >
                      تعديل
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      color="error"
                      startIcon={<DeleteIcon />}
                      onClick={() => {
                        if (window.confirm('هل أنت متأكد من حذف هذا المنصب؟')) {
                          const updatedPositions = positions.filter(
                            (p) => p.id !== position.id
                          );
                          setPositions(updatedPositions);
                          localStorage.setItem(
                            'employeePositions',
                            JSON.stringify(updatedPositions)
                          );
                          setSnackbar({
                            open: true,
                            message: 'تم حذف المنصب بنجاح',
                            severity: 'success',
                          });
                        }
                      }}
                    >
                      حذف
                    </Button>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>
      </Paper>

      {/* Add/Edit Employee Dialog */}
      <EmployeeDialog
        open={openDialog}
        onClose={handleCloseDialog}
        employee={editingEmployee}
        positions={positions}
        onSave={(employee) => {
          if (editingEmployee) {
            // Update existing employee
            const updatedEmployees = employees.map((emp) =>
              emp.id === employee.id ? employee : emp
            );
            setEmployees(updatedEmployees);
            localStorage.setItem('employees', JSON.stringify(updatedEmployees));
            setSnackbar({
              open: true,
              message: 'تم تحديث بيانات الموظف بنجاح',
              severity: 'success',
            });
          } else {
            // Add new employee
            const newEmployee = {
              ...employee,
              id: Date.now().toString(),
            };
            const updatedEmployees = [...employees, newEmployee];
            setEmployees(updatedEmployees);
            localStorage.setItem('employees', JSON.stringify(updatedEmployees));
            setSnackbar({
              open: true,
              message: 'تم إضافة الموظف بنجاح',
              severity: 'success',
            });
          }
          handleCloseDialog();
        }}
        isDarkMode={isDarkMode}
      />

      {/* View Employee Dialog */}
      <ViewEmployeeDialog
        open={openViewDialog}
        onClose={handleCloseViewDialog}
        employee={viewingEmployee}
        calculateNetSalary={calculateNetSalary}
        isDarkMode={isDarkMode}
      />

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

// Employee Dialog Component
interface EmployeeDialogProps {
  open: boolean;
  onClose: () => void;
  employee: Employee | null;
  positions: Position[];
  onSave: (employee: Employee) => void;
  isDarkMode: boolean;
}

const EmployeeDialog: React.FC<EmployeeDialogProps> = ({
  open,
  onClose,
  employee,
  positions,
  onSave,
  isDarkMode,
}) => {
  const [formData, setFormData] = useState<Partial<Employee>>({
    nameAr: '',
    nameEn: '',
    idNumber: '',
    phone: '',
    address: '',
    hireDate: new Date().toISOString().split('T')[0],
    position: '',
    status: 'active',
    salary: {
      basicSalary: 0,
      salaryType: 'monthly',
      startDate: new Date().toISOString().split('T')[0],
      allowances: [],
      deductions: [],
    },
    notes: '',
  });

  useEffect(() => {
    if (employee) {
      setFormData(employee);
    } else {
      setFormData({
        nameAr: '',
        nameEn: '',
        idNumber: '',
        phone: '',
        address: '',
        hireDate: new Date().toISOString().split('T')[0],
        position: '',
        status: 'active',
        salary: {
          basicSalary: 0,
          salaryType: 'monthly',
          startDate: new Date().toISOString().split('T')[0],
          allowances: [],
          deductions: [],
        },
        notes: '',
      });
    }
  }, [employee, open]);

  const handleSubmit = () => {
    if (
      !formData.nameAr ||
      !formData.idNumber ||
      !formData.phone ||
      !formData.position
    ) {
      alert(
        'يرجى ملء جميع الحقول المطلوبة (الاسم بالعربية، رقم الهوية، رقم الهاتف، المنصب)'
      );
      return;
    }

    onSave(formData as Employee);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          minHeight: '700px',
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle
        sx={{
          fontWeight: 'bold',
          fontSize: '1.5rem',
          textAlign: 'center',
          background: 'linear-gradient(135deg, #4a5568 0%, #2d3748 100%)',
          color: 'white',
          mb: 0,
        }}
      >
        {employee ? 'تعديل بيانات الموظف' : 'إضافة موظف جديد'}
      </DialogTitle>
      <DialogContent sx={{ p: 4, overflow: 'auto' }}>
        <Grid container spacing={4}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography
              variant="h6"
              fontWeight="bold"
              sx={{ mb: 2, color: 'primary.main' }}
            >
              📋 البيانات الأساسية
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary.main"
              >
                الاسم بالعربية *
              </Typography>
            </Box>
            <TextField
              fullWidth
              placeholder="أدخل الاسم بالعربية"
              value={formData.nameAr}
              onChange={(e) =>
                setFormData({ ...formData, nameAr: e.target.value })
              }
              variant="outlined"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="text.secondary"
              >
                الاسم بالإنجليزية (اختياري)
              </Typography>
            </Box>
            <TextField
              fullWidth
              placeholder="Enter name in English"
              value={formData.nameEn}
              onChange={(e) =>
                setFormData({ ...formData, nameEn: e.target.value })
              }
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary.main"
              >
                رقم الهوية/الإقامة *
              </Typography>
            </Box>
            <TextField
              fullWidth
              placeholder="1234567890"
              value={formData.idNumber}
              onChange={(e) =>
                setFormData({ ...formData, idNumber: e.target.value })
              }
              variant="outlined"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary.main"
              >
                رقم الهاتف *
              </Typography>
            </Box>
            <TextField
              fullWidth
              placeholder="0501234567"
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
              variant="outlined"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                },
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="text.secondary"
              >
                العنوان (اختياري)
              </Typography>
            </Box>
            <TextField
              fullWidth
              placeholder="الرياض، المملكة العربية السعودية"
              value={formData.address}
              onChange={(e) =>
                setFormData({ ...formData, address: e.target.value })
              }
              variant="outlined"
              multiline
              rows={2}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                },
              }}
            />
          </Grid>

          {/* Employment Information */}
          <Grid item xs={12}>
            <Typography
              variant="h6"
              fontWeight="bold"
              sx={{ mb: 2, mt: 3, color: 'primary.main' }}
            >
              💼 بيانات التوظيف
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary.main"
              >
                تاريخ التوظيف *
              </Typography>
            </Box>
            <TextField
              fullWidth
              type="date"
              value={formData.hireDate}
              onChange={(e) =>
                setFormData({ ...formData, hireDate: e.target.value })
              }
              variant="outlined"
              required
              InputLabelProps={{ shrink: true }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                },
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary.main"
              >
                حالة الموظف *
              </Typography>
            </Box>
            <FormControl fullWidth required>
              <Select
                value={formData.status}
                onChange={(e) =>
                  setFormData({ ...formData, status: e.target.value as any })
                }
                sx={{
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                }}
              >
                <MenuItem value="active">✅ نشط</MenuItem>
                <MenuItem value="inactive">⏸️ معطل</MenuItem>
                <MenuItem value="resigned">❌ مستقيل</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary.main"
              >
                المنصب/الوظيفة *
              </Typography>
            </Box>
            <FormControl fullWidth required>
              <Select
                value={formData.position}
                onChange={(e) =>
                  setFormData({ ...formData, position: e.target.value })
                }
                sx={{
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                }}
              >
                {positions
                  .filter((p) => p.active)
                  .map((position) => (
                    <MenuItem key={position.id} value={position.name}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          width: '100%',
                        }}
                      >
                        <Typography sx={{ fontSize: '1.3rem' }}>
                          {position.icon}
                        </Typography>
                        <Typography fontWeight="bold">
                          {position.name}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ ml: 'auto' }}
                        >
                          {position.nameEn}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Salary Information */}
          <Grid item xs={12}>
            <Typography
              variant="h6"
              fontWeight="bold"
              sx={{ mb: 2, mt: 3, color: 'primary.main' }}
            >
              💰 بيانات الراتب
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary.main"
              >
                الراتب الأساسي *
              </Typography>
            </Box>
            <TextField
              fullWidth
              type="number"
              placeholder="1800"
              value={formData.salary?.basicSalary || 0}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  salary: {
                    ...formData.salary!,
                    basicSalary: parseFloat(e.target.value) || 0,
                  },
                })
              }
              variant="outlined"
              required
              InputProps={{
                endAdornment: (
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 'bold', color: 'primary.main' }}
                  >
                    ر.س
                  </Typography>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                },
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary.main"
              >
                نوع الراتب
              </Typography>
            </Box>
            <FormControl fullWidth>
              <Select
                value={formData.salary?.salaryType || 'monthly'}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    salary: {
                      ...formData.salary!,
                      salaryType: e.target.value as any,
                    },
                  })
                }
                sx={{
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                }}
              >
                <MenuItem value="monthly">📅 شهري</MenuItem>
                <MenuItem value="weekly">📆 أسبوعي</MenuItem>
                <MenuItem value="daily">🗓️ يومي</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary.main"
              >
                تاريخ بداية احتساب الراتب
              </Typography>
            </Box>
            <TextField
              fullWidth
              type="date"
              value={
                formData.salary?.startDate ||
                new Date().toISOString().split('T')[0]
              }
              onChange={(e) =>
                setFormData({
                  ...formData,
                  salary: {
                    ...formData.salary!,
                    startDate: e.target.value,
                  },
                })
              }
              variant="outlined"
              InputLabelProps={{ shrink: true }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                },
              }}
            />
          </Grid>

          {/* Allowances Section */}
          <Grid item xs={12}>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, mt: 2 }}>
              💵 البدلات الإضافية (اختيارية)
            </Typography>
            <Box
              sx={{
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
                p: 2,
              }}
            >
              {formData.salary?.allowances &&
              formData.salary.allowances.length > 0 ? (
                formData.salary.allowances.map((allowance, index) => (
                  <Box
                    key={allowance.id}
                    sx={{
                      display: 'flex',
                      gap: 1,
                      mb: 1,
                      alignItems: 'center',
                    }}
                  >
                    <TextField
                      size="small"
                      label="اسم البدل"
                      value={allowance.name}
                      onChange={(e) => {
                        const updatedAllowances = [
                          ...formData.salary!.allowances,
                        ];
                        updatedAllowances[index] = {
                          ...allowance,
                          name: e.target.value,
                        };
                        setFormData({
                          ...formData,
                          salary: {
                            ...formData.salary!,
                            allowances: updatedAllowances,
                          },
                        });
                      }}
                      sx={{ flex: 2 }}
                    />
                    <TextField
                      size="small"
                      label="المبلغ"
                      type="number"
                      value={allowance.amount}
                      onChange={(e) => {
                        const updatedAllowances = [
                          ...formData.salary!.allowances,
                        ];
                        updatedAllowances[index] = {
                          ...allowance,
                          amount: parseFloat(e.target.value) || 0,
                        };
                        setFormData({
                          ...formData,
                          salary: {
                            ...formData.salary!,
                            allowances: updatedAllowances,
                          },
                        });
                      }}
                      sx={{ flex: 1 }}
                    />
                    <FormControl size="small" sx={{ flex: 1 }}>
                      <InputLabel>النوع</InputLabel>
                      <Select
                        value={allowance.type}
                        onChange={(e) => {
                          const updatedAllowances = [
                            ...formData.salary!.allowances,
                          ];
                          updatedAllowances[index] = {
                            ...allowance,
                            type: e.target.value as any,
                          };
                          setFormData({
                            ...formData,
                            salary: {
                              ...formData.salary!,
                              allowances: updatedAllowances,
                            },
                          });
                        }}
                        label="النوع"
                      >
                        <MenuItem value="fixed">مبلغ ثابت</MenuItem>
                        <MenuItem value="percentage">نسبة مئوية</MenuItem>
                      </Select>
                    </FormControl>
                    <IconButton
                      size="small"
                      onClick={() => {
                        const updatedAllowances =
                          formData.salary!.allowances.filter(
                            (_, i) => i !== index
                          );
                        setFormData({
                          ...formData,
                          salary: {
                            ...formData.salary!,
                            allowances: updatedAllowances,
                          },
                        });
                      }}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                ))
              ) : (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ textAlign: 'center', py: 2 }}
                >
                  لا توجد بدلات مضافة
                </Typography>
              )}
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() => {
                  const newAllowance: Allowance = {
                    id: Date.now().toString(),
                    name: '',
                    amount: 0,
                    type: 'fixed',
                  };
                  setFormData({
                    ...formData,
                    salary: {
                      ...formData.salary!,
                      allowances: [
                        ...(formData.salary?.allowances || []),
                        newAllowance,
                      ],
                    },
                  });
                }}
                size="small"
                sx={{ mt: 1 }}
              >
                إضافة بدل
              </Button>
            </Box>
          </Grid>

          {/* Deductions Section */}
          <Grid item xs={12}>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, mt: 2 }}>
              💸 الخصومات (اختيارية)
            </Typography>
            <Box
              sx={{
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
                p: 2,
              }}
            >
              {formData.salary?.deductions &&
              formData.salary.deductions.length > 0 ? (
                formData.salary.deductions.map((deduction, index) => (
                  <Box
                    key={deduction.id}
                    sx={{
                      display: 'flex',
                      gap: 1,
                      mb: 1,
                      alignItems: 'center',
                    }}
                  >
                    <TextField
                      size="small"
                      label="اسم الخصم"
                      value={deduction.name}
                      onChange={(e) => {
                        const updatedDeductions = [
                          ...formData.salary!.deductions,
                        ];
                        updatedDeductions[index] = {
                          ...deduction,
                          name: e.target.value,
                        };
                        setFormData({
                          ...formData,
                          salary: {
                            ...formData.salary!,
                            deductions: updatedDeductions,
                          },
                        });
                      }}
                      sx={{ flex: 2 }}
                    />
                    <TextField
                      size="small"
                      label="المبلغ"
                      type="number"
                      value={deduction.amount}
                      onChange={(e) => {
                        const updatedDeductions = [
                          ...formData.salary!.deductions,
                        ];
                        updatedDeductions[index] = {
                          ...deduction,
                          amount: parseFloat(e.target.value) || 0,
                        };
                        setFormData({
                          ...formData,
                          salary: {
                            ...formData.salary!,
                            deductions: updatedDeductions,
                          },
                        });
                      }}
                      sx={{ flex: 1 }}
                    />
                    <FormControl size="small" sx={{ flex: 1 }}>
                      <InputLabel>النوع</InputLabel>
                      <Select
                        value={deduction.type}
                        onChange={(e) => {
                          const updatedDeductions = [
                            ...formData.salary!.deductions,
                          ];
                          updatedDeductions[index] = {
                            ...deduction,
                            type: e.target.value as any,
                          };
                          setFormData({
                            ...formData,
                            salary: {
                              ...formData.salary!,
                              deductions: updatedDeductions,
                            },
                          });
                        }}
                        label="النوع"
                      >
                        <MenuItem value="fixed">مبلغ ثابت</MenuItem>
                        <MenuItem value="percentage">نسبة مئوية</MenuItem>
                      </Select>
                    </FormControl>
                    <IconButton
                      size="small"
                      onClick={() => {
                        const updatedDeductions =
                          formData.salary!.deductions.filter(
                            (_, i) => i !== index
                          );
                        setFormData({
                          ...formData,
                          salary: {
                            ...formData.salary!,
                            deductions: updatedDeductions,
                          },
                        });
                      }}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                ))
              ) : (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ textAlign: 'center', py: 2 }}
                >
                  لا توجد خصومات مضافة
                </Typography>
              )}
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() => {
                  const newDeduction: Deduction = {
                    id: Date.now().toString(),
                    name: '',
                    amount: 0,
                    type: 'fixed',
                  };
                  setFormData({
                    ...formData,
                    salary: {
                      ...formData.salary!,
                      deductions: [
                        ...(formData.salary?.deductions || []),
                        newDeduction,
                      ],
                    },
                  });
                }}
                size="small"
                sx={{ mt: 1 }}
              >
                إضافة خصم
              </Button>
            </Box>
          </Grid>

          {/* Notes Section */}
          <Grid item xs={12}>
            <Typography
              variant="h6"
              fontWeight="bold"
              sx={{ mb: 2, mt: 3, color: 'primary.main' }}
            >
              📝 ملاحظات إضافية
            </Typography>
            <Divider sx={{ mb: 2 }} />
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="text.secondary"
              >
                ملاحظات (اختياري)
              </Typography>
            </Box>
            <TextField
              fullWidth
              placeholder="أضف أي ملاحظات إضافية حول الموظف..."
              value={formData.notes}
              onChange={(e) =>
                setFormData({ ...formData, notes: e.target.value })
              }
              variant="outlined"
              multiline
              rows={3}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(0,0,0,0.02)',
                  '&:hover': {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                  },
                },
              }}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions sx={{ p: 3, gap: 2, justifyContent: 'center' }}>
        <Button
          onClick={onClose}
          variant="outlined"
          size="large"
          sx={{
            borderRadius: 2,
            minWidth: 120,
            fontWeight: 'bold',
          }}
        >
          ❌ إلغاء
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          size="large"
          sx={{
            borderRadius: 2,
            minWidth: 120,
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #4a5568 30%, #718096 90%)',
            '&:hover': {
              background: 'linear-gradient(45deg, #2d3748 30%, #4a5568 90%)',
            },
          }}
        >
          {employee ? '💾 تحديث البيانات' : '➕ إضافة الموظف'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// View Employee Dialog Component
interface ViewEmployeeDialogProps {
  open: boolean;
  onClose: () => void;
  employee: Employee | null;
  calculateNetSalary: (employee: Employee) => number;
  isDarkMode: boolean;
}

const ViewEmployeeDialog: React.FC<ViewEmployeeDialogProps> = ({
  open,
  onClose,
  employee,
  calculateNetSalary,
  isDarkMode,
}) => {
  if (!employee) return null;

  const netSalary = calculateNetSalary(employee);
  const totalAllowances = employee.salary.allowances.reduce(
    (sum, allowance) => {
      return (
        sum +
        (allowance.type === 'fixed'
          ? allowance.amount
          : (employee.salary.basicSalary * allowance.amount) / 100)
      );
    },
    0
  );
  const totalDeductions = employee.salary.deductions.reduce(
    (sum, deduction) => {
      return (
        sum +
        (deduction.type === 'fixed'
          ? deduction.amount
          : (employee.salary.basicSalary * deduction.amount) / 100)
      );
    },
    0
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          minHeight: '500px',
        },
      }}
    >
      <DialogTitle sx={{ fontWeight: 'bold', fontSize: '1.5rem', pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar src={employee.photo} sx={{ width: 60, height: 60 }}>
            {employee.nameAr.charAt(0)}
          </Avatar>
          <Box>
            <Typography variant="h5" fontWeight="bold">
              {employee.nameAr}
            </Typography>
            {employee.nameEn && (
              <Typography variant="subtitle1" color="text.secondary">
                {employee.nameEn}
              </Typography>
            )}
          </Box>
        </Box>
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3}>
          {/* Personal Information */}
          <Grid item xs={12}>
            <Card sx={{ p: 2, mb: 2 }}>
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                📋 البيانات الشخصية
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    رقم الهوية/الإقامة
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {employee.idNumber}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    رقم الهاتف
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {employee.phone}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">
                    العنوان
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {employee.address}
                  </Typography>
                </Grid>
              </Grid>
            </Card>
          </Grid>

          {/* Employment Information */}
          <Grid item xs={12}>
            <Card sx={{ p: 2, mb: 2 }}>
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                💼 بيانات التوظيف
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    تاريخ التوظيف
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {new Date(employee.hireDate).toLocaleDateString('ar-SA')}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    المنصب/الوظيفة
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {employee.position}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    حالة الموظف
                  </Typography>
                  <Chip
                    label={
                      employee.status === 'active'
                        ? 'نشط'
                        : employee.status === 'inactive'
                        ? 'معطل'
                        : 'مستقيل'
                    }
                    color={
                      employee.status === 'active'
                        ? 'success'
                        : employee.status === 'inactive'
                        ? 'warning'
                        : 'error'
                    }
                    size="small"
                  />
                </Grid>
              </Grid>
            </Card>
          </Grid>

          {/* Salary Information */}
          <Grid item xs={12}>
            <Card sx={{ p: 2 }}>
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                💰 بيانات الراتب
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    الراتب الأساسي
                  </Typography>
                  <Typography variant="h6" fontWeight="bold" color="primary">
                    {employee.salary.basicSalary.toLocaleString()} ر.س
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    نوع الراتب
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {employee.salary.salaryType === 'monthly'
                      ? 'شهري'
                      : employee.salary.salaryType === 'weekly'
                      ? 'أسبوعي'
                      : 'يومي'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي البدلات
                  </Typography>
                  <Typography
                    variant="body1"
                    fontWeight="bold"
                    color="success.main"
                  >
                    +{totalAllowances.toLocaleString()} ر.س
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body2" color="text.secondary">
                    إجمالي الخصومات
                  </Typography>
                  <Typography
                    variant="body1"
                    fontWeight="bold"
                    color="error.main"
                  >
                    -{totalDeductions.toLocaleString()} ر.س
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body2" color="text.secondary">
                    الراتب الصافي
                  </Typography>
                  <Typography variant="h6" fontWeight="bold" color="primary">
                    {netSalary.toLocaleString()} ر.س
                  </Typography>
                </Grid>
              </Grid>
            </Card>
          </Grid>

          {/* Notes */}
          {employee.notes && (
            <Grid item xs={12}>
              <Card sx={{ p: 2 }}>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  📝 ملاحظات
                </Typography>
                <Typography variant="body1">{employee.notes}</Typography>
              </Card>
            </Grid>
          )}
        </Grid>
      </DialogContent>
      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={onClose}
          variant="contained"
          sx={{
            borderRadius: 2,
            background: 'linear-gradient(45deg, #4a5568 30%, #718096 90%)',
            '&:hover': {
              background: 'linear-gradient(45deg, #2d3748 30%, #4a5568 90%)',
            },
          }}
        >
          إغلاق
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default Employees;

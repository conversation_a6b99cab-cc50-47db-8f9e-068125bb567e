import {
  Backup as BackupIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import {
  Alert,
  Box,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  Snackbar,
  Tab,
  Tabs,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import DropdownBackupManager from '../../components/dropdown-management/DropdownBackupManager';
import DropdownCategoryManager from '../../components/dropdown-management/DropdownCategoryManager';
import DropdownStatistics from '../../components/dropdown-management/DropdownStatistics';
import DataAnalysisDialog from '../../components/dropdown-management/DataAnalysisDialog';
import { useThemeStore } from '../../store/themeStore';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index} style={{ width: '100%' }}>
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const DropdownManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // States
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [backupDialogOpen, setBackupDialogOpen] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'warning' | 'info',
  });

  // Dropdown categories configuration
  const dropdownCategories = {
    animals: {
      id: 'animals',
      name: 'الحيوانات',
      nameEn: 'Animals',
      icon: '🐑',
      categories: [
        {
          id: 'animal_types',
          name: 'أنواع الحيوانات',
          nameEn: 'Animal Types',
          storageKey: 'animalTypes',
          icon: '🐑',
          hasRelations: true,
          relatedTo: ['animal_breeds'],
        },
        {
          id: 'animal_breeds',
          name: 'السلالات',
          nameEn: 'Breeds',
          storageKey: 'breeds',
          icon: '🧬',
          parentCategory: 'animal_types',
          relationField: 'animalTypeId',
        },
        {
          id: 'animal_categories',
          name: 'فئات الحيوانات',
          nameEn: 'Animal Categories',
          storageKey: 'animalCategories',
          icon: '📋',
        },
        {
          id: 'animal_locations',
          name: 'المواقع والحظائر',
          nameEn: 'Locations & Barns',
          storageKey: 'animalLocations',
          icon: '🏠',
        },
      ],
    },
    employees: {
      id: 'employees',
      name: 'الموظفين',
      nameEn: 'Employees',
      icon: '👥',
      categories: [
        {
          id: 'employee_positions',
          name: 'المناصب الوظيفية',
          nameEn: 'Job Positions',
          storageKey: 'employeePositions',
          icon: '💼',
        },
        {
          id: 'employee_allowances',
          name: 'أنواع البدلات',
          nameEn: 'Allowance Types',
          storageKey: 'employeeAllowances',
          icon: '💰',
          hasMetadata: true,
          metadataFields: ['type', 'defaultAmount'],
        },
        {
          id: 'employee_deductions',
          name: 'أنواع الخصومات',
          nameEn: 'Deduction Types',
          storageKey: 'employeeDeductions',
          icon: '📉',
          hasMetadata: true,
          metadataFields: ['type', 'defaultAmount'],
        },
      ],
    },
    purchases: {
      id: 'purchases',
      name: 'المشتريات والمصروفات',
      nameEn: 'Purchases & Expenses',
      icon: '🛒',
      categories: [
        {
          id: 'purchase_categories',
          name: 'تصنيفات المشتريات',
          nameEn: 'Purchase Categories',
          storageKey: 'purchaseCategories',
          icon: '📦',
        },
        {
          id: 'expense_categories',
          name: 'تصنيفات المصروفات',
          nameEn: 'Expense Categories',
          storageKey: 'expenseCategories',
          icon: '💸',
        },
        {
          id: 'payment_methods',
          name: 'طرق الدفع',
          nameEn: 'Payment Methods',
          storageKey: 'paymentMethods',
          icon: '💳',
        },
        {
          id: 'suppliers',
          name: 'الموردين',
          nameEn: 'Suppliers',
          storageKey: 'suppliers',
          icon: '🏢',
          hasMetadata: true,
          metadataFields: ['contactInfo', 'address', 'taxNumber'],
        },
      ],
    },
    sales: {
      id: 'sales',
      name: 'المبيعات',
      nameEn: 'Sales',
      icon: '💰',
      categories: [
        {
          id: 'sales_methods',
          name: 'طرق البيع',
          nameEn: 'Sales Methods',
          storageKey: 'salesMethods',
          icon: '🛍️',
        },
        {
          id: 'product_categories',
          name: 'تصنيفات المنتجات',
          nameEn: 'Product Categories',
          storageKey: 'productCategories',
          icon: '📋',
        },
        {
          id: 'measurement_units',
          name: 'وحدات القياس',
          nameEn: 'Measurement Units',
          storageKey: 'measurementUnits',
          icon: '📏',
        },
        {
          id: 'customer_types',
          name: 'أنواع العملاء',
          nameEn: 'Customer Types',
          storageKey: 'customerTypes',
          icon: '👤',
        },
      ],
    },
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setSelectedCategory(null);
  };

  const handleExportData = () => {
    try {
      const allData = {};
      Object.values(dropdownCategories).forEach((module) => {
        module.categories.forEach((category) => {
          const data = localStorage.getItem(category.storageKey);
          if (data) {
            allData[category.storageKey] = JSON.parse(data);
          }
        });
      });

      const dataStr = JSON.stringify(allData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `dropdown-settings-${
        new Date().toISOString().split('T')[0]
      }.json`;
      link.click();
      URL.revokeObjectURL(url);

      setSnackbar({
        open: true,
        message: 'تم تصدير البيانات بنجاح',
        severity: 'success',
      });
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'حدث خطأ أثناء تصدير البيانات',
        severity: 'error',
      });
    }
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target?.result as string);

        Object.entries(importedData).forEach(([key, value]) => {
          localStorage.setItem(key, JSON.stringify(value));
        });

        setSnackbar({
          open: true,
          message: 'تم استيراد البيانات بنجاح',
          severity: 'success',
        });

        // Refresh the page to reflect changes
        window.location.reload();
      } catch (error) {
        setSnackbar({
          open: true,
          message: 'حدث خطأ أثناء استيراد البيانات',
          severity: 'error',
        });
      }
    };
    reader.readAsText(file);
  };

  const getCurrentModuleCategories = () => {
    const moduleKeys = Object.keys(dropdownCategories);
    const currentModule = moduleKeys[tabValue];
    return dropdownCategories[currentModule]?.categories || [];
  };

  const filteredCategories = getCurrentModuleCategories().filter(
    (category) =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.nameEn.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Page Header */}
      <Box
        sx={{
          mb: 4,
          p: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: 3,
          color: 'white',
          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Box>
            <Typography
              variant="h4"
              component="h1"
              fontWeight="bold"
              sx={{ mb: 1 }}
            >
              ⚙️ إدارة القوائم المنسدلة
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
              مركز موحد لإدارة جميع القوائم المنسدلة في النظام
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="تصدير البيانات">
              <IconButton
                onClick={handleExportData}
                sx={{ color: 'white', bgcolor: 'rgba(255,255,255,0.2)' }}
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="استيراد البيانات">
              <IconButton
                component="label"
                sx={{ color: 'white', bgcolor: 'rgba(255,255,255,0.2)' }}
              >
                <UploadIcon />
                <input
                  type="file"
                  accept=".json"
                  hidden
                  onChange={handleImportData}
                />
              </IconButton>
            </Tooltip>
            <Tooltip title="النسخ الاحتياطي">
              <IconButton
                onClick={() => setBackupDialogOpen(true)}
                sx={{ color: 'white', bgcolor: 'rgba(255,255,255,0.2)' }}
              >
                <BackupIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      {/* Statistics Overview */}
      <DropdownStatistics categories={dropdownCategories} />

      {/* Main Content */}
      <Paper
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
        }}
      >
        {/* Module Tabs */}
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontWeight: 500,
              fontSize: '1rem',
              textTransform: 'none',
              minHeight: 64,
            },
          }}
        >
          {Object.values(dropdownCategories).map((module, index) => (
            <Tab
              key={module.id}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <span>{module.icon}</span>
                  <span>{module.name}</span>
                </Box>
              }
            />
          ))}
        </Tabs>

        {/* Search and Filters */}
        <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
          <TextField
            fullWidth
            placeholder="البحث في القوائم..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ maxWidth: 400 }}
          />
        </Box>

        {/* Tab Panels */}
        {Object.values(dropdownCategories).map((module, index) => (
          <TabPanel key={module.id} value={tabValue} index={index}>
            <Grid container spacing={3}>
              {filteredCategories.map((category) => (
                <Grid item xs={12} sm={6} md={4} key={category.id}>
                  <DropdownCategoryManager
                    category={category}
                    onUpdate={() => {
                      setSnackbar({
                        open: true,
                        message: 'تم تحديث القائمة بنجاح',
                        severity: 'success',
                      });
                    }}
                  />
                </Grid>
              ))}
            </Grid>
          </TabPanel>
        ))}
      </Paper>

      {/* Backup Dialog */}
      <DropdownBackupManager
        open={backupDialogOpen}
        onClose={() => setBackupDialogOpen(false)}
        categories={dropdownCategories}
        onSuccess={(message) => {
          setSnackbar({
            open: true,
            message,
            severity: 'success',
          });
        }}
        onError={(message) => {
          setSnackbar({
            open: true,
            message,
            severity: 'error',
          });
        }}
      />

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DropdownManagement;

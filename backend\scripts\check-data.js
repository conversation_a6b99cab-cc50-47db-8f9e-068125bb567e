const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkData() {
  try {
    console.log('🔍 فحص البيانات في قاعدة البيانات...\n');

    // عد السجلات في كل جدول
    const counts = await Promise.all([
      prisma.animal.count(),
      prisma.birth.count(),
      prisma.employee.count(),
      prisma.sale.count(),
      prisma.purchase.count(),
      prisma.expense.count(),
      prisma.treatment.count(),
      prisma.weightRecord.count(),
    ]);

    const tables = [
      { name: 'الحيوانات (animals)', count: counts[0] },
      { name: 'المواليد (births)', count: counts[1] },
      { name: 'الموظفين (employees)', count: counts[2] },
      { name: 'المبيعات (sales)', count: counts[3] },
      { name: 'المشتريات (purchases)', count: counts[4] },
      { name: 'المصروفات (expenses)', count: counts[5] },
      { name: 'العلاجات (treatments)', count: counts[6] },
      { name: 'سجلات الأوزان (weight_records)', count: counts[7] },
    ];

    console.log('📊 عدد السجلات في كل جدول:');
    console.log('================================');
    tables.forEach(table => {
      console.log(`${table.name}: ${table.count} سجل`);
    });

    console.log('\n🔍 عينة من البيانات:');
    console.log('====================');

    // عرض عينة من الموظفين
    const employees = await prisma.employee.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        position: true,
        status: true,
      }
    });
    console.log('\n👥 الموظفين:');
    employees.forEach((emp, index) => {
      console.log(`  ${index + 1}. ${emp.name} - ${emp.position} (${emp.status})`);
    });

    // عرض عينة من المبيعات
    const sales = await prisma.sale.findMany({
      take: 5,
      select: {
        id: true,
        saleDate: true,
        saleType: true,
        totalPrice: true,
        customerName: true,
      }
    });
    console.log('\n💰 المبيعات:');
    sales.forEach((sale, index) => {
      console.log(`  ${index + 1}. ${sale.customerName} - ${sale.totalPrice} ريال (${sale.saleType})`);
    });

    // عرض عينة من المشتريات
    const purchases = await prisma.purchase.findMany({
      take: 5,
      select: {
        id: true,
        description: true,
        type: true,
        totalCost: true,
        supplier: true,
      }
    });
    console.log('\n🛒 المشتريات:');
    purchases.forEach((purchase, index) => {
      console.log(`  ${index + 1}. ${purchase.description} - ${purchase.totalCost} ريال (${purchase.supplier})`);
    });

    // عرض عينة من المصروفات
    const expenses = await prisma.expense.findMany({
      take: 5,
      select: {
        id: true,
        description: true,
        category: true,
        amount: true,
        date: true,
      }
    });
    console.log('\n💸 المصروفات:');
    expenses.forEach((expense, index) => {
      console.log(`  ${index + 1}. ${expense.description} - ${expense.amount} ريال (${expense.category})`);
    });

    // عرض عينة من الحيوانات
    const animals = await prisma.animal.findMany({
      take: 5,
      select: {
        id: true,
        internalId: true,
        tagNumber: true,
        status: true,
      }
    });
    console.log('\n🐑 الحيوانات:');
    animals.forEach((animal, index) => {
      console.log(`  ${index + 1}. ${animal.internalId} - ${animal.tagNumber} (${animal.status})`);
    });

    console.log('\n✅ انتهى فحص البيانات');

  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkData();

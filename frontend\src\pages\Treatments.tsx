import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useTranslation } from 'react-i18next';

const Treatments: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        {t('treatments')}
      </Typography>
      
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          صفحة العلاجات قيد التطوير
        </Typography>
        <Typography variant="body1" sx={{ mt: 2 }}>
          ستتضمن هذه الصفحة إدارة العلاجات والتحصينات والأدوية
        </Typography>
      </Paper>
    </Box>
  );
};

export default Treatments;

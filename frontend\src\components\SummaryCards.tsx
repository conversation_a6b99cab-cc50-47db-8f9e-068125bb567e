import { Box, Card, CardContent, Grid, Typography } from '@mui/material';
import React from 'react';
import { summaryCardColors } from '../utils/pageColors';

interface SummaryCardData {
  title: string;
  value: string | number;
  type: 'total' | 'success' | 'warning' | 'error' | 'info';
  icon?: string;
}

interface SummaryCardsProps {
  cards: SummaryCardData[];
  isDarkMode?: boolean;
}

const SummaryCards: React.FC<SummaryCardsProps> = ({ cards, isDarkMode = false }) => {
  return (
    <Grid container spacing={2} sx={{ mb: 3 }}>
      {cards.map((card, index) => {
        const cardColors = summaryCardColors[card.type];
        
        return (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                borderRadius: 3,
                background: cardColors.background,
                color: cardColors.color,
                boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
                border: 'none',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 40px rgba(0,0,0,0.2)',
                }
              }}
            >
              <CardContent sx={{ p: 2.5, textAlign: 'center' }}>
                {card.icon && (
                  <Typography variant="h4" sx={{ mb: 1 }}>
                    {card.icon}
                  </Typography>
                )}
                <Typography
                  variant="h4"
                  component="div"
                  fontWeight="bold"
                  sx={{ mb: 1 }}
                >
                  {card.value}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ 
                    opacity: 0.9,
                    fontWeight: 500
                  }}
                >
                  {card.title}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        );
      })}
    </Grid>
  );
};

export default SummaryCards;

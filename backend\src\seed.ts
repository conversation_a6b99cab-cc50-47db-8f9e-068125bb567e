import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create Animal Types
  const sheepType = await prisma.animalType.upsert({
    where: { name: 'sheep' },
    update: {},
    create: {
      name: 'sheep',
      nameAr: 'أغنام',
      nameEn: 'Sheep'
    }
  });

  const goatType = await prisma.animalType.upsert({
    where: { name: 'goat' },
    update: {},
    create: {
      name: 'goat',
      nameAr: 'ماعز',
      nameEn: 'Goats'
    }
  });

  console.log('✅ Animal types created');

  // Create Breeds for Sheep
  const sheepBreeds = [
    { name: 'najdi', nameAr: 'نجدي', nameEn: 'Najdi' },
    { name: 'harri', nameAr: 'حري', nameEn: 'Harri' },
    { name: 'awassi', nameAr: 'عواسي', nameEn: 'Awassi' },
    { name: 'barbari', nameAr: 'بربري', nameEn: 'Barbari' }
  ];

  for (const breed of sheepBreeds) {
    const existingBreed = await prisma.breed.findFirst({
      where: { name: breed.name }
    });

    if (!existingBreed) {
      await prisma.breed.create({
        data: {
          ...breed,
          animalTypeId: sheepType.id,
          description: `سلالة ${breed.nameAr} من الأغنام`
        }
      });
    }
  }

  // Create Breeds for Goats
  const goatBreeds = [
    { name: 'ardi', nameAr: 'عارضي', nameEn: 'Ardi' },
    { name: 'damascus', nameAr: 'شامي', nameEn: 'Damascus' },
    { name: 'jamunapari', nameAr: 'جامونابري', nameEn: 'Jamunapari' },
    { name: 'boer', nameAr: 'بوير', nameEn: 'Boer' }
  ];

  for (const breed of goatBreeds) {
    const existingBreed = await prisma.breed.findFirst({
      where: { name: breed.name }
    });

    if (!existingBreed) {
      await prisma.breed.create({
        data: {
          ...breed,
          animalTypeId: goatType.id,
          description: `سلالة ${breed.nameAr} من الماعز`
        }
      });
    }
  }

  console.log('✅ Breeds created');

  // Create Feed Types
  const feedTypes = [
    {
      name: 'barley',
      nameAr: 'شعير',
      nameEn: 'Barley',
      type: 'CONCENTRATE' as const,
      weightKg: 1.0,
      pricePerKg: 2.5
    },
    {
      name: 'alfalfa',
      nameAr: 'برسيم',
      nameEn: 'Alfalfa',
      type: 'ROUGHAGE' as const,
      weightKg: 1.0,
      pricePerKg: 3.0
    },
    {
      name: 'corn',
      nameAr: 'ذرة',
      nameEn: 'Corn',
      type: 'CONCENTRATE' as const,
      weightKg: 1.0,
      pricePerKg: 2.8
    },
    {
      name: 'hay',
      nameAr: 'تبن',
      nameEn: 'Hay',
      type: 'ROUGHAGE' as const,
      weightKg: 1.0,
      pricePerKg: 1.5
    },
    {
      name: 'vitamins',
      nameAr: 'فيتامينات',
      nameEn: 'Vitamins',
      type: 'SUPPLEMENT' as const,
      weightKg: 0.1,
      pricePerKg: 50.0
    }
  ];

  for (const feedType of feedTypes) {
    await prisma.feedType.upsert({
      where: { name: feedType.name },
      update: {},
      create: {
        ...feedType,
        description: `علف ${feedType.nameAr}`
      }
    });
  }

  console.log('✅ Feed types created');

  // Create Reproduction Stages for Sheep
  const sheepStages = [
    {
      stageName: 'early_pregnancy',
      stageNameAr: 'حمل مبكر',
      stageNameEn: 'Early Pregnancy',
      durationDays: 50,
      dailyBarley: 0.5,
      dailyAlfalfa: 1.0,
      dailyOther: 0.1
    },
    {
      stageName: 'late_pregnancy',
      stageNameAr: 'حمل متأخر',
      stageNameEn: 'Late Pregnancy',
      durationDays: 100,
      dailyBarley: 0.8,
      dailyAlfalfa: 1.5,
      dailyOther: 0.2
    },
    {
      stageName: 'nursing',
      stageNameAr: 'رضاعة',
      stageNameEn: 'Nursing',
      durationDays: 90,
      dailyBarley: 1.0,
      dailyAlfalfa: 2.0,
      dailyOther: 0.3
    },
    {
      stageName: 'weaning',
      stageNameAr: 'فطام',
      stageNameEn: 'Weaning',
      durationDays: 30,
      dailyBarley: 0.6,
      dailyAlfalfa: 1.2,
      dailyOther: 0.1
    },
    {
      stageName: 'dry_period',
      stageNameAr: 'فترة جفاف',
      stageNameEn: 'Dry Period',
      durationDays: 60,
      dailyBarley: 0.4,
      dailyAlfalfa: 0.8,
      dailyOther: 0.05
    }
  ];

  for (const stage of sheepStages) {
    await prisma.reproductionStage.create({
      data: {
        ...stage,
        animalTypeId: sheepType.id
      }
    });
  }

  console.log('✅ Reproduction stages for sheep created');

  // Create System Settings
  const systemSettings = [
    {
      key: 'default_language',
      value: 'ar',
      description: 'Default system language',
      category: 'general'
    },
    {
      key: 'currency',
      value: 'SAR',
      description: 'Default currency',
      category: 'financial'
    },
    {
      key: 'backup_enabled',
      value: 'true',
      description: 'Enable automatic backups',
      category: 'system'
    },
    {
      key: 'alert_email_enabled',
      value: 'false',
      description: 'Enable email alerts',
      category: 'alerts'
    }
  ];

  for (const setting of systemSettings) {
    await prisma.systemSetting.upsert({
      where: { key: setting.key },
      update: {},
      create: setting
    });
  }

  console.log('✅ System settings created');

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

// API Types
export interface Animal {
  id: string;
  registrationNumber: string; // رقم التسجيل في النظام (فريد ولا يتكرر)
  tagNumber: string; // رقم التاغ (يمكن إعادة استخدامه)
  type: 'sheep' | 'goat';
  breed: string;
  gender: 'male' | 'female';
  birthDate: string;
  weight: number;
  status: 'present' | 'sold' | 'lost'; // موجود | مباع | فاقد
  barnLocation?: string; // الحظيرة المتواجد فيها الحيوان
  joinDate?: string; // تاريخ دخول القطيع
  notes?: string; // ملاحظات
}

export interface Birth {
  id: string;
  registrationNumber: string; // رقم التسجيل (تلقائي من النظام)
  tagNumber?: string; // رقم التاغ (اختياري - فقط للمواليد الحية)
  motherRegistrationNumber: string; // رقم تسجيل الأم (إلزامي)
  motherTagNumber: string; // رقم تاغ الأم (إلزامي)
  fatherTagNumber?: string; // رقم تاغ الأب (اختياري)
  birthDate: string; // تاريخ الميلاد
  gender: 'male' | 'female'; // الجنس
  birthWeight: number; // الوزن عند الولادة
  type: 'sheep' | 'goat'; // النوع
  breed: string; // السلالة
  status: 'present' | 'sold' | 'dead'; // موجود | مباع | نافق
  birthType: 'single' | 'twin'; // مفرد | توأم
  twinCount?: number; // عدد التوائم (في حال التوأم)
  barnLocation?: string; // الحظيرة
  notes?: string; // ملاحظات
}

export interface Breed {
  id: string;
  name: string;
  nameEn: string;
  type: 'sheep' | 'goat';
}

export interface DashboardStats {
  totalAnimals: number;
  totalSheep: number;
  totalGoats: number;
  pregnantAnimals: number;
  newBorns: number;
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  total?: number;
}

export interface Employee {
  id: string;
  name: string;
  position: string;
  salary: number;
  hireDate: string;
  phone: string;
  email?: string;
}

export interface Feed {
  id: string;
  name: string;
  type: string;
  quantity: number;
  unit: string;
  pricePerUnit: number;
  supplier: string;
  purchaseDate: string;
}

export interface Sale {
  id: string;
  animalId: string;
  buyerName: string;
  salePrice: number;
  saleDate: string;
  notes?: string;
}

export interface Expense {
  id: string;
  category: string;
  description: string;
  amount: number;
  date: string;
  receipt?: string;
}

export interface Treatment {
  id: string;
  animalId: string;
  treatmentType: string;
  medication: string;
  dosage: string;
  treatmentDate: string;
  veterinarian?: string;
  notes?: string;
}

export interface ReproductionCycle {
  id: string;
  femaleId: string;
  maleId?: string;
  matingDate: string;
  expectedBirthDate: string;
  actualBirthDate?: string;
  numberOfOffspring?: number;
  status: 'mated' | 'pregnant' | 'delivered' | 'failed';
}

// Settings Types
export interface FarmSettings {
  farmName: string;
  ownerName: string;
  location: string;
  phone: string;
  email: string;
  currency: string;
  language: string;
  timezone: string;
}

export interface AnimalType {
  id: string;
  name: string;
  nameEn: string;
  active: boolean;
}

export interface BreedType {
  id: string;
  name: string;
  nameEn: string;
  type: 'sheep' | 'goat';
  active: boolean;
}

export interface FeedType {
  id: string;
  name: string;
  nameEn: string;
  category: 'grain' | 'forage' | 'concentrate' | 'roughage';
  unit: string;
  active: boolean;
}

export interface TreatmentType {
  id: string;
  name: string;
  nameEn: string;
  category: 'prevention' | 'treatment' | 'supplement';
  active: boolean;
}

export interface SystemSettings {
  alertDays: {
    feedExpiry: number;
    vaccination: number;
    expectedBirth: number;
  };
  backup: {
    frequency: 'daily' | 'weekly' | 'monthly';
    retentionDays: number;
  };
  thresholds: {
    minWeight: number;
    maxProductionAge: number;
  };
}

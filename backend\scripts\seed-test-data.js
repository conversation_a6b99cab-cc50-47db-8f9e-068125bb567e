const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedTestData() {
  try {
    console.log('🌱 بدء إضافة البيانات التجريبية...');

    // تنظيف البيانات الموجودة (بالترتيب الصحيح لتجنب قيود foreign key)
    console.log('🧹 تنظيف البيانات الموجودة...');
    await prisma.weightRecord.deleteMany();
    await prisma.treatment.deleteMany();
    await prisma.expense.deleteMany();
    await prisma.purchase.deleteMany();
    await prisma.sale.deleteMany();
    await prisma.employee.deleteMany();
    await prisma.animal.deleteMany();
    await prisma.breed.deleteMany();
    await prisma.animalType.deleteMany();

    // إضافة أنواع الحيوانات
    const sheepType = await prisma.animalType.upsert({
      where: { name: 'sheep' },
      update: {},
      create: {
        name: 'sheep',
        nameAr: 'أغنام',
        nameEn: 'Sheep',
      },
    });

    const goatType = await prisma.animalType.upsert({
      where: { name: 'goat' },
      update: {},
      create: {
        name: 'goat',
        nameAr: 'ماعز',
        nameEn: 'Goat',
      },
    });

    // إضافة السلالات
    const najdiBreed = await prisma.breed.create({
      data: {
        name: 'najdi',
        nameAr: 'نجدي',
        nameEn: 'Najdi',
        animalTypeId: sheepType.id,
        description: 'سلالة أغنام نجدية محلية',
      },
    });

    const shamiBreed = await prisma.breed.create({
      data: {
        name: 'shami',
        nameAr: 'شامي',
        nameEn: 'Shami',
        animalTypeId: goatType.id,
        description: 'سلالة ماعز شامية',
      },
    });

    // إضافة حيوانات تجريبية
    const animals = await Promise.all([
      prisma.animal.create({
        data: {
          internalId: 'A001',
          tagNumber: 'T001',
          animalTypeId: sheepType.id,
          breedId: najdiBreed.id,
          gender: 'FEMALE',
          category: 'MOTHER',
          source: 'PURCHASED',
          birthDate: new Date('2022-03-15'),
          birthWeight: 3.5,
          currentWeight: 45.5,
          status: 'ALIVE',
          barnLocation: 'حظيرة أ',
          notes: 'نعجة منتجة',
        },
      }),
      prisma.animal.create({
        data: {
          internalId: 'A002',
          tagNumber: 'T002',
          animalTypeId: goatType.id,
          breedId: shamiBreed.id,
          gender: 'MALE',
          category: 'FATHER',
          source: 'PURCHASED',
          birthDate: new Date('2021-08-20'),
          birthWeight: 4.2,
          currentWeight: 55.2,
          status: 'ALIVE',
          barnLocation: 'حظيرة ب',
          notes: 'فحل للتلقيح',
        },
      }),
      prisma.animal.create({
        data: {
          internalId: 'A003',
          tagNumber: 'T003',
          animalTypeId: sheepType.id,
          breedId: najdiBreed.id,
          gender: 'FEMALE',
          category: 'NEWBORN',
          source: 'INTERNAL',
          birthDate: new Date('2024-01-15'),
          birthWeight: 3.2,
          currentWeight: 25.0,
          status: 'ALIVE',
          barnLocation: 'حظيرة أ',
          notes: 'مولود جديد',
        },
      }),
    ]);

    // إضافة موظفين
    const employees = await Promise.all([
      prisma.employee.create({
        data: {
          name: 'أحمد محمد',
          idNumber: '1234567890',
          position: 'مدير المزرعة',
          monthlySalary: 8000,
          hireDate: new Date('2023-01-01'),
          status: 'ACTIVE',
          phone: '0501234567',
          address: 'الرياض',
          notes: 'موظف متميز',
        },
      }),
      prisma.employee.create({
        data: {
          name: 'سالم أحمد',
          idNumber: '0987654321',
          position: 'راعي',
          monthlySalary: 4000,
          hireDate: new Date('2023-06-01'),
          status: 'ACTIVE',
          phone: '0507654321',
          address: 'الخرج',
        },
      }),
    ]);

    // إضافة مبيعات
    const sales = await Promise.all([
      prisma.sale.create({
        data: {
          saleDate: new Date('2024-11-15'),
          saleType: 'MARKET',
          totalPrice: 15000,
          customerName: 'شركة التجارة المتقدمة',
          customerPhone: '0501111111',
          notes: 'بيع 10 رؤوس أغنام',
        },
      }),
      prisma.sale.create({
        data: {
          saleDate: new Date('2024-10-20'),
          saleType: 'INDIVIDUAL',
          totalPrice: 3000,
          customerName: 'محمد علي',
          customerPhone: '0502222222',
          notes: 'بيع خروف واحد',
        },
      }),
    ]);

    // إضافة مشتريات
    const purchases = await Promise.all([
      prisma.purchase.create({
        data: {
          type: 'OTHER',
          description: 'علف مركز للأغنام',
          quantity: 100,
          unitPrice: 45,
          totalCost: 4500,
          purchaseDate: new Date('2024-11-01'),
          supplier: 'شركة الأعلاف المتقدمة',
          notes: 'علف عالي الجودة',
        },
      }),
      prisma.purchase.create({
        data: {
          type: 'MEDICINE',
          description: 'أدوية بيطرية',
          quantity: 10,
          unitPrice: 50,
          totalCost: 500,
          purchaseDate: new Date('2024-10-15'),
          supplier: 'الصيدلية البيطرية',
          notes: 'أدوية للتطعيم',
        },
      }),
    ]);

    // إضافة مصروفات
    const expenses = await Promise.all([
      prisma.expense.create({
        data: {
          description: 'فاتورة كهرباء',
          category: 'كهرباء',
          amount: 1200,
          date: new Date('2024-11-01'),
          notes: 'فاتورة شهر أكتوبر',
        },
      }),
      prisma.expense.create({
        data: {
          description: 'صيانة المعدات',
          category: 'صيانة',
          amount: 800,
          date: new Date('2024-10-25'),
          notes: 'صيانة دورية',
        },
      }),
    ]);

    // إضافة علاجات
    const treatments = await Promise.all([
      prisma.treatment.create({
        data: {
          animalId: animals[0].id,
          diseaseType: 'تطعيم وقائي',
          medicine: 'لقاح الحمى القلاعية',
          dosage: '2 مل',
          date: new Date('2024-10-15'),
          administeredBy: 'د. سالم أحمد',
          cost: 25,
          notes: 'تطعيم سنوي',
        },
      }),
      prisma.treatment.create({
        data: {
          animalId: animals[1].id,
          diseaseType: 'علاج طفيليات',
          medicine: 'مضاد طفيليات',
          dosage: '5 مل',
          date: new Date('2024-09-20'),
          administeredBy: 'د. محمد حسن',
          cost: 40,
          notes: 'علاج وقائي',
        },
      }),
    ]);

    // إضافة سجلات أوزان
    const weightRecords = await Promise.all([
      prisma.weightRecord.create({
        data: {
          animalId: animals[0].id,
          weight: 45.5,
          date: new Date('2024-11-01'),
          notes: 'وزن طبيعي',
        },
      }),
      prisma.weightRecord.create({
        data: {
          animalId: animals[1].id,
          weight: 55.2,
          date: new Date('2024-11-01'),
          notes: 'وزن ممتاز',
        },
      }),
      prisma.weightRecord.create({
        data: {
          animalId: animals[2].id,
          weight: 25.0,
          date: new Date('2024-11-01'),
          notes: 'نمو جيد',
        },
      }),
    ]);

    console.log('✅ تم إضافة البيانات التجريبية بنجاح!');
    console.log(`📊 الإحصائيات:`);
    console.log(`   - الحيوانات: ${animals.length}`);
    console.log(`   - الموظفين: ${employees.length}`);
    console.log(`   - المبيعات: ${sales.length}`);
    console.log(`   - المشتريات: ${purchases.length}`);
    console.log(`   - المصروفات: ${expenses.length}`);
    console.log(`   - العلاجات: ${treatments.length}`);
    console.log(`   - سجلات الأوزان: ${weightRecords.length}`);
  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات التجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedTestData();

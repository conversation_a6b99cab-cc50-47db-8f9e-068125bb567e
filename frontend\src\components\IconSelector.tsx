import { Box, Typography } from '@mui/material';
import React from 'react';
import { useThemeStore } from '../store/themeStore';

// Available icons organized by categories
export const iconCategories = {
  animals: {
    name: 'الحيوانات والطيور',
    icons: [
      '🐑',
      '🐐',
      '🐄',
      '🐎',
      '🐪',
      '🐔',
      '🦆',
      '🦃',
      '🐰',
      '🐱',
      '🐶',
      '🐷',
      '🦌',
      '🦏',
      '🐘',
      '🦒',
    ],
  },
  food: {
    name: 'الأعلاف والطعام',
    icons: [
      '🌾',
      '🌽',
      '🥕',
      '🥬',
      '🍃',
      '🌿',
      '🌱',
      '🌳',
      '🍎',
      '🍌',
      '🥜',
      '🌰',
      '🍯',
      '🥛',
      '🧄',
      '🫘',
    ],
  },
  medical: {
    name: 'الطب والعلاج',
    icons: [
      '💊',
      '💉',
      '🩹',
      '🩺',
      '⚕️',
      '🏥',
      '🧪',
      '🔬',
      '🩸',
      '🦠',
      '🧬',
      '⚗️',
      '🧴',
      '🩻',
      '🔍',
      '🧫',
    ],
  },
  buildings: {
    name: 'المباني والحظائر',
    icons: [
      '🏠',
      '🏢',
      '🏭',
      '🏗️',
      '🏘️',
      '🏚️',
      '🏛️',
      '🏰',
      '🏯',
      '🏟️',
      '🏪',
      '🏬',
      '🏤',
      '🏦',
      '🏨',
      '🏩',
    ],
  },
  tools: {
    name: 'الأدوات والمعدات',
    icons: [
      '🔧',
      '⚙️',
      '🔨',
      '🛠️',
      '🔩',
      '⛏️',
      '🪓',
      '🔪',
      '✂️',
      '📏',
      '📐',
      '🔗',
      '⛓️',
      '🪝',
      '🧰',
      '🪚',
    ],
  },
  nature: {
    name: 'الطبيعة والبيئة',
    icons: [
      '🌱',
      '🌿',
      '🍃',
      '🌾',
      '🌳',
      '🌲',
      '🌴',
      '🌵',
      '🌸',
      '🌺',
      '🌻',
      '🌷',
      '🌹',
      '🌼',
      '🌙',
      '☀️',
    ],
  },
  money: {
    name: 'العملات والأموال',
    icons: [
      '💵',
      '💴',
      '💶',
      '💷',
      '💰',
      '💸',
      '💲',
      '💹',
      '🪙',
      '💎',
      '🏆',
      '🥇',
      '🥈',
      '🥉',
      '🎖️',
      '🏅',
    ],
  },
  cards: {
    name: 'البطاقات والرقمي',
    icons: [
      '💳',
      '🎫',
      '🏧',
      '📱',
      '💻',
      '⌚',
      '🔐',
      '🔑',
      '📟',
      '📠',
      '📞',
      '☎️',
      '📧',
      '📨',
      '📩',
      '📤',
    ],
  },
  banks: {
    name: 'البنوك والمؤسسات',
    icons: [
      '🏦',
      '🏪',
      '🏬',
      '🏢',
      '🏛️',
      '🏤',
      '🏭',
      '🏘️',
      '🏙️',
      '🌆',
      '🌇',
      '🌃',
      '🏞️',
      '🏜️',
      '🏝️',
      '🏖️',
    ],
  },
  documents: {
    name: 'المستندات والتقارير',
    icons: [
      '📄',
      '📋',
      '📊',
      '📈',
      '📉',
      '📝',
      '📑',
      '📜',
      '📰',
      '📓',
      '📔',
      '📕',
      '📗',
      '📘',
      '📙',
      '📚',
    ],
  },
  symbols: {
    name: 'رموز ومؤثرات',
    icons: [
      '⚡',
      '🔥',
      '💫',
      '✨',
      '🌟',
      '⭐',
      '🌙',
      '☀️',
      '🌠',
      '💥',
      '💢',
      '💨',
      '💦',
      '💧',
      '❄️',
      '🌈',
    ],
  },
  transport: {
    name: 'النقل والمواصلات',
    icons: [
      '🚗',
      '🚚',
      '✈️',
      '🚢',
      '🚲',
      '🏍️',
      '🚌',
      '🚕',
      '🚐',
      '🚛',
      '🚜',
      '🏎️',
      '🚓',
      '🚑',
      '🚒',
      '🚁',
    ],
  },
  activities: {
    name: 'الأنشطة والفعاليات',
    icons: [
      '🎯',
      '🎪',
      '🎨',
      '🎭',
      '🎮',
      '🎲',
      '🎸',
      '🎺',
      '🎻',
      '🎹',
      '🥁',
      '🎤',
      '🎧',
      '🎬',
      '🎥',
      '📷',
    ],
  },
  misc: {
    name: 'متنوعة',
    icons: [
      '📍',
      '📌',
      '📎',
      '🔖',
      '🏷️',
      '💡',
      '🔦',
      '🕯️',
      '🪔',
      '🔔',
      '🔕',
      '📢',
      '📣',
      '📯',
      '📡',
      '📻',
    ],
  },
};

// Flatten all icons for easy access
export const availableIcons = Object.values(iconCategories).flatMap(
  (category) => category.icons
);

interface IconSelectorProps {
  selectedIcon: string;
  onIconSelect: (icon: string) => void;
  title?: string;
  maxHeight?: number;
}

const IconSelector: React.FC<IconSelectorProps> = ({
  selectedIcon,
  onIconSelect,
  title = 'اختر الأيقونة',
  maxHeight = 300,
}) => {
  const { isDarkMode } = useThemeStore();
  return (
    <Box>
      <Typography
        variant="subtitle1"
        fontWeight="bold"
        color="text.primary"
        sx={{ mb: 1 }}
      >
        {title} <span style={{ color: 'red' }}>*</span>
      </Typography>

      {/* Selected Icon Display */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          mb: 2,
          p: 2,
          border: isDarkMode ? '2px solid #555' : '2px solid #e0e0e0',
          borderRadius: 2,
          backgroundColor: isDarkMode ? '#2a2a2a' : '#f9f9f9',
        }}
      >
        <Typography variant="h4">{selectedIcon}</Typography>
        <Typography variant="body1" fontWeight="bold">
          الأيقونة المختارة: {selectedIcon}
        </Typography>
      </Box>

      {/* Icon Categories */}
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        اختر أيقونة من الفئات التالية:
      </Typography>
      <Box
        sx={{
          maxHeight: maxHeight,
          overflow: 'auto',
          border: isDarkMode ? '1px solid #555' : '1px solid #e0e0e0',
          borderRadius: 2,
          p: 2,
          backgroundColor: isDarkMode ? '#1a1a1a' : '#fafafa',
        }}
      >
        {Object.entries(iconCategories).map(([categoryKey, category]) => (
          <Box key={categoryKey} sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              fontWeight="bold"
              color="primary.main"
              sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}
            >
              {category.name}
              <Box
                sx={{
                  height: 1,
                  flex: 1,
                  backgroundColor: 'primary.main',
                  opacity: 0.3,
                }}
              />
            </Typography>
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: 'repeat(8, 1fr)',
                gap: 1,
              }}
            >
              {category.icons.map((icon, index) => (
                <Box
                  key={`${categoryKey}-${icon}-${index}`}
                  onClick={() => onIconSelect(icon)}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    p: 1.5,
                    borderRadius: 1,
                    cursor: 'pointer',
                    backgroundColor:
                      selectedIcon === icon
                        ? '#2196f3'
                        : isDarkMode
                        ? '#333'
                        : 'white',
                    color: selectedIcon === icon ? 'white' : 'inherit',
                    border:
                      selectedIcon === icon
                        ? '2px solid #1976d2'
                        : isDarkMode
                        ? '1px solid #555'
                        : '1px solid #e0e0e0',
                    boxShadow:
                      selectedIcon === icon
                        ? '0 2px 8px rgba(33, 150, 243, 0.3)'
                        : '0 1px 3px rgba(0, 0, 0, 0.1)',
                    '&:hover': {
                      backgroundColor:
                        selectedIcon === icon
                          ? '#1976d2'
                          : isDarkMode
                          ? '#444'
                          : '#e3f2fd',
                      transform: 'scale(1.1)',
                      boxShadow: '0 4px 12px rgba(33, 150, 243, 0.4)',
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                  title={`اختر ${icon} - ${category.name}`}
                >
                  <Typography variant="h5">{icon}</Typography>
                </Box>
              ))}
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default IconSelector;

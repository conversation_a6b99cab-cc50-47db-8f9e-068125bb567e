import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, Alert } from '@mui/material';

const BackupTest: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAPI = async () => {
    setLoading(true);
    setResult('جاري الاختبار...');
    
    try {
      // اختبار 1: الاتصال المباشر
      console.log('Testing direct connection...');
      const response1 = await fetch('http://localhost:3001/api/backup/tables');
      console.log('Direct response:', response1.status, response1.ok);
      
      if (response1.ok) {
        const data1 = await response1.json();
        console.log('Direct data:', data1);
        setResult(`✅ الاتصال المباشر نجح: ${JSON.stringify(data1, null, 2)}`);
      } else {
        setResult(`❌ الاتصال المباشر فشل: ${response1.status}`);
      }
    } catch (error) {
      console.error('Test error:', error);
      setResult(`❌ خطأ في الاختبار: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testHealth = async () => {
    setLoading(true);
    setResult('جاري اختبار الصحة...');
    
    try {
      const response = await fetch('http://localhost:3001/health');
      console.log('Health response:', response.status, response.ok);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Health data:', data);
        setResult(`✅ اختبار الصحة نجح: ${JSON.stringify(data, null, 2)}`);
      } else {
        setResult(`❌ اختبار الصحة فشل: ${response.status}`);
      }
    } catch (error) {
      console.error('Health test error:', error);
      setResult(`❌ خطأ في اختبار الصحة: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        اختبار الاتصال بـ API
      </Typography>
      
      <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
        <Button 
          variant="contained" 
          onClick={testHealth}
          disabled={loading}
        >
          اختبار الصحة
        </Button>
        <Button 
          variant="contained" 
          onClick={testAPI}
          disabled={loading}
        >
          اختبار API الجداول
        </Button>
      </Box>

      {result && (
        <Alert severity={result.includes('✅') ? 'success' : 'error'}>
          <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
            {result}
          </pre>
        </Alert>
      )}
    </Box>
  );
};

export default BackupTest;

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export const API_ENDPOINTS = {
  // Backup endpoints
  BACKUP_TABLES: `${API_BASE_URL}/api/backup/tables`,
  BACKUP_EXPORT: (tables: string, format: string) => 
    `${API_BASE_URL}/api/backup/export/${tables}?format=${format}`,
  BACKUP_VALIDATE: `${API_BASE_URL}/api/backup/validate`,
  BACKUP_IMPORT: `${API_BASE_URL}/api/backup/import`,
  
  // Other endpoints can be added here
  HEALTH: `${API_BASE_URL}/health`,
  TEST: `${API_BASE_URL}/api/test`,
};

export default API_BASE_URL;

import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  GetApp as ExportIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import {
  <PERSON>ert,
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  MenuItem,
  Paper,
  Popover,
  Select,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeStore } from '../store/themeStore';

// Types
interface Sale {
  id: string;
  saleMethod: string; // 'wholesale' | 'retail'
  saleDate: string;
  category: string;
  productDescription: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalAmount: number;
  buyerName?: string;
  paymentMethod: string;
  notes?: string;
  createdAt: string;
}

interface SaleCategory {
  id: string;
  name: string;
  nameEn?: string;
  icon: string;
  active: boolean;
}

interface SaleMethod {
  id: string;
  name: string;
  nameEn: string;
  icon: string;
  active: boolean;
}

interface PaymentMethod {
  id: string;
  name: string;
  nameEn: string;
  icon: string;
  active: boolean;
}

// Icon library for categories and methods
const ICON_LIBRARY = {
  animals: ['🐑', '🐏', '🐐', '🐄', '🐃', '🐂', '🐎', '🐴', '🐷', '🐖'],
  products: ['🥛', '🧀', '🥚', '🍖', '🥩', '🍗', '🥓', '🍯', '🌾', '🌽'],
  business: ['💰', '💵', '💴', '💶', '💷', '💳', '💎', '🏪', '🏬', '🏭'],
  tools: ['🔧', '🔨', '⚒️', '🛠️', '⚙️', '🔩', '⛏️', '🪓', '🪚', '🔪'],
  transport: ['🚚', '🚛', '🚜', '🚐', '🚗', '🚙', '🚕', '🚌', '🚎', '🏍️'],
  nature: ['🌱', '🌿', '🍃', '🌳', '🌲', '🌴', '🌵', '🌾', '🌻', '🌺'],
  food: ['🍎', '🍊', '🍋', '🍌', '🍇', '🍓', '🫐', '🍈', '🍉', '🍑'],
  medical: ['💊', '💉', '🩹', '🩺', '⚕️', '🏥', '🚑', '🧬', '🦠', '🧪'],
  energy: ['⚡', '🔋', '⛽', '🛢️', '💡', '🔥', '☀️', '🌙', '⭐', '💫'],
  water: ['💧', '🌊', '🚿', '🚰', '⛲', '🏊', '🌧️', '☔', '❄️', '🧊'],
};

const Sales: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { isDarkMode } = useThemeStore();

  // States
  const [sales, setSales] = useState<Sale[]>([]);
  const [saleCategories, setSaleCategories] = useState<SaleCategory[]>([]);
  const [saleMethods, setSaleMethods] = useState<SaleMethod[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editingSale, setEditingSale] = useState<Sale | null>(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  // Categories Management
  const [categoriesDialog, setCategoriesDialog] = useState(false);
  const [editingCategory, setEditingCategory] = useState<SaleCategory | null>(
    null
  );
  const [categoryForm, setCategoryForm] = useState({
    name: '',
    nameEn: '',
    icon: '',
    active: true,
  });

  // Sale Methods Management
  const [methodsDialog, setMethodsDialog] = useState(false);
  const [editingMethod, setEditingMethod] = useState<SaleMethod | null>(null);
  const [methodForm, setMethodForm] = useState({
    name: '',
    nameEn: '',
    icon: '',
    active: true,
  });

  // Icon picker states
  const [categoryIconAnchor, setCategoryIconAnchor] =
    useState<HTMLElement | null>(null);
  const [methodIconAnchor, setMethodIconAnchor] = useState<HTMLElement | null>(
    null
  );

  // Units Management
  const [units, setUnits] = useState<string[]>([
    'رأس',
    'كيلو',
    'طبق',
    'صندوق',
    'كيس',
    'لتر',
    'متر',
    'قطعة',
    'حبة',
    'علبة',
  ]);

  // Form data
  const [formData, setFormData] = useState({
    saleMethod: '',
    saleDate: new Date().toISOString().split('T')[0],
    category: '',
    productDescription: '',
    quantity: '',
    unit: '',
    unitPrice: '',
    totalAmount: '',
    buyerName: '',
    paymentMethod: '',
    notes: '',
  });

  // Filters
  const [filterCategory, setFilterCategory] = useState('');
  const [filterSaleMethod, setFilterSaleMethod] = useState('');
  const [filterPaymentMethod, setFilterPaymentMethod] = useState('');
  const [filterDateFrom, setFilterDateFrom] = useState('');
  const [filterDateTo, setFilterDateTo] = useState('');

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Load data on component mount
  useEffect(() => {
    loadData();
    loadUnits();
  }, []);

  const loadUnits = () => {
    try {
      const savedUnits = localStorage.getItem('units');
      if (savedUnits) {
        setUnits(JSON.parse(savedUnits));
      }
    } catch (error) {
      console.error('Error loading units:', error);
    }
  };

  const loadData = () => {
    try {
      // Load sales
      const savedSales = localStorage.getItem('sales');
      if (savedSales) {
        setSales(JSON.parse(savedSales));
      }

      // Load sale categories
      const savedCategories = localStorage.getItem('saleCategories');
      if (savedCategories) {
        setSaleCategories(JSON.parse(savedCategories));
      } else {
        // Add default sale categories
        const defaultCategories: SaleCategory[] = [
          {
            id: '1',
            name: 'أغنام لباني',
            nameEn: 'Breeding Sheep',
            icon: '🐑',
            active: true,
          },
          {
            id: '2',
            name: 'أغنام تسمين',
            nameEn: 'Fattening Sheep',
            icon: '🐏',
            active: true,
          },
          {
            id: '3',
            name: 'أغنام إحلال',
            nameEn: 'Replacement Sheep',
            icon: '🐐',
            active: true,
          },
          {
            id: '4',
            name: 'منتجات مشتل',
            nameEn: 'Nursery Products',
            icon: '🌱',
            active: true,
          },
          {
            id: '5',
            name: 'منتجات فقاسة',
            nameEn: 'Hatchery Products',
            icon: '🐣',
            active: true,
          },
          {
            id: '6',
            name: 'بيض مائدة',
            nameEn: 'Table Eggs',
            icon: '🥚',
            active: true,
          },
        ];
        setSaleCategories(defaultCategories);
        localStorage.setItem(
          'saleCategories',
          JSON.stringify(defaultCategories)
        );
      }

      // Load sale methods
      const savedMethods = localStorage.getItem('saleMethods');
      if (savedMethods) {
        setSaleMethods(JSON.parse(savedMethods));
      } else {
        // Add default sale methods
        const defaultMethods: SaleMethod[] = [
          {
            id: '1',
            name: 'جملة',
            nameEn: 'Wholesale',
            icon: '📦',
            active: true,
          },
          {
            id: '2',
            name: 'مفرد/تجزئة',
            nameEn: 'Retail',
            icon: '🛒',
            active: true,
          },
        ];
        setSaleMethods(defaultMethods);
        localStorage.setItem('saleMethods', JSON.stringify(defaultMethods));
      }

      // Load payment methods (reuse from purchases/expenses)
      const savedPaymentMethods = localStorage.getItem('paymentMethods');
      if (savedPaymentMethods) {
        setPaymentMethods(JSON.parse(savedPaymentMethods));
      } else {
        // Add default payment methods
        const defaultPaymentMethods: PaymentMethod[] = [
          { id: '1', name: 'نقدي', nameEn: 'Cash', icon: '💵', active: true },
          { id: '2', name: 'شيك', nameEn: 'Check', icon: '📄', active: true },
          {
            id: '3',
            name: 'تحويل بنكي',
            nameEn: 'Bank Transfer',
            icon: '🏦',
            active: true,
          },
          {
            id: '4',
            name: 'بطاقة ائتمان',
            nameEn: 'Credit Card',
            icon: '💳',
            active: true,
          },
          {
            id: '5',
            name: 'بطاقة مدى',
            nameEn: 'Mada Card',
            icon: '💳',
            active: true,
          },
          {
            id: '6',
            name: 'محفظة إلكترونية',
            nameEn: 'E-Wallet',
            icon: '📱',
            active: true,
          },
        ];
        setPaymentMethods(defaultPaymentMethods);
        localStorage.setItem(
          'paymentMethods',
          JSON.stringify(defaultPaymentMethods)
        );
      }
    } catch (error) {
      console.error('Error loading sales data:', error);
    }
  };

  // Helper functions
  const getActiveCategories = () => {
    return saleCategories.filter((cat) => cat.active);
  };

  const getActiveSaleMethods = () => {
    return saleMethods.filter((method) => method.active);
  };

  const getActivePaymentMethods = () => {
    return paymentMethods.filter((method) => method.active);
  };

  const getCategoryInfo = (categoryName: string) => {
    return saleCategories.find((cat) => cat.name === categoryName);
  };

  const getSaleMethodInfo = (methodName: string) => {
    return saleMethods.find((method) => method.name === methodName);
  };

  const getPaymentMethodInfo = (methodName: string) => {
    return paymentMethods.find((method) => method.name === methodName);
  };

  // Calculate total amount when quantity or unit price changes
  useEffect(() => {
    if (formData.quantity && formData.unitPrice) {
      const total =
        parseFloat(formData.quantity) * parseFloat(formData.unitPrice);
      setFormData((prev) => ({ ...prev, totalAmount: total.toFixed(2) }));
    }
  }, [formData.quantity, formData.unitPrice]);

  // Calculate unit price when total amount and quantity changes (for wholesale)
  useEffect(() => {
    if (
      formData.saleMethod === 'جملة' &&
      formData.quantity &&
      formData.totalAmount
    ) {
      const unitPrice =
        parseFloat(formData.totalAmount) / parseFloat(formData.quantity);
      setFormData((prev) => ({ ...prev, unitPrice: unitPrice.toFixed(2) }));
    }
  }, [formData.totalAmount, formData.quantity, formData.saleMethod]);

  // Reset form
  const resetForm = () => {
    setFormData({
      saleMethod: '',
      saleDate: new Date().toISOString().split('T')[0],
      category: '',
      productDescription: '',
      quantity: '',
      unit: '',
      unitPrice: '',
      totalAmount: '',
      buyerName: '',
      paymentMethod: '',
      notes: '',
    });
    setEditingSale(null);
  };

  // Handle form submission
  const handleSubmit = () => {
    try {
      const newSale: Sale = {
        id: editingSale ? editingSale.id : Date.now().toString(),
        saleMethod: formData.saleMethod,
        saleDate: formData.saleDate,
        category: formData.category,
        productDescription: formData.productDescription,
        quantity: parseFloat(formData.quantity),
        unit: formData.unit,
        unitPrice: parseFloat(formData.unitPrice),
        totalAmount: parseFloat(formData.totalAmount),
        buyerName: formData.buyerName,
        paymentMethod: formData.paymentMethod,
        notes: formData.notes,
        createdAt: editingSale
          ? editingSale.createdAt
          : new Date().toISOString(),
      };

      let updatedSales;
      if (editingSale) {
        updatedSales = sales.map((sale) =>
          sale.id === editingSale.id ? newSale : sale
        );
        setSnackbar({
          open: true,
          message: 'تم تحديث عملية البيع بنجاح',
          severity: 'success',
        });
      } else {
        updatedSales = [...sales, newSale];
        setSnackbar({
          open: true,
          message: 'تم إضافة عملية البيع بنجاح',
          severity: 'success',
        });
      }

      setSales(updatedSales);
      localStorage.setItem('sales', JSON.stringify(updatedSales));
      resetForm();
      setOpenDialog(false);
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'حدث خطأ في حفظ البيانات',
        severity: 'error',
      });
    }
  };

  // Handle delete sale
  const handleDeleteSale = (id: string) => {
    const updatedSales = sales.filter((sale) => sale.id !== id);
    setSales(updatedSales);
    localStorage.setItem('sales', JSON.stringify(updatedSales));
    setSnackbar({
      open: true,
      message: 'تم حذف عملية البيع بنجاح',
      severity: 'success',
    });
  };

  // Handle edit sale
  const handleEditSale = (sale: Sale) => {
    setEditingSale(sale);
    setFormData({
      saleMethod: sale.saleMethod,
      saleDate: sale.saleDate,
      category: sale.category,
      productDescription: sale.productDescription,
      quantity: sale.quantity.toString(),
      unit: sale.unit,
      unitPrice: sale.unitPrice.toString(),
      totalAmount: sale.totalAmount.toString(),
      buyerName: sale.buyerName || '',
      paymentMethod: sale.paymentMethod,
      notes: sale.notes || '',
    });
    setOpenDialog(true);
  };

  // Reset filters
  const handleResetFilters = () => {
    setFilterCategory('');
    setFilterSaleMethod('');
    setFilterPaymentMethod('');
    setFilterDateFrom('');
    setFilterDateTo('');
    setSearchTerm('');
    setPage(0);
  };

  // Icon picker component
  const IconPicker = ({
    anchorEl,
    onClose,
    onSelect,
  }: {
    anchorEl: HTMLElement | null;
    onClose: () => void;
    onSelect: (icon: string) => void;
  }) => (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
    >
      <Box sx={{ p: 2, maxWidth: 300 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
          اختر أيقونة
        </Typography>
        {Object.entries(ICON_LIBRARY).map(([category, icons]) => (
          <Box key={category} sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ mb: 1, fontWeight: 'bold', textTransform: 'capitalize' }}
            >
              {category === 'animals' && 'حيوانات'}
              {category === 'products' && 'منتجات'}
              {category === 'business' && 'أعمال'}
              {category === 'tools' && 'أدوات'}
              {category === 'transport' && 'نقل'}
              {category === 'nature' && 'طبيعة'}
              {category === 'food' && 'طعام'}
              {category === 'medical' && 'طبي'}
              {category === 'energy' && 'طاقة'}
              {category === 'water' && 'مياه'}
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {icons.map((icon) => (
                <IconButton
                  key={icon}
                  onClick={() => {
                    onSelect(icon);
                    onClose();
                  }}
                  sx={{
                    fontSize: '1.5rem',
                    width: 40,
                    height: 40,
                    border: '1px solid #e0e0e0',
                    borderRadius: 1,
                    '&:hover': {
                      backgroundColor: 'primary.light',
                      borderColor: 'primary.main',
                    },
                  }}
                >
                  {icon}
                </IconButton>
              ))}
            </Box>
          </Box>
        ))}
      </Box>
    </Popover>
  );

  // Helper function for field styles
  const getFieldStyles = (borderColor: string = 'primary.main') => ({
    backgroundColor: isDarkMode ? 'grey.800' : 'white',
    borderRadius: 2,
    '& .MuiOutlinedInput-root': {
      height: 56,
      '& fieldset': {
        borderColor,
        borderWidth: '2px',
      },
      '&:hover fieldset': {
        borderColor: `${borderColor}`,
      },
      '&.Mui-focused fieldset': {
        borderColor: `${borderColor}`,
      },
    },
    '& .MuiInputBase-input': {
      color: isDarkMode ? 'white' : 'inherit',
    },
    '& .MuiSelect-select': {
      color: isDarkMode ? 'white' : 'inherit',
    },
  });

  // Export to Excel
  const handleExportToExcel = () => {
    try {
      const exportData = filteredSales.map((sale) => ({
        التاريخ: sale.saleDate,
        'طريقة البيع': sale.saleMethod,
        التصنيف: sale.category,
        'وصف المنتج': sale.productDescription,
        الكمية: sale.quantity,
        الوحدة: sale.unit,
        'سعر الوحدة': sale.unitPrice,
        'المبلغ الإجمالي': sale.totalAmount,
        'اسم المشتري': sale.buyerName || '',
        'طريقة الدفع': sale.paymentMethod,
        ملاحظات: sale.notes || '',
      }));

      // Create CSV content
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map((row) =>
          headers.map((header) => `"${row[header] || ''}"`).join(',')
        ),
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute(
        'download',
        `sales_${new Date().toISOString().split('T')[0]}.csv`
      );
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setSnackbar({
        open: true,
        message: 'تم تصدير البيانات بنجاح',
        severity: 'success',
      });
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'حدث خطأ في تصدير البيانات',
        severity: 'error',
      });
    }
  };

  // Filter sales based on search and filters
  const filteredSales = sales.filter((sale) => {
    const matchesSearch =
      !searchTerm ||
      sale.productDescription
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      sale.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (sale.buyerName &&
        sale.buyerName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = !filterCategory || sale.category === filterCategory;
    const matchesSaleMethod =
      !filterSaleMethod || sale.saleMethod === filterSaleMethod;
    const matchesPaymentMethod =
      !filterPaymentMethod || sale.paymentMethod === filterPaymentMethod;

    const matchesDateFrom = !filterDateFrom || sale.saleDate >= filterDateFrom;
    const matchesDateTo = !filterDateTo || sale.saleDate <= filterDateTo;

    return (
      matchesSearch &&
      matchesCategory &&
      matchesSaleMethod &&
      matchesPaymentMethod &&
      matchesDateFrom &&
      matchesDateTo
    );
  });

  return (
    // Updated Sales Page with white background and full width
    <Box
      sx={{
        width: '100%',
        height: '100vh',
        backgroundColor: theme.palette.background.default,
        m: 0,
        p: 0,
        overflow: 'auto',
      }}
    >
      {/* Page Title */}
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3, px: 2 }}>
        💰 إدارة المبيعات
      </Typography>

      {/* Filters */}
      <Paper
        sx={{
          p: 2,
          mx: 1,
          mb: 2,
          borderRadius: 2,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Grid container spacing={2} alignItems="end">
          {/* Date From */}
          <Grid item xs={12} sm={6} md={1.5}>
            <Box>
              <Typography
                variant="caption"
                sx={{
                  mb: 0.5,
                  fontWeight: 'bold',
                  color: 'info.main',
                  display: 'block',
                }}
              >
                📅 من تاريخ
              </Typography>
              <TextField
                fullWidth
                type="date"
                size="small"
                value={filterDateFrom}
                onChange={(e) => setFilterDateFrom(e.target.value)}
              />
            </Box>
          </Grid>

          {/* Date To */}
          <Grid item xs={12} sm={6} md={1.5}>
            <Box>
              <Typography
                variant="caption"
                sx={{
                  mb: 0.5,
                  fontWeight: 'bold',
                  color: 'info.main',
                  display: 'block',
                }}
              >
                📅 إلى تاريخ
              </Typography>
              <TextField
                fullWidth
                type="date"
                size="small"
                value={filterDateTo}
                onChange={(e) => setFilterDateTo(e.target.value)}
              />
            </Box>
          </Grid>

          {/* Search */}
          <Grid item xs={12} md={2.5}>
            <Box>
              <Typography
                variant="caption"
                sx={{
                  mb: 0.5,
                  fontWeight: 'bold',
                  color: 'success.main',
                  display: 'block',
                }}
              >
                🔍 البحث في المبيعات
              </Typography>
              <TextField
                fullWidth
                placeholder="منتج، مشتري، تفاصيل..."
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </Box>
          </Grid>

          {/* Category Filter */}
          <Grid item xs={12} sm={6} md={1.5}>
            <Box>
              <Typography
                variant="caption"
                sx={{
                  mb: 0.5,
                  fontWeight: 'bold',
                  color: 'primary.main',
                  display: 'block',
                }}
              >
                🏷️ التصنيف
              </Typography>
              <FormControl fullWidth size="small">
                <Select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  displayEmpty
                >
                  <MenuItem value="">الكل</MenuItem>
                  {getActiveCategories().map((category) => (
                    <MenuItem key={category.id} value={category.name}>
                      {category.icon} {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>

          {/* Sale Method Filter */}
          <Grid item xs={12} sm={6} md={1.5}>
            <Box>
              <Typography
                variant="caption"
                sx={{
                  mb: 0.5,
                  fontWeight: 'bold',
                  color: 'secondary.main',
                  display: 'block',
                }}
              >
                💳 طريقة البيع
              </Typography>
              <FormControl fullWidth size="small">
                <Select
                  value={filterSaleMethod}
                  onChange={(e) => setFilterSaleMethod(e.target.value)}
                  displayEmpty
                >
                  <MenuItem value="">الكل</MenuItem>
                  {getActiveSaleMethods().map((method) => (
                    <MenuItem key={method.id} value={method.name}>
                      {method.icon} {method.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>

          {/* Payment Method Filter */}
          <Grid item xs={12} sm={6} md={1.5}>
            <Box>
              <Typography
                variant="caption"
                sx={{
                  mb: 0.5,
                  fontWeight: 'bold',
                  color: 'text.secondary',
                  display: 'block',
                }}
              >
                💰 طريقة الدفع
              </Typography>
              <FormControl fullWidth size="small">
                <Select
                  value={filterPaymentMethod}
                  onChange={(e) => setFilterPaymentMethod(e.target.value)}
                  displayEmpty
                >
                  <MenuItem value="">الكل</MenuItem>
                  {getActivePaymentMethods().map((method) => (
                    <MenuItem key={method.id} value={method.name}>
                      {method.icon} {method.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>

          {/* Action Buttons - في الجانب الآخر من الصفحة */}
          <Grid item xs={12} md={1.5}>
            <Box>
              <Typography
                variant="caption"
                sx={{
                  mb: 0.5,
                  fontWeight: 'bold',
                  color: 'error.main',
                  display: 'block',
                }}
              >
                ⚙️ الإجراءات
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleResetFilters}
                  sx={{ fontSize: '0.7rem', minWidth: 'auto', px: 1 }}
                >
                  إعادة تعيين
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<ExportIcon sx={{ fontSize: '0.8rem' }} />}
                  onClick={handleExportToExcel}
                  sx={{ fontSize: '0.7rem', minWidth: 'auto', px: 1 }}
                >
                  Excel
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Summary Cards and Action Buttons */}
      <Box
        sx={{ display: 'flex', gap: 3, mb: 3, px: 2, alignItems: 'flex-start' }}
      >
        {/* Summary Cards */}
        <Grid container spacing={2} sx={{ flex: 1 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper
              sx={{
                p: 1.5,
                textAlign: 'center',
                backgroundColor: 'success.light',
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  color: 'success.contrastText',
                  fontSize: '1.1rem',
                }}
              >
                {sales
                  .reduce((sum, sale) => sum + sale.totalAmount, 0)
                  .toLocaleString()}{' '}
                ر.س
              </Typography>
              <Typography
                variant="caption"
                sx={{ color: 'success.contrastText', fontSize: '0.7rem' }}
              >
                💰 إجمالي المبيعات
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper
              sx={{
                p: 1.5,
                textAlign: 'center',
                backgroundColor: 'primary.light',
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.contrastText',
                  fontSize: '1.1rem',
                }}
              >
                {sales.length}
              </Typography>
              <Typography
                variant="caption"
                sx={{ color: 'primary.contrastText', fontSize: '0.7rem' }}
              >
                📊 عدد العمليات
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper
              sx={{
                p: 1.5,
                textAlign: 'center',
                backgroundColor: 'secondary.light',
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  color: 'secondary.contrastText',
                  fontSize: '1.1rem',
                }}
              >
                {sales.length > 0
                  ? (
                      sales.reduce((sum, sale) => sum + sale.totalAmount, 0) /
                      sales.length
                    ).toFixed(0)
                  : 0}{' '}
                ر.س
              </Typography>
              <Typography
                variant="caption"
                sx={{ color: 'secondary.contrastText', fontSize: '0.7rem' }}
              >
                📈 متوسط قيمة البيع
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper
              sx={{
                p: 1.5,
                textAlign: 'center',
                backgroundColor: 'info.light',
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  color: 'info.contrastText',
                  fontSize: '1.1rem',
                }}
              >
                {sales
                  .filter((sale) => {
                    const saleDate = new Date(sale.saleDate);
                    const currentDate = new Date();
                    return (
                      saleDate.getMonth() === currentDate.getMonth() &&
                      saleDate.getFullYear() === currentDate.getFullYear()
                    );
                  })
                  .reduce((sum, sale) => sum + sale.totalAmount, 0)
                  .toLocaleString()}{' '}
                ر.س
              </Typography>
              <Typography
                variant="caption"
                sx={{ color: 'info.contrastText', fontSize: '0.7rem' }}
              >
                📅 مبيعات هذا الشهر
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            alignItems: 'center',
            minWidth: 'fit-content',
          }}
        >
          <Button
            variant="outlined"
            size="small"
            startIcon={<SettingsIcon />}
            onClick={() => setCategoriesDialog(true)}
          >
            إدارة التصنيفات
          </Button>
          <Button
            variant="outlined"
            size="small"
            startIcon={<SettingsIcon />}
            onClick={() => setMethodsDialog(true)}
          >
            إدارة طرق البيع
          </Button>
          <Button
            variant="contained"
            size="small"
            startIcon={<AddIcon />}
            onClick={() => {
              setEditingSale(null);
              setFormData({
                saleMethod: '',
                saleDate: new Date().toISOString().split('T')[0],
                category: '',
                productDescription: '',
                quantity: 0,
                unit: '',
                unitPrice: 0,
                totalAmount: 0,
                buyerName: '',
                paymentMethod: '',
                notes: '',
              });
              setOpenDialog(true);
            }}
          >
            إضافة عملية بيع
          </Button>
        </Box>
      </Box>

      {/* Sales Table */}
      <Paper
        sx={{
          borderRadius: 2,
          mx: 1,
          mb: 1,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6" fontWeight="bold">
            📋 قائمة المبيعات ({filteredSales.length})
          </Typography>
        </Box>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow
                sx={{ backgroundColor: isDarkMode ? 'grey.800' : 'grey.50' }}
              >
                <TableCell>
                  <strong>التاريخ</strong>
                </TableCell>
                <TableCell>
                  <strong>طريقة البيع</strong>
                </TableCell>
                <TableCell>
                  <strong>التصنيف</strong>
                </TableCell>
                <TableCell>
                  <strong>وصف المنتج</strong>
                </TableCell>
                <TableCell>
                  <strong>الكمية</strong>
                </TableCell>
                <TableCell>
                  <strong>سعر الوحدة</strong>
                </TableCell>
                <TableCell>
                  <strong>المبلغ الإجمالي</strong>
                </TableCell>
                <TableCell>
                  <strong>المشتري</strong>
                </TableCell>
                <TableCell>
                  <strong>طريقة الدفع</strong>
                </TableCell>
                <TableCell>
                  <strong>الإجراءات</strong>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredSales.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} align="center" sx={{ py: 4 }}>
                    <Typography variant="body1" color="text.secondary">
                      لا توجد مبيعات مسجلة حتى الآن
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mt: 1 }}
                    >
                      انقر على "إضافة عملية بيع" لإضافة أول عملية بيع
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredSales
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((sale) => (
                    <TableRow key={sale.id} hover>
                      <TableCell>{sale.saleDate}</TableCell>
                      <TableCell>
                        <Chip
                          label={sale.saleMethod}
                          size="small"
                          color={
                            sale.saleMethod === 'جملة' ? 'primary' : 'secondary'
                          }
                          icon={
                            <Typography>
                              {getSaleMethodInfo(sale.saleMethod)?.icon}
                            </Typography>
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                        >
                          <Typography>
                            {getCategoryInfo(sale.category)?.icon}
                          </Typography>
                          <Typography variant="body2">
                            {sale.category}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{sale.productDescription}</TableCell>
                      <TableCell>
                        {sale.quantity} {sale.unit}
                      </TableCell>
                      <TableCell>
                        {sale.unitPrice.toLocaleString()} ر.س
                      </TableCell>
                      <TableCell>
                        <Typography fontWeight="bold" color="success.main">
                          {sale.totalAmount.toLocaleString()} ر.س
                        </Typography>
                      </TableCell>
                      <TableCell>{sale.buyerName || '-'}</TableCell>
                      <TableCell>
                        <Chip
                          label={sale.paymentMethod}
                          size="small"
                          variant="outlined"
                          icon={
                            <Typography>
                              {getPaymentMethodInfo(sale.paymentMethod)?.icon}
                            </Typography>
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() => handleEditSale(sale)}
                            sx={{ color: 'primary.main' }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteSale(sale.id)}
                            sx={{ color: 'error.main' }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {filteredSales.length > 0 && (
          <TablePagination
            component="div"
            count={filteredSales.length}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
            labelRowsPerPage="عدد الصفوف في الصفحة:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} من ${count}`
            }
          />
        )}
      </Paper>

      {/* Add/Edit Sale Dialog */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          },
        }}
      >
        <DialogTitle
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            py: 3,
            borderRadius: '12px 12px 0 0',
          }}
        >
          <Typography
            variant="h4"
            component="div"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 2,
            }}
          >
            {editingSale ? '✏️ تعديل عملية البيع' : '➕ إضافة عملية بيع جديدة'}
          </Typography>
        </DialogTitle>
        <DialogContent
          sx={{
            p: 4,
            backgroundColor: isDarkMode ? 'grey.900' : 'grey.50',
            color: isDarkMode ? 'white' : 'inherit',
          }}
        >
          <Grid container spacing={4} sx={{ mt: 1 }}>
            {/* Sale Method */}
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 1.5,
                    fontWeight: 'bold',
                    color: 'secondary.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  💳 طريقة البيع *
                </Typography>
                <FormControl fullWidth required>
                  <Select
                    value={formData.saleMethod}
                    onChange={(e) =>
                      setFormData({ ...formData, saleMethod: e.target.value })
                    }
                    displayEmpty
                    sx={getFieldStyles('secondary.main')}
                  >
                    <MenuItem value="" disabled>
                      <Typography color="text.secondary">
                        اختر طريقة البيع
                      </Typography>
                    </MenuItem>
                    {getActiveSaleMethods().map((method) => (
                      <MenuItem key={method.id} value={method.name}>
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                        >
                          <Typography>{method.icon}</Typography>
                          <Typography>{method.name}</Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Grid>

            {/* Sale Date */}
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 1.5,
                    fontWeight: 'bold',
                    color: 'info.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  📅 تاريخ البيع *
                </Typography>
                <TextField
                  fullWidth
                  required
                  type="date"
                  value={formData.saleDate}
                  onChange={(e) =>
                    setFormData({ ...formData, saleDate: e.target.value })
                  }
                  sx={getFieldStyles('info.main')}
                />
              </Box>
            </Grid>

            {/* Category */}
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 1.5,
                    fontWeight: 'bold',
                    color: 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  🏷️ تصنيف المنتج *
                </Typography>
                <FormControl fullWidth required>
                  <Select
                    value={formData.category}
                    onChange={(e) =>
                      setFormData({ ...formData, category: e.target.value })
                    }
                    displayEmpty
                    sx={getFieldStyles('primary.main')}
                  >
                    <MenuItem value="" disabled>
                      <Typography color="text.secondary">
                        اختر تصنيف المنتج
                      </Typography>
                    </MenuItem>
                    {getActiveCategories().map((category) => (
                      <MenuItem key={category.id} value={category.name}>
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                        >
                          <Typography>{category.icon}</Typography>
                          <Typography>{category.name}</Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Grid>

            {/* Product Description */}
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 1.5,
                    fontWeight: 'bold',
                    color: 'success.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  📝 وصف المنتج *
                </Typography>
                <TextField
                  fullWidth
                  required
                  value={formData.productDescription}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      productDescription: e.target.value,
                    })
                  }
                  placeholder="مثال: أغنام حري عمر سنة"
                  sx={getFieldStyles('success.main')}
                />
              </Box>
            </Grid>

            {/* Quantity */}
            <Grid item xs={12} sm={4}>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 1.5,
                    fontWeight: 'bold',
                    color: 'info.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  🔢 الكمية *
                </Typography>
                <TextField
                  fullWidth
                  required
                  type="number"
                  value={formData.quantity}
                  onChange={(e) =>
                    setFormData({ ...formData, quantity: e.target.value })
                  }
                  placeholder="أدخل الكمية"
                  inputProps={{ min: 0, step: 0.01 }}
                  sx={getFieldStyles('info.main')}
                />
              </Box>
            </Grid>

            {/* Unit */}
            <Grid item xs={12} sm={4}>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 1.5,
                    fontWeight: 'bold',
                    color: 'warning.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  📏 وحدة القياس *
                </Typography>
                <FormControl fullWidth required>
                  <Select
                    value={formData.unit}
                    onChange={(e) =>
                      setFormData({ ...formData, unit: e.target.value })
                    }
                    displayEmpty
                    sx={getFieldStyles('warning.main')}
                  >
                    <MenuItem value="" disabled>
                      <Typography color="text.secondary">
                        اختر وحدة القياس
                      </Typography>
                    </MenuItem>
                    {units.map((unit, index) => (
                      <MenuItem key={index} value={unit}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            width: '100%',
                          }}
                        >
                          <Typography>{unit}</Typography>
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              const newUnits = units.filter(
                                (_, i) => i !== index
                              );
                              setUnits(newUnits);
                              localStorage.setItem(
                                'units',
                                JSON.stringify(newUnits)
                              );
                              if (formData.unit === unit) {
                                setFormData({ ...formData, unit: '' });
                              }
                            }}
                            sx={{ ml: 1, color: 'error.main' }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </MenuItem>
                    ))}
                    <MenuItem
                      onClick={() => {
                        const newUnit = prompt('أدخل وحدة قياس جديدة:');
                        if (
                          newUnit &&
                          newUnit.trim() &&
                          !units.includes(newUnit.trim())
                        ) {
                          const newUnits = [...units, newUnit.trim()];
                          setUnits(newUnits);
                          localStorage.setItem(
                            'units',
                            JSON.stringify(newUnits)
                          );
                          setFormData({ ...formData, unit: newUnit.trim() });
                        }
                      }}
                      sx={{
                        borderTop: '1px solid',
                        borderColor: 'divider',
                        backgroundColor: 'primary.light',
                        '&:hover': {
                          backgroundColor: 'primary.main',
                          color: 'white',
                        },
                      }}
                    >
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        <AddIcon />
                        <Typography>إضافة وحدة جديدة</Typography>
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Grid>

            {/* Unit Price or Total Amount based on sale method */}
            {formData.saleMethod === 'جملة' ? (
              <Grid item xs={12} sm={4}>
                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      mb: 1.5,
                      fontWeight: 'bold',
                      color: 'success.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    }}
                  >
                    💰 المبلغ الإجمالي *
                  </Typography>
                  <TextField
                    fullWidth
                    required
                    type="number"
                    value={formData.totalAmount}
                    onChange={(e) =>
                      setFormData({ ...formData, totalAmount: e.target.value })
                    }
                    placeholder="أدخل المبلغ الإجمالي"
                    inputProps={{ min: 0, step: 0.01 }}
                    InputProps={{
                      endAdornment: (
                        <Typography
                          variant="body2"
                          sx={{
                            ml: 1,
                            color: 'success.main',
                            fontWeight: 'bold',
                          }}
                        >
                          ر.س
                        </Typography>
                      ),
                    }}
                    sx={getFieldStyles('success.main')}
                  />
                </Box>
              </Grid>
            ) : (
              <Grid item xs={12} sm={4}>
                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      mb: 1.5,
                      fontWeight: 'bold',
                      color: 'success.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    }}
                  >
                    💰 سعر الوحدة *
                  </Typography>
                  <TextField
                    fullWidth
                    required
                    type="number"
                    value={formData.unitPrice}
                    onChange={(e) =>
                      setFormData({ ...formData, unitPrice: e.target.value })
                    }
                    placeholder="أدخل سعر الوحدة"
                    inputProps={{ min: 0, step: 0.01 }}
                    InputProps={{
                      endAdornment: (
                        <Typography
                          variant="body2"
                          sx={{
                            ml: 1,
                            color: 'success.main',
                            fontWeight: 'bold',
                          }}
                        >
                          ر.س
                        </Typography>
                      ),
                    }}
                    sx={getFieldStyles('success.main')}
                  />
                </Box>
              </Grid>
            )}

            {/* Calculated field display */}
            {formData.saleMethod === 'جملة' &&
            formData.quantity &&
            formData.totalAmount ? (
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="سعر الوحدة (محسوب)"
                  value={formData.unitPrice}
                  InputProps={{
                    readOnly: true,
                    endAdornment: (
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        ر.س
                      </Typography>
                    ),
                  }}
                  sx={{ backgroundColor: isDarkMode ? 'grey.700' : 'grey.50' }}
                />
              </Grid>
            ) : formData.saleMethod === 'مفرد/تجزئة' &&
              formData.quantity &&
              formData.unitPrice ? (
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="المبلغ الإجمالي (محسوب)"
                  value={formData.totalAmount}
                  InputProps={{
                    readOnly: true,
                    endAdornment: (
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        ر.س
                      </Typography>
                    ),
                  }}
                  sx={{ backgroundColor: isDarkMode ? 'grey.700' : 'grey.50' }}
                />
              </Grid>
            ) : null}

            {/* Buyer Name */}
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 1.5,
                    fontWeight: 'bold',
                    color: 'text.primary',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  👤 اسم المشتري (اختياري)
                </Typography>
                <TextField
                  fullWidth
                  value={formData.buyerName}
                  onChange={(e) =>
                    setFormData({ ...formData, buyerName: e.target.value })
                  }
                  placeholder="اسم العميل أو المشتري"
                  sx={getFieldStyles('text.secondary')}
                />
              </Box>
            </Grid>

            {/* Payment Method */}
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 1.5,
                    fontWeight: 'bold',
                    color: 'warning.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  💰 طريقة الدفع *
                </Typography>
                <FormControl fullWidth required>
                  <Select
                    value={formData.paymentMethod}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        paymentMethod: e.target.value,
                      })
                    }
                    displayEmpty
                    sx={getFieldStyles('warning.main')}
                  >
                    <MenuItem value="" disabled>
                      <Typography color="text.secondary">
                        اختر طريقة الدفع
                      </Typography>
                    </MenuItem>
                    {getActivePaymentMethods().map((method) => (
                      <MenuItem key={method.id} value={method.name}>
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                        >
                          <Typography>{method.icon}</Typography>
                          <Typography>{method.name}</Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Grid>

            {/* Notes */}
            <Grid item xs={12}>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 1.5,
                    fontWeight: 'bold',
                    color: 'text.primary',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  📝 ملاحظات (اختياري)
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={2}
                  value={formData.notes}
                  onChange={(e) =>
                    setFormData({ ...formData, notes: e.target.value })
                  }
                  placeholder="أي ملاحظات إضافية حول عملية البيع"
                  sx={{
                    ...getFieldStyles('text.secondary'),
                    '& .MuiOutlinedInput-root': {
                      ...getFieldStyles('text.secondary')[
                        '& .MuiOutlinedInput-root'
                      ],
                      height: 'auto', // Override height for multiline
                    },
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            p: 4,
            pt: 2,
            backgroundColor: isDarkMode ? 'grey.900' : 'grey.50',
            gap: 2,
          }}
        >
          <Button
            onClick={() => {
              resetForm();
              setOpenDialog(false);
            }}
            variant="outlined"
            size="large"
            sx={{
              borderRadius: 3,
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 'bold',
              borderWidth: '2px',
              borderColor: 'grey.400',
              color: 'grey.600',
              '&:hover': {
                borderWidth: '2px',
                borderColor: 'grey.600',
                backgroundColor: isDarkMode ? 'grey.800' : 'grey.50',
              },
            }}
          >
            ❌ إلغاء
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            size="large"
            disabled={
              !formData.saleMethod ||
              !formData.category ||
              !formData.productDescription ||
              !formData.quantity ||
              !formData.unit ||
              !formData.paymentMethod ||
              (!formData.unitPrice && !formData.totalAmount)
            }
            sx={{
              borderRadius: 3,
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 'bold',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              boxShadow: '0 8px 20px rgba(102, 126, 234, 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                boxShadow: '0 12px 25px rgba(102, 126, 234, 0.4)',
                transform: 'translateY(-2px)',
              },
              '&:disabled': {
                background: 'grey.300',
                color: 'grey.500',
                boxShadow: 'none',
              },
              transition: 'all 0.3s ease',
            }}
          >
            {editingSale ? '✏️ تحديث البيانات' : '💾 حفظ العملية'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Categories Management Dialog */}
      <Dialog
        open={categoriesDialog}
        onClose={() => setCategoriesDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          },
        }}
      >
        <DialogTitle
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #4caf50 0%, #81c784 100%)',
            color: 'white',
            py: 3,
            borderRadius: '12px 12px 0 0',
          }}
        >
          <Typography
            variant="h5"
            component="div"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 2,
            }}
          >
            🏷️ إدارة التصنيفات
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              إضافة تصنيف جديد
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  color="text.primary"
                  sx={{ mb: 1 }}
                >
                  اسم التصنيف <span style={{ color: 'red' }}>*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  value={categoryForm.name}
                  onChange={(e) =>
                    setCategoryForm({ ...categoryForm, name: e.target.value })
                  }
                  placeholder="مثال: منتجات الألبان"
                  sx={{ borderRadius: 2 }}
                  error={!categoryForm.name && categoryForm.name !== ''}
                  helperText={
                    !categoryForm.name && categoryForm.name !== ''
                      ? 'هذا الحقل مطلوب'
                      : ''
                  }
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  color="text.primary"
                  sx={{ mb: 1 }}
                >
                  الاسم بالإنجليزية
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  value={categoryForm.nameEn}
                  onChange={(e) =>
                    setCategoryForm({ ...categoryForm, nameEn: e.target.value })
                  }
                  placeholder="Example: Dairy Products"
                  sx={{
                    borderRadius: 2,
                    backgroundColor: isDarkMode ? 'grey.800' : 'white',
                    '& .MuiInputBase-input': {
                      color: isDarkMode ? 'white' : 'inherit',
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={2}>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  color="text.primary"
                  sx={{ mb: 1 }}
                >
                  الأيقونة <span style={{ color: 'red' }}>*</span>
                </Typography>
                <Box sx={{ position: 'relative' }}>
                  <TextField
                    fullWidth
                    variant="outlined"
                    value={categoryForm.icon}
                    onChange={(e) =>
                      setCategoryForm({ ...categoryForm, icon: e.target.value })
                    }
                    placeholder="🥛"
                    sx={{
                      borderRadius: 2,
                      textAlign: 'center',
                      backgroundColor: isDarkMode ? 'grey.800' : 'white',
                      '& .MuiInputBase-input': {
                        color: isDarkMode ? 'white' : 'inherit',
                      },
                    }}
                    error={!categoryForm.icon && categoryForm.icon !== ''}
                    helperText={
                      !categoryForm.icon && categoryForm.icon !== ''
                        ? 'هذا الحقل مطلوب'
                        : ''
                    }
                    onClick={(e) => setCategoryIconAnchor(e.currentTarget)}
                    InputProps={{
                      style: { cursor: 'pointer' },
                      readOnly: false,
                    }}
                  />
                  <IconPicker
                    anchorEl={categoryIconAnchor}
                    onClose={() => setCategoryIconAnchor(null)}
                    onSelect={(icon) =>
                      setCategoryForm({ ...categoryForm, icon })
                    }
                  />
                </Box>
              </Grid>
              <Grid
                item
                xs={12}
                sm={2}
                sx={{ display: 'flex', alignItems: 'end' }}
              >
                <Button
                  fullWidth
                  variant="contained"
                  onClick={() => {
                    if (!categoryForm.name || !categoryForm.icon) {
                      setSnackbar({
                        open: true,
                        message: 'يرجى ملء الاسم والأيقونة على الأقل',
                        severity: 'error',
                      });
                      return;
                    }

                    const newCategory = {
                      id: Date.now().toString(),
                      name: categoryForm.name,
                      nameEn: categoryForm.nameEn || categoryForm.name,
                      icon: categoryForm.icon,
                      active: true,
                    };
                    const updatedCategories = [...saleCategories, newCategory];
                    setSaleCategories(updatedCategories);
                    localStorage.setItem(
                      'saleCategories',
                      JSON.stringify(updatedCategories)
                    );
                    setCategoryForm({ name: '', nameEn: '', icon: '' });
                    setSnackbar({
                      open: true,
                      message: 'تم إضافة التصنيف بنجاح',
                      severity: 'success',
                    });
                  }}
                  disabled={!categoryForm.name || !categoryForm.icon}
                  sx={{
                    height: '56px',
                    borderRadius: 2,
                    backgroundColor: 'primary.main',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                    '&:disabled': {
                      background: 'grey.300',
                      color: 'grey.500',
                    },
                  }}
                >
                  ➕ إضافة
                </Button>
              </Grid>
            </Grid>
          </Box>

          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            التصنيفات الموجودة
          </Typography>
          <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <strong>الأيقونة</strong>
                  </TableCell>
                  <TableCell>
                    <strong>الاسم</strong>
                  </TableCell>
                  <TableCell>
                    <strong>الاسم بالإنجليزية</strong>
                  </TableCell>
                  <TableCell>
                    <strong>الحالة</strong>
                  </TableCell>
                  <TableCell>
                    <strong>الإجراءات</strong>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {saleCategories.map((category) => (
                  <TableRow key={category.id}>
                    <TableCell sx={{ fontSize: '1.5rem' }}>
                      {category.icon}
                    </TableCell>
                    <TableCell>{category.name}</TableCell>
                    <TableCell>{category.nameEn}</TableCell>
                    <TableCell>
                      <Chip
                        label={category.active ? 'نشط' : 'غير نشط'}
                        color={category.active ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => {
                            const updatedCategories = saleCategories.map((c) =>
                              c.id === category.id
                                ? { ...c, active: !c.active }
                                : c
                            );
                            setSaleCategories(updatedCategories);
                            localStorage.setItem(
                              'saleCategories',
                              JSON.stringify(updatedCategories)
                            );
                          }}
                          color={category.active ? 'warning' : 'success'}
                        >
                          {category.active ? '⏸️' : '▶️'}
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => {
                            const updatedCategories = saleCategories.filter(
                              (c) => c.id !== category.id
                            );
                            setSaleCategories(updatedCategories);
                            localStorage.setItem(
                              'saleCategories',
                              JSON.stringify(updatedCategories)
                            );
                            setSnackbar({
                              open: true,
                              message: 'تم حذف التصنيف بنجاح',
                              severity: 'success',
                            });
                          }}
                          color="error"
                        >
                          🗑️
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={() => setCategoriesDialog(false)}
            variant="contained"
            color="primary"
          >
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>

      {/* Sale Methods Management Dialog */}
      <Dialog
        open={methodsDialog}
        onClose={() => setMethodsDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          },
        }}
      >
        <DialogTitle
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%)',
            color: 'white',
            py: 3,
            borderRadius: '12px 12px 0 0',
          }}
        >
          <Typography
            variant="h5"
            component="div"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 2,
            }}
          >
            💳 إدارة طرق البيع
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              إضافة طريقة بيع جديدة
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  color="text.primary"
                  sx={{ mb: 1 }}
                >
                  اسم طريقة البيع <span style={{ color: 'red' }}>*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  value={methodForm.name}
                  onChange={(e) =>
                    setMethodForm({ ...methodForm, name: e.target.value })
                  }
                  placeholder="مثال: بيع مباشر"
                  sx={{
                    borderRadius: 2,
                    backgroundColor: isDarkMode ? 'grey.800' : 'white',
                    '& .MuiInputBase-input': {
                      color: isDarkMode ? 'white' : 'inherit',
                    },
                  }}
                  error={!methodForm.name && methodForm.name !== ''}
                  helperText={
                    !methodForm.name && methodForm.name !== ''
                      ? 'هذا الحقل مطلوب'
                      : ''
                  }
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  color="text.primary"
                  sx={{ mb: 1 }}
                >
                  الاسم بالإنجليزية
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  value={methodForm.nameEn}
                  onChange={(e) =>
                    setMethodForm({ ...methodForm, nameEn: e.target.value })
                  }
                  placeholder="Example: Direct Sale"
                  sx={{
                    borderRadius: 2,
                    backgroundColor: isDarkMode ? 'grey.800' : 'white',
                    '& .MuiInputBase-input': {
                      color: isDarkMode ? 'white' : 'inherit',
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={2}>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  color="text.primary"
                  sx={{ mb: 1 }}
                >
                  الأيقونة <span style={{ color: 'red' }}>*</span>
                </Typography>
                <Box sx={{ position: 'relative' }}>
                  <TextField
                    fullWidth
                    variant="outlined"
                    value={methodForm.icon}
                    onChange={(e) =>
                      setMethodForm({ ...methodForm, icon: e.target.value })
                    }
                    placeholder="🛒"
                    sx={{
                      borderRadius: 2,
                      textAlign: 'center',
                      backgroundColor: isDarkMode ? 'grey.800' : 'white',
                      '& .MuiInputBase-input': {
                        color: isDarkMode ? 'white' : 'inherit',
                      },
                    }}
                    error={!methodForm.icon && methodForm.icon !== ''}
                    helperText={
                      !methodForm.icon && methodForm.icon !== ''
                        ? 'هذا الحقل مطلوب'
                        : ''
                    }
                    onClick={(e) => setMethodIconAnchor(e.currentTarget)}
                    InputProps={{
                      style: { cursor: 'pointer' },
                      readOnly: false,
                    }}
                  />
                  <IconPicker
                    anchorEl={methodIconAnchor}
                    onClose={() => setMethodIconAnchor(null)}
                    onSelect={(icon) => setMethodForm({ ...methodForm, icon })}
                  />
                </Box>
              </Grid>
              <Grid
                item
                xs={12}
                sm={2}
                sx={{ display: 'flex', alignItems: 'end' }}
              >
                <Button
                  fullWidth
                  variant="contained"
                  onClick={() => {
                    if (!methodForm.name || !methodForm.icon) {
                      setSnackbar({
                        open: true,
                        message: 'يرجى ملء الاسم والأيقونة على الأقل',
                        severity: 'error',
                      });
                      return;
                    }

                    const newMethod = {
                      id: Date.now().toString(),
                      name: methodForm.name,
                      nameEn: methodForm.nameEn || methodForm.name,
                      icon: methodForm.icon,
                      active: true,
                    };
                    const updatedMethods = [...saleMethods, newMethod];
                    setSaleMethods(updatedMethods);
                    localStorage.setItem(
                      'saleMethods',
                      JSON.stringify(updatedMethods)
                    );
                    setMethodForm({ name: '', nameEn: '', icon: '' });
                    setSnackbar({
                      open: true,
                      message: 'تم إضافة طريقة البيع بنجاح',
                      severity: 'success',
                    });
                  }}
                  disabled={!methodForm.name || !methodForm.icon}
                  sx={{
                    height: '56px',
                    borderRadius: 2,
                    backgroundColor: 'primary.main',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                    '&:disabled': {
                      background: 'grey.300',
                      color: 'grey.500',
                    },
                  }}
                >
                  ➕ إضافة
                </Button>
              </Grid>
            </Grid>
          </Box>

          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            طرق البيع الموجودة
          </Typography>
          <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <strong>الأيقونة</strong>
                  </TableCell>
                  <TableCell>
                    <strong>الاسم</strong>
                  </TableCell>
                  <TableCell>
                    <strong>الاسم بالإنجليزية</strong>
                  </TableCell>
                  <TableCell>
                    <strong>الحالة</strong>
                  </TableCell>
                  <TableCell>
                    <strong>الإجراءات</strong>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {saleMethods.map((method) => (
                  <TableRow key={method.id}>
                    <TableCell sx={{ fontSize: '1.5rem' }}>
                      {method.icon}
                    </TableCell>
                    <TableCell>{method.name}</TableCell>
                    <TableCell>{method.nameEn}</TableCell>
                    <TableCell>
                      <Chip
                        label={method.active ? 'نشط' : 'غير نشط'}
                        color={method.active ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => {
                            const updatedMethods = saleMethods.map((m) =>
                              m.id === method.id
                                ? { ...m, active: !m.active }
                                : m
                            );
                            setSaleMethods(updatedMethods);
                            localStorage.setItem(
                              'saleMethods',
                              JSON.stringify(updatedMethods)
                            );
                          }}
                          color={method.active ? 'warning' : 'success'}
                        >
                          {method.active ? '⏸️' : '▶️'}
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => {
                            const updatedMethods = saleMethods.filter(
                              (m) => m.id !== method.id
                            );
                            setSaleMethods(updatedMethods);
                            localStorage.setItem(
                              'saleMethods',
                              JSON.stringify(updatedMethods)
                            );
                            setSnackbar({
                              open: true,
                              message: 'تم حذف طريقة البيع بنجاح',
                              severity: 'success',
                            });
                          }}
                          color="error"
                        >
                          🗑️
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={() => setMethodsDialog(false)}
            variant="contained"
            color="primary"
          >
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Sales;

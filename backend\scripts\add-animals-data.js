const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addAnimalsData() {
  try {
    console.log('🐑 إضافة بيانات الحيوانات والمواليد...');

    // إضافة أنواع الحيوانات إذا لم تكن موجودة
    let sheepType, goatType;
    
    try {
      sheepType = await prisma.animalType.create({
        data: {
          name: 'sheep',
          nameAr: 'أغنام',
          nameEn: 'Sheep',
        },
      });
    } catch (error) {
      // إذا كان موجود، احصل عليه
      sheepType = await prisma.animalType.findFirst({
        where: { name: 'sheep' }
      });
    }

    try {
      goatType = await prisma.animalType.create({
        data: {
          name: 'goat',
          nameAr: 'ماعز',
          nameEn: 'Goat',
        },
      });
    } catch (error) {
      // إذا كان موجود، احصل عليه
      goatType = await prisma.animalType.findFirst({
        where: { name: 'goat' }
      });
    }

    // إضافة السلالات إذا لم تكن موجودة
    let najdiBreed, shamiBreed;
    
    try {
      najdiBreed = await prisma.breed.create({
        data: {
          name: 'najdi',
          nameAr: 'نجدي',
          nameEn: 'Najdi',
          animalTypeId: sheepType.id,
          description: 'سلالة أغنام نجدية محلية',
        },
      });
    } catch (error) {
      najdiBreed = await prisma.breed.findFirst({
        where: { name: 'najdi' }
      });
    }

    try {
      shamiBreed = await prisma.breed.create({
        data: {
          name: 'shami',
          nameAr: 'شامي',
          nameEn: 'Shami',
          animalTypeId: goatType.id,
          description: 'سلالة ماعز شامية',
        },
      });
    } catch (error) {
      shamiBreed = await prisma.breed.findFirst({
        where: { name: 'shami' }
      });
    }

    // إضافة حيوانات
    const animals = [];
    const animalData = [
      { type: 'sheep', breed: 'najdi', gender: 'FEMALE', category: 'MOTHER', name: 'نعجة 1' },
      { type: 'sheep', breed: 'najdi', gender: 'MALE', category: 'FATHER', name: 'كبش 1' },
      { type: 'goat', breed: 'shami', gender: 'FEMALE', category: 'MOTHER', name: 'عنزة 1' },
      { type: 'goat', breed: 'shami', gender: 'MALE', category: 'FATHER', name: 'تيس 1' },
      { type: 'sheep', breed: 'najdi', gender: 'FEMALE', category: 'NEWBORN', name: 'خروف صغير 1' },
      { type: 'sheep', breed: 'najdi', gender: 'MALE', category: 'NEWBORN', name: 'خروف صغير 2' },
      { type: 'goat', breed: 'shami', gender: 'FEMALE', category: 'NEWBORN', name: 'جدي صغير 1' },
      { type: 'goat', breed: 'shami', gender: 'MALE', category: 'FATTENING', name: 'جدي تسمين 1' },
      { type: 'sheep', breed: 'najdi', gender: 'FEMALE', category: 'FOR_SALE', name: 'نعجة للبيع 1' },
      { type: 'sheep', breed: 'najdi', gender: 'MALE', category: 'FATTENING', name: 'خروف تسمين 1' },
    ];

    for (let i = 0; i < animalData.length; i++) {
      const data = animalData[i];
      const animal = await prisma.animal.create({
        data: {
          internalId: `A${(i + 1).toString().padStart(3, '0')}`,
          tagNumber: `T${(i + 1).toString().padStart(3, '0')}`,
          animalTypeId: data.type === 'sheep' ? sheepType.id : goatType.id,
          breedId: data.breed === 'najdi' ? najdiBreed.id : shamiBreed.id,
          gender: data.gender,
          category: data.category,
          source: i < 4 ? 'PURCHASED' : 'INTERNAL',
          birthDate: new Date(2022 + (i % 3), (i % 12), 15),
          birthWeight: 3.0 + (i * 0.2),
          currentWeight: 25.0 + (i * 2.5),
          status: 'ALIVE',
          barnLocation: `حظيرة ${String.fromCharCode(65 + (i % 3))}`,
          notes: data.name,
        },
      });
      animals.push(animal);
    }

    // إنشاء دورة إنتاجية
    const reproductionCycle = await prisma.reproductionCycle.create({
      data: {
        cycleNumber: 1,
        animalTypeId: sheepType.id,
        motherId: animals[0].id, // النعجة الأولى
        fatherId: animals[1].id, // الكبش الأول
        pregnancyStart: new Date(2024, 0, 1),
        status: 'COMPLETED',
        totalCost: 1500,
        totalRevenue: 6000,
        notes: 'دورة إنتاجية ناجحة',
      },
    });

    // إضافة مواليد
    const births = [];
    for (let i = 1; i <= 7; i++) {
      const birth = await prisma.birth.create({
        data: {
          reproductionCycleId: reproductionCycle.id,
          motherId: animals[0].id,
          fatherId: animals[1].id,
          gender: i % 2 === 0 ? 'MALE' : 'FEMALE',
          birthDate: new Date(2024, i % 12, 15),
          birthWeight: 3.0 + (i * 0.1),
          birthType: i === 7 ? 'TWIN' : 'SINGLE',
          status: 'ALIVE',
          notes: `مولود رقم ${i}`,
        },
      });
      births.push(birth);
    }

    // إضافة علاجات
    const treatments = [];
    for (let i = 0; i < 12; i++) {
      const treatment = await prisma.treatment.create({
        data: {
          animalId: animals[i % animals.length].id,
          diseaseType: i % 3 === 0 ? 'تطعيم وقائي' : i % 3 === 1 ? 'علاج طفيليات' : 'فحص دوري',
          medicine: i % 3 === 0 ? 'لقاح الحمى القلاعية' : i % 3 === 1 ? 'مضاد طفيليات' : 'فيتامينات',
          dosage: `${2 + (i % 5)} مل`,
          date: new Date(2024, (i % 12), 15),
          administeredBy: i % 2 === 0 ? 'د. سالم أحمد' : 'د. محمد حسن',
          cost: 25 + (i * 5),
          notes: `علاج رقم ${i + 1}`,
        },
      });
      treatments.push(treatment);
    }

    // إضافة سجلات أوزان
    const weightRecords = [];
    for (let i = 0; i < 20; i++) {
      const weightRecord = await prisma.weightRecord.create({
        data: {
          animalId: animals[i % animals.length].id,
          weight: 25.0 + (i * 1.2),
          date: new Date(2024, (i % 12), 15),
          notes: `قياس وزن رقم ${i + 1}`,
        },
      });
      weightRecords.push(weightRecord);
    }

    console.log('✅ تم إضافة بيانات الحيوانات بنجاح!');
    console.log(`📊 الإحصائيات:`);
    console.log(`   - الحيوانات: ${animals.length}`);
    console.log(`   - المواليد: ${births.length}`);
    console.log(`   - العلاجات: ${treatments.length}`);
    console.log(`   - سجلات الأوزان: ${weightRecords.length}`);

    // عرض الإجمالي الحالي
    const totalCounts = await Promise.all([
      prisma.animal.count(),
      prisma.birth.count(),
      prisma.treatment.count(),
      prisma.weightRecord.count(),
    ]);

    console.log(`\n📈 الإجمالي الحالي:`);
    console.log(`   - إجمالي الحيوانات: ${totalCounts[0]}`);
    console.log(`   - إجمالي المواليد: ${totalCounts[1]}`);
    console.log(`   - إجمالي العلاجات: ${totalCounts[2]}`);
    console.log(`   - إجمالي سجلات الأوزان: ${totalCounts[3]}`);

  } catch (error) {
    console.error('❌ خطأ في إضافة بيانات الحيوانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addAnimalsData();

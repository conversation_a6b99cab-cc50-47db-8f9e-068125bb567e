import { PrismaClient } from '@prisma/client';
import express from 'express';
import multer from 'multer';
import path from 'path';
import * as XLSX from 'xlsx';

const prisma = new PrismaClient();
const router = express.Router();

// إعداد multer لرفع الملفات
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/backups');
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, '-')
      .slice(0, 19);
    const ext = path.extname(file.originalname);
    cb(null, `backup_${timestamp}${ext}`);
  },
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.xlsx', '.json'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم'));
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
});

// GET /api/backup/tables - الحصول على قائمة الجداول المتاحة
router.get('/tables', async (req, res) => {
  try {
    // الحصول على عدد السجلات من قاعدة البيانات الفعلية
    const [
      animalsCount,
      birthsCount,
      employeesCount,
      salesCount,
      purchasesCount,
      expensesCount,
      treatmentsCount,
      weightRecordsCount,
    ] = await Promise.all([
      prisma.animal.count(),
      prisma.birth.count(),
      prisma.employee.count(),
      prisma.sale.count(),
      prisma.purchase.count(),
      prisma.expense.count(),
      prisma.treatment.count(),
      prisma.weightRecord.count(),
    ]);

    const tables = [
      { key: 'animals', name: 'الحيوانات', recordCount: animalsCount },
      { key: 'births', name: 'المواليد', recordCount: birthsCount },
      { key: 'employees', name: 'الموظفين', recordCount: employeesCount },
      { key: 'sales', name: 'المبيعات', recordCount: salesCount },
      { key: 'purchases', name: 'المشتريات', recordCount: purchasesCount },
      { key: 'expenses', name: 'المصروفات', recordCount: expensesCount },
      { key: 'treatments', name: 'العلاجات', recordCount: treatmentsCount },
      {
        key: 'weight_records',
        name: 'سجلات الأوزان',
        recordCount: weightRecordsCount,
      },
    ];

    res.json({
      success: true,
      data: tables,
    });
  } catch (error) {
    console.error('Error fetching table counts:', error);
    res.status(500).json({
      success: false,
      error: 'فشل في جلب معلومات الجداول',
    });
  }
});

// GET /api/backup/export/:tables - تصدير الجداول المحددة
router.get('/export/:tables', async (req, res) => {
  try {
    const { tables } = req.params;
    const { format = 'excel' } = req.query;
    const tableKeys = tables.split(',');

    // التحقق من صحة الجداول
    const validTables = [
      'animals',
      'births',
      'employees',
      'sales',
      'purchases',
      'expenses',
      'treatments',
      'weight_records',
    ];
    const invalidTables = tableKeys.filter((key) => !validTables.includes(key));
    if (invalidTables.length > 0) {
      return res.status(400).json({
        success: false,
        error: `جداول غير موجودة: ${invalidTables.join(', ')}`,
      });
    }

    // إعداد البيانات للتصدير من قاعدة البيانات الفعلية
    const exportData: any = {};
    for (const key of tableKeys) {
      try {
        exportData[key] = await getTableData(key);
      } catch (error) {
        console.error(`Error fetching data for table ${key}:`, error);
        exportData[key] = []; // إرجاع مصفوفة فارغة في حالة الخطأ
      }
    }

    if (format === 'excel') {
      // تصدير Excel
      const workbook = XLSX.utils.book_new();

      tableKeys.forEach((key) => {
        const worksheet = XLSX.utils.json_to_sheet(exportData[key]);
        XLSX.utils.book_append_sheet(
          workbook,
          worksheet,
          getTableNameInArabic(key)
        );
      });

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );

      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      res.send(buffer);
      return;
    } else {
      // تصدير JSON
      res.setHeader('Content-Type', 'application/json');
      res.json(exportData);
      return;
    }
  } catch (error) {
    console.error('Export error:', error);
    res.status(500).json({
      success: false,
      error: 'فشل في تصدير البيانات',
    });
    return;
  }
});

// دالة للحصول على البيانات الفعلية من قاعدة البيانات
async function getTableData(tableKey: string) {
  switch (tableKey) {
    case 'animals':
      return await prisma.animal.findMany({
        include: {
          breed: true,
          animalType: true,
        },
      });
    case 'births':
      return await prisma.birth.findMany({
        include: {
          mother: true,
          father: true,
        },
      });
    case 'employees':
      return await prisma.employee.findMany();
    case 'sales':
      return await prisma.sale.findMany();
    case 'purchases':
      return await prisma.purchase.findMany();
    case 'expenses':
      return await prisma.expense.findMany();
    case 'treatments':
      return await prisma.treatment.findMany({
        include: {
          animal: true,
        },
      });
    case 'weight_records':
      return await prisma.weightRecord.findMany({
        include: {
          animal: true,
        },
      });
    default:
      throw new Error(`جدول غير مدعوم: ${tableKey}`);
  }
}

// دالة مساعدة للحصول على اسم الجدول بالعربية
function getTableNameInArabic(key: string): string {
  const names: { [key: string]: string } = {
    animals: 'الحيوانات',
    births: 'المواليد',
    employees: 'الموظفين',
    sales: 'المبيعات',
    purchases: 'المشتريات',
    expenses: 'المصروفات',
    treatments: 'العلاجات',
    weight_records: 'سجلات الأوزان',
  };
  return names[key] || key;
}

export default router;

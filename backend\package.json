{"name": "farm-management-backend", "version": "1.0.0", "description": "Farm Management System Backend API", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node src/seed.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["farm", "management", "livestock", "sheep", "goats"], "author": "Farm Management System", "license": "MIT", "dependencies": {"@prisma/client": "^6.8.2", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "prisma": "^6.8.2", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.29", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
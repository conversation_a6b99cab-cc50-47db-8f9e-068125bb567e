{"name": "farm-management-system", "version": "1.0.0", "description": "نظام متكامل لإدارة مزرعة تربية وإنتاج وتسمين الأغنام والماعز", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "db:setup": "cd backend && npm run db:generate && npm run db:push && npm run db:seed", "clean": "rimraf backend/dist frontend/dist backend/node_modules frontend/node_modules node_modules", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["farm", "management", "livestock", "sheep", "goats", "agriculture", "arabic", "bilingual"], "author": "Farm Management System Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/farm-management-system.git"}, "bugs": {"url": "https://github.com/your-username/farm-management-system/issues"}, "homepage": "https://github.com/your-username/farm-management-system#readme"}
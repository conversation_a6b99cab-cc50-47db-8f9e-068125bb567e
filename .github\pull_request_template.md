# 🔄 Pull Request

## 📋 وصف التغييرات
وصف موجز للتغييرات التي تم إجراؤها في هذا PR.

## 🎯 نوع التغيير
- [ ] 🐛 إصلاح خطأ (تغيير لا يكسر الوظائف الموجودة ويصلح مشكلة)
- [ ] ✨ ميزة جديدة (تغيير لا يكسر الوظائف الموجودة ويضيف وظيفة)
- [ ] 💥 تغيير كاسر (إصلاح أو ميزة تتسبب في عدم عمل الوظائف الموجودة كما هو متوقع)
- [ ] 📚 تحديث التوثيق
- [ ] 🎨 تحسين الكود (تنسيق، إعادة تسمية متغيرات، إلخ)
- [ ] ⚡ تحسين الأداء
- [ ] ✅ إضافة أو تحديث الاختبارات
- [ ] 🔧 تحديث أدوات البناء أو التبعيات

## 🔗 Issues ذات الصلة
يصلح # (رقم issue)
يغلق # (رقم issue)
مرتبط بـ # (رقم issue)

## 🧪 كيف تم اختبار هذا؟
وصف الاختبارات التي أجريتها للتحقق من تغييراتك.

- [ ] اختبار A
- [ ] اختبار B

**تكوين الاختبار**:
* إصدار Node.js:
* نظام التشغيل:
* المتصفح:

## 📸 لقطات الشاشة (إذا كان مناسباً)
أضف لقطات شاشة لتوضيح التغييرات في الواجهة.

## ✅ قائمة المراجعة
- [ ] الكود يتبع إرشادات الأسلوب لهذا المشروع
- [ ] أجريت مراجعة ذاتية للكود الخاص بي
- [ ] علقت على الكود، خاصة في المناطق صعبة الفهم
- [ ] أجريت التغييرات المقابلة على التوثيق
- [ ] تغييراتي لا تولد تحذيرات جديدة
- [ ] أضفت اختبارات تثبت أن إصلاحي فعال أو أن ميزتي تعمل
- [ ] الاختبارات الجديدة والموجودة تمر محلياً مع تغييراتي
- [ ] أي تغييرات تابعة تم دمجها ونشرها في الوحدات النهائية

## 🔄 التغييرات في قاعدة البيانات
- [ ] لا توجد تغييرات في قاعدة البيانات
- [ ] تمت إضافة migration جديدة
- [ ] تم تحديث schema موجود
- [ ] تمت إضافة بيانات أولية جديدة

إذا كانت هناك تغييرات في قاعدة البيانات، يرجى وصفها:

## 🚀 التأثير على الأداء
- [ ] لا يوجد تأثير على الأداء
- [ ] تحسين في الأداء
- [ ] تأثير سلبي محتمل (يرجى التوضيح)

## 🔒 اعتبارات الأمان
- [ ] لا توجد تغييرات أمنية
- [ ] تحسين في الأمان
- [ ] تغييرات تتطلب مراجعة أمنية

## 📝 ملاحظات للمراجعين
أي معلومات إضافية تريد أن يعرفها المراجعون.

## 🔄 خطوات النشر
إذا كانت هناك خطوات خاصة مطلوبة للنشر:

1. خطوة 1
2. خطوة 2
3. خطوة 3

## 📋 قائمة المراجعة النهائية
- [ ] تم اختبار التغييرات محلياً
- [ ] تم تحديث التوثيق
- [ ] تم تحديث CHANGELOG.md
- [ ] تم اختبار التوافق مع المتصفحات المختلفة
- [ ] تم اختبار الاستجابة للجوال

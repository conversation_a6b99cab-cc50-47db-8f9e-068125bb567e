const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Create Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    process.env.FRONTEND_URL
  ].filter(Boolean),
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Farm Management System API is running',
    timestamp: new Date().toISOString()
  });
});

// Basic API endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'نظام إدارة المزرعة يعمل بنجاح!',
    messageEn: 'Farm Management System is working successfully!',
    timestamp: new Date().toISOString()
  });
});

// Mock API endpoints for testing
app.get('/api/animals', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        tagNumber: 'SH001',
        name: 'نعجة رقم 1',
        type: 'sheep',
        breed: 'عواسي',
        gender: 'female',
        birthDate: '2023-01-15',
        weight: 45.5,
        status: 'healthy'
      },
      {
        id: '2',
        tagNumber: 'GT001',
        name: 'ماعز رقم 1',
        type: 'goat',
        breed: 'شامي',
        gender: 'male',
        birthDate: '2023-03-20',
        weight: 38.2,
        status: 'healthy'
      }
    ],
    total: 2
  });
});

app.get('/api/breeds', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: '1', name: 'عواسي', nameEn: 'Awassi', type: 'sheep' },
      { id: '2', name: 'شامي', nameEn: 'Shami', type: 'goat' },
      { id: '3', name: 'نجدي', nameEn: 'Najdi', type: 'sheep' }
    ]
  });
});

app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalAnimals: 125,
      totalSheep: 85,
      totalGoats: 40,
      pregnantAnimals: 15,
      newBorns: 8,
      totalRevenue: 45000,
      totalExpenses: 28000,
      netProfit: 17000
    }
  });
});

// Settings API endpoints
app.get('/api/settings/farm', (req, res) => {
  res.json({
    success: true,
    data: {
      farmName: 'مزرعة الأمل للأغنام والماعز',
      ownerName: 'أحمد محمد',
      location: 'الرياض، المملكة العربية السعودية',
      phone: '+966501234567',
      email: '<EMAIL>',
      currency: 'SAR',
      language: 'ar',
      timezone: 'Asia/Riyadh'
    }
  });
});

app.put('/api/settings/farm', (req, res) => {
  // Here you would save to database
  res.json({
    success: true,
    message: 'تم حفظ إعدادات المزرعة بنجاح'
  });
});

app.get('/api/settings/animal-types', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: '1', name: 'أغنام', nameEn: 'Sheep', active: true },
      { id: '2', name: 'ماعز', nameEn: 'Goats', active: true }
    ]
  });
});

app.get('/api/settings/breeds', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: '1', name: 'عواسي', nameEn: 'Awassi', type: 'sheep', active: true },
      { id: '2', name: 'نجدي', nameEn: 'Najdi', type: 'sheep', active: true },
      { id: '3', name: 'شامي', nameEn: 'Shami', type: 'goat', active: true },
      { id: '4', name: 'المحلي', nameEn: 'Local', type: 'goat', active: true }
    ]
  });
});

app.get('/api/settings/feed-types', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: '1', name: 'شعير', nameEn: 'Barley', category: 'grain', unit: 'kg', active: true },
      { id: '2', name: 'برسيم', nameEn: 'Alfalfa', category: 'forage', unit: 'kg', active: true },
      { id: '3', name: 'علف مركز', nameEn: 'Concentrate', category: 'concentrate', unit: 'kg', active: true },
      { id: '4', name: 'تبن', nameEn: 'Straw', category: 'roughage', unit: 'kg', active: true }
    ]
  });
});

app.get('/api/settings/treatment-types', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: '1', name: 'تحصين', nameEn: 'Vaccination', category: 'prevention', active: true },
      { id: '2', name: 'علاج مضاد حيوي', nameEn: 'Antibiotic', category: 'treatment', active: true },
      { id: '3', name: 'فيتامينات', nameEn: 'Vitamins', category: 'supplement', active: true },
      { id: '4', name: 'مضاد طفيليات', nameEn: 'Antiparasitic', category: 'treatment', active: true }
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The route ${req.originalUrl} does not exist`
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Farm Management System API is running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/test`);
});

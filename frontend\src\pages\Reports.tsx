import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useTranslation } from 'react-i18next';

const Reports: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        {t('reports')}
      </Typography>
      
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          صفحة التقارير قيد التطوير
        </Typography>
        <Typography variant="body1" sx={{ mt: 2 }}>
          ستتضمن هذه الصفحة التقارير المالية وتقارير الإنتاج والأداء
        </Typography>
      </Paper>
    </Box>
  );
};

export default Reports;

const express = require('express');
const multer = require('multer');
const XLSX = require('xlsx');
const fs = require('fs').promises;
const path = require('path');

const router = express.Router();

// إعداد multer لرفع الملفات
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/backups');
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const ext = path.extname(file.originalname);
    cb(null, `backup_${timestamp}${ext}`);
  }
});

const upload = multer({ 
  storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.xlsx', '.json'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم'));
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB
  }
});

// بيانات وهمية للاختبار
const mockData = {
  animals: [
    {
      id: '1',
      internalId: 'A001',
      tagNumber: 'T001',
      animalType: 'أغنام',
      breed: 'نجدي',
      gender: 'أنثى',
      category: 'أم',
      birthDate: '2022-03-15',
      currentWeight: 45.5,
      status: 'نشط',
      barnLocation: 'حظيرة أ',
      createdAt: new Date().toISOString(),
    },
    {
      id: '2',
      internalId: 'A002',
      tagNumber: 'T002',
      animalType: 'ماعز',
      breed: 'شامي',
      gender: 'ذكر',
      category: 'فحل',
      birthDate: '2021-08-20',
      currentWeight: 55.2,
      status: 'نشط',
      barnLocation: 'حظيرة ب',
      createdAt: new Date().toISOString(),
    },
  ],
  births: [
    {
      id: '1',
      motherId: 'A001',
      fatherId: 'A002',
      birthDate: '2024-01-15',
      birthType: 'ولادة طبيعية',
      numberOfOffspring: 2,
      status: 'موجود',
      season: 'الشتاء',
      createdAt: new Date().toISOString(),
    },
  ],
  employees: [
    {
      id: '1',
      name: 'أحمد محمد',
      idNumber: '1234567890',
      position: 'مدير المزرعة',
      monthlySalary: 8000,
      hireDate: '2023-01-01',
      status: 'نشط',
      phone: '0501234567',
      createdAt: new Date().toISOString(),
    },
  ],
  sales: [
    {
      id: '1',
      saleDate: '2024-11-15',
      saleType: 'جملة',
      totalPrice: 15000,
      customerName: 'شركة التجارة المتقدمة',
      paymentMethod: 'تحويل بنكي',
      notes: 'بيع 10 رؤوس أغنام',
      createdAt: new Date().toISOString(),
    },
  ],
  purchases: [
    {
      id: '1',
      type: 'أعلاف',
      description: 'علف مركز للأغنام',
      quantity: 100,
      unitPrice: 45,
      totalPrice: 4500,
      supplier: 'شركة الأعلاف المتقدمة',
      purchaseDate: '2024-11-01',
      createdAt: new Date().toISOString(),
    },
  ],
  expenses: [
    {
      id: '1',
      description: 'فاتورة كهرباء',
      category: 'كهرباء',
      amount: 1200,
      date: '2024-11-01',
      paymentMethod: 'نقدي',
      createdAt: new Date().toISOString(),
    },
  ],
  treatments: [
    {
      id: '1',
      animalId: 'A001',
      diseaseType: 'تطعيم وقائي',
      medicine: 'لقاح الحمى القلاعية',
      dosage: '2 مل',
      treatmentDate: '2024-10-15',
      veterinarian: 'د. سالم أحمد',
      cost: 25,
      createdAt: new Date().toISOString(),
    },
  ],
  weight_records: [
    {
      id: '1',
      animalId: 'A001',
      weight: 45.5,
      date: '2024-11-01',
      notes: 'وزن طبيعي',
      createdAt: new Date().toISOString(),
    },
  ],
};

// GET /api/backup/tables - الحصول على قائمة الجداول المتاحة
router.get('/tables', (req, res) => {
  const tables = Object.keys(mockData).map(key => ({
    key,
    name: getTableNameInArabic(key),
    recordCount: mockData[key].length,
  }));

  res.json({
    success: true,
    data: tables,
  });
});

// GET /api/backup/export/:tables - تصدير الجداول المحددة
router.get('/export/:tables', async (req, res) => {
  try {
    const { tables } = req.params;
    const { format = 'excel' } = req.query;
    const tableKeys = tables.split(',');

    // التحقق من صحة الجداول
    const invalidTables = tableKeys.filter(key => !mockData[key]);
    if (invalidTables.length > 0) {
      return res.status(400).json({
        success: false,
        error: `جداول غير موجودة: ${invalidTables.join(', ')}`,
      });
    }

    // إعداد البيانات للتصدير
    const exportData = {};
    tableKeys.forEach(key => {
      exportData[key] = mockData[key];
    });

    if (format === 'excel') {
      // تصدير Excel
      const workbook = XLSX.utils.book_new();
      
      tableKeys.forEach(key => {
        const worksheet = XLSX.utils.json_to_sheet(mockData[key]);
        XLSX.utils.book_append_sheet(workbook, worksheet, key);
      });

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const filename = `نسخة_احتياطية_${timestamp}.xlsx`;
      
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      res.send(buffer);
    } else {
      // تصدير JSON
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const filename = `نسخة_احتياطية_${timestamp}.json`;
      
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.json(exportData);
    }
  } catch (error) {
    console.error('Export error:', error);
    res.status(500).json({
      success: false,
      error: 'فشل في تصدير البيانات',
    });
  }
});

// POST /api/backup/import - استيراد البيانات
router.post('/import', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'لم يتم رفع أي ملف',
      });
    }

    const { replaceExisting = false, tableName } = req.body;
    const filePath = req.file.path;
    const fileExt = path.extname(req.file.originalname).toLowerCase();

    let importedData = [];

    if (fileExt === '.xlsx') {
      // قراءة ملف Excel
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      importedData = XLSX.utils.sheet_to_json(worksheet);
    } else if (fileExt === '.json') {
      // قراءة ملف JSON
      const fileContent = await fs.readFile(filePath, 'utf8');
      const jsonData = JSON.parse(fileContent);
      importedData = Array.isArray(jsonData) ? jsonData : [jsonData];
    }

    // التحقق من صحة البيانات
    const validation = validateImportData(importedData);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: validation.errors.join(', '),
      });
    }

    // محاكاة عملية الاستيراد
    console.log(`Importing ${importedData.length} records to table: ${tableName}`);

    // حذف الملف المؤقت
    await fs.unlink(filePath);

    res.json({
      success: true,
      recordsImported: importedData.length,
      message: `تم استيراد ${importedData.length} سجل بنجاح`,
    });
  } catch (error) {
    console.error('Import error:', error);
    
    // حذف الملف المؤقت في حالة الخطأ
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Error deleting temp file:', unlinkError);
      }
    }

    res.status(500).json({
      success: false,
      error: 'فشل في استيراد البيانات',
    });
  }
});

// دالة مساعدة للحصول على اسم الجدول بالعربية
function getTableNameInArabic(key) {
  const names = {
    animals: 'الحيوانات',
    births: 'المواليد',
    employees: 'الموظفين',
    sales: 'المبيعات',
    purchases: 'المشتريات',
    expenses: 'المصروفات',
    treatments: 'العلاجات',
    weight_records: 'سجلات الأوزان',
  };
  return names[key] || key;
}

// دالة التحقق من صحة البيانات المستوردة
function validateImportData(data) {
  const errors = [];
  const warnings = [];

  if (!Array.isArray(data) || data.length === 0) {
    errors.push('الملف فارغ أو لا يحتوي على بيانات صالحة');
    return { isValid: false, errors, warnings };
  }

  // التحقق من وجود الحقول المطلوبة
  const firstRecord = data[0];
  if (!firstRecord.id && !firstRecord.ID) {
    warnings.push('لا يوجد حقل ID في البيانات');
  }

  // التحقق من تكرار المعرفات
  const ids = data.map(record => record.id || record.ID).filter(Boolean);
  const uniqueIds = new Set(ids);
  if (ids.length !== uniqueIds.size) {
    errors.push('يوجد معرفات مكررة في البيانات');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

module.exports = router;
